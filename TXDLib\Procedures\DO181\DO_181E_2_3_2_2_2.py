# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             RF Peak Power Output, Section *******.2.
             
             Step1: ATCRBS Power:Interrogate the transponder with a standard Mode A interrogation.
             Use a 14 pulse (7777) reply group. Determine that the power output 
             meets the requirements of *******.
             
             Step2: Short ModeS Power: Repeat with a standard ModeA/ModeS All Call.  Determine the reply Pulse Power for both
             types.
             
             Step3: Long Modes S Power: Repeat with 16 Mode S interrogations per
             second and coding for which a Long (112bit) reply is required.
             
             Step4: Extended Long Mode S Power: repeat step 3, stimulating the 
             maximum rate of long replies for which the transponder is required
             
             Note: Section ******* Peak Output Power should have a Minimum of 
             21dBW (125W) and a Maximum of 27dBW (500W).
             
INPUTS:      RM,ATC,PwrMeter,Path Loss
OUTPUTS:     'PwrLvl' array of Power Levels, one for each step above.


HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
10/2023      CS     Updates to Power Meter settings and clean up results
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import B4500CPwrMeter


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def pw_init(pw,mode):
    """ Initializes Power Meter to pulse mode, sets timebase, sets trigger""" 
    
    ### PUT METER IN PULSE MODE
    pw.autoset()         #Initialize to defaults
    time.sleep(5)
    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    time.sleep(.3)
    pw.setCalculateUnits('dBm')
    time.sleep(.3)
    pw.setCalculate1_on('ON')
    time.sleep(.3)
    #Trigger
    pw.basicWrite("TRIGger:POSition LEFT")
    time.sleep(.3)
    pw.basicWrite("TRIGger:SOURce CH1")
    time.sleep(.3)   
    pw.basicWrite("TRIGger:SLOPe POS")
    time.sleep(.3)
    pw.basicWrite("TRIGger:MODE NORMAL")  #Importantae!
    time.sleep(.3)
    pw.basicWrite("TRIGger:LEV 48.0")    #Importantae!
    time.sleep(.3)
    pw.basicWrite("TRIGger:HOLDoff 30e-6")
    time.sleep(.3)
    pw.basicWrite("DISPlay:TRACe1:VSCALE 10.00")
    time.sleep(.3)
    pw.basicWrite("DISPlay:TRACe1:VCENTer 21.71")
    time.sleep(.3)
    pw.basicWrite("SENSe1:AVERage 1")
    time.sleep(.3)
    #pw.basicWrite("SENSe2:AVERage 1")
    #pw.basicWrite("SENSe3:AVERage 1")
    #pw.basicWrite("SENSe4:AVERage 1")
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(1)
        
    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    if (mode == 'ModeA'):
        TimeBase = '10e-6'                 #Importantae!
        time.sleep(1)
        pw.setTimeBase(TimeBase)
        time.sleep(1)
    else:
        TimeBase = '10e-6'                 #Importantae!
        time.sleep(1)
        pw.setTimeBase(TimeBase)
        time.sleep(1)
       
    print("TimeBase-Pulse: ",pw.getTimeBase())
    print("TSPAN: ",pw.basicQuery("DISPlay:TSPAN?"))
    pw.setFrequency1('1.090e9')
    time.sleep(1)
      

def find_pulses(pw,title="PowerMeter (dBm vs time)"):
    """ using Power Meter, finds peaks, gets stats for each pulse, and plots. Returns string with
    pulse parameters and integer with number of pulses. """
    pw.basicWrite("ABORt")
    time.sleep(0.3)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(1)
    pw.basicWrite("INITiate:IMMediate")
    time.sleep(5)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(0.3)
    nPulse = pw.findpeaks('48')      #actual level for max in dBm
    pwr = ""
    for i in range(nPulse):
        print("\n\nPULSE : ",i)
        pw.setpulsepositions(i)        # set markers for pulse i
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
    #plots the results
    #pw.plotpeaks('-20',title)            #comment this statment when running from TestStand
    return pwr, nPulse
    
    
##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_2_2(rm,atc,pwrmeter,PathLoss):
    """ DO-181E, RF Peak Output Power: Sect *******.2 """
    
    rm.logMessage(2,"*** DO-181E, RF Peak Output Power: Sect *******.2 ***\r\n")
    
    
    #Initial Pulse Power Levels
    PwrLvl = [0.0,0.0,0.0,0.0]                                 #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
    time.sleep(10)
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    time.sleep(1)
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    time.sleep(5)
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    time.sleep(1)
    
    #Turn ON RF
    atc.gwrite(":ATC:XPDR:RF ON")   
    time.sleep(15)
    atc.waitforstatus()
    time.sleep(1) 
    #initialize powermeter
    pw_init(pwrmeter,'ModeA')
    
    #Step1:Measure Pulse Power -- Mode A  
    val = 0.0
    time.sleep(10)
    p_str, npulse = find_pulses(pwrmeter) 
    if npulse > 0:
        time.sleep(1)
        #get first pulse power
        p_str = p_str.split(',')
        val = float(p_str[3]) 
    else:
        rm.logMessage(3,"Test_2_3_2_2_2 Step1 - Error, No Pulses")   
    
    #Step 1 Power Level    
    PwrLvl[0] = val #dBm
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    time.sleep(1)
   
    #Set Up Transponder -- MODE A/MODE S All Call
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    time.sleep(10)
   
    #Turn ON RF
    atc.gwrite(":ATC:XPDR:RF ON")   
    time.sleep(10)
    atc.waitforstatus()
    time.sleep(1)
    #initialize powermeter
    pw_init(pwrmeter,'ModeS')
    time.sleep(5)
         
    #Step2:Measure Pulse Power -- Mode A/Mode S All Call   
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter)
    if npulse > 0:
        #get first pulse power
        time.sleep(1)
        p_str = p_str.split(',')
        val = float(p_str[3]) 
    else:
        rm.logMessage(3,"Test_2_3_2_2_2 Step2 - Error, No Pulses")   

   
    #Step 2 Power Level
    PwrLvl[1] = val
 
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
 

    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    time.sleep(10)

    #Turn ON RF
    atc.gwrite(":ATC:XPDR:RF ON")   
    time.sleep(15)
    atc.waitforstatus()
    time.sleep(1)  
    #initialize powermeter
    pw_init(pwrmeter,'ModeS')
    time.sleep(5) 
    #Step3:Measure Pulse Power -- Mode S  
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter)
    if npulse > 0:
        #get first pulse power
        time.sleep(1)
        p_str = p_str.split(',')
        val = float(p_str[3]) 
    else:
        rm.logMessage(3,"Test_2_3_2_2_2 Step3 - Error, No Pulses")   
       
    #Step 3 Power Level
    PwrLvl[2] = val
 
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
      
 
    #Set Up Transponder -- MODE S Extended
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28800000000004") #Mode S Message DF4, RR=16, Adrs000004, get DF21,112Bit reply
    time.sleep(10)
    
    #Turn ON RF
    atc.gwrite(":ATC:XPDR:RF ON")   
    time.sleep(10)
    atc.waitforstatus()
    time.sleep(1)
    #initialize powermeter
    pw_init(pwrmeter,'ModeS')
    time.sleep(5)
    #Step4:Measure Pulse Power -- Mode S Extended   
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter)
    if npulse > 0:
        time.sleep(1)
        #get first pulse power
        p_str = p_str.split(',')
        val = float(p_str[3]) 
    else:
        rm.logMessage(3,"Test_2_3_2_2_2 Step4 - Error, No Pulses")   
        
    #Step 4 Power Level
    PwrLvl[3] = val
    
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Adjust Power Levels by Path Loss
    #PwrLvl[0] = PwrLvl[0] + PathLoss
    #PwrLvl[1] = PwrLvl[1] + PathLoss
    #PwrLvl[2] = PwrLvl[2] + PathLoss
    #PwrLvl[3] = PwrLvl[3] + PathLoss

    rm.logMessage(0,"Test_2_3_2_2_2 - Done: " + str(PwrLvl))        
    rm.logMessage(2,"Done, closing session")
    
    return PwrLvl
    

##############################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

     #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    pwr_obj = B4500CPwrMeter(rm)
    pwr_obj.Reset()
    
    #Power for Both Antennas
    res = Test_2_3_2_2_2(rm,atc_obj,pwr_obj,-52.0)
    
    pwr_obj.close()
    atc_obj.close()




