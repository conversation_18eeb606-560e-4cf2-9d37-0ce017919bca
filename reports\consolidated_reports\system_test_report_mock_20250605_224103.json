{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T22:41:03.314322", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 35, "passed": 17, "failed": 18, "errors": 0, "timeouts": 0, "success_rate": 48.57142857142857, "total_execution_time": 1800.3596994876862, "start_time": "2025-06-05T22:11:02.953727", "end_time": "2025-06-05T22:41:03.313426"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "PASSED", "return_code": 0, "execution_time": 115.32443428039551, "start_time": "2025-06-05T22:11:02.954744", "end_time": "2025-06-05T22:12:58.279160", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1a - MODE A ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.0926143816733266\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.57,-18.23,-17.47,-20.68\n['-20.57', '-18.23', '-17.47', '-20.68']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1a - Top Ant: -20.68\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.040603076533875\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply Delay Initial: 3.040603076533875\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply Delay Final: 3.040603076533875\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1b Bot Ant\nTimeBase-Pulse:  0.1056760736562869\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.48,-26.13,-15.32,-23.27\n['-22.48', '-26.13', '-15.32', '-23.27']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1b - Bot Ant: -23.27\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1543166497549384\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Initial: 3.1543166497549384\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 3.1543166497549384\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09634438624175995\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.49,-20.34,-14.84,-18.85\n['-20.49', '-20.34', '-14.84', '-18.85']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2a - Top Ant: -18.85\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.8069350908276927\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Initial: 2.8069350908276927\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Final: 2.8069350908276927\nTimeBase-Pulse:  0.09802877452985803\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.13,-25.96,-22.87,-22.35\n['-22.13', '-25.96', '-22.87', '-22.35']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2b - Bot Ant: -22.35\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0069859293702303\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay 2 Initial: 3.0069859293702303\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 3.0069859293702303\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Power's: -20.680000,-23.270000,-18.850000,-22.350000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: ReplyDelay's: 3.040603,3.154317,2.806935,3.006986\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "PASSED", "return_code": 0, "execution_time": 87.02793717384338, "start_time": "2025-06-05T22:12:58.279794", "end_time": "2025-06-05T22:14:25.307732", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1a - MODE A ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09990120003955935\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -23.41,-23.40,-17.70,-24.26\n['-23.41', '-23.40', '-17.70', '-24.26']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1a - Top Ant: -24.26\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.900444590607648\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply Delay Initial: 2.900444590607648\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply Delay Final: 2.900444590607648\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1b Bot Ant\nTimeBase-Pulse:  0.0904802038323206\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -15.60,-26.16,-17.40,-18.15\n['-15.60', '-26.16', '-17.40', '-18.15']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1b - Bot Ant: -18.15\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1026408515380512\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Initial: 3.1026408515380512\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 3.1026408515380512\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09364996161801539\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.44,-19.97,-20.48,-16.15\n['-22.44', '-19.97', '-20.48', '-16.15']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2a - Top Ant: -16.15\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.8668666647976293\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Initial: 2.8668666647976293\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Final: 2.8668666647976293\nTimeBase-Pulse:  0.10539714341731948\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -21.06,-25.37,-22.53,-20.19\n['-21.06', '-25.37', '-22.53', '-20.19']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2b - Bot Ant: -20.19\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.933125341376311\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay 2 Initial: 2.933125341376311\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 2.933125341376311\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Power's: -24.260000,-18.150000,-16.150000,-20.190000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: ReplyDelay's: 2.900445,3.102641,2.866867,2.933125\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 2.886066436767578, "start_time": "2025-06-05T22:14:25.308603", "end_time": "2025-06-05T22:14:28.194669", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1b - MODE CS ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 376, in <module>\n    res = Test_2_3_2_10_Step1b(rm,atc_obj,pwr_obj,rf_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 162, in Test_2_3_2_10_Step1b\n    atc.transponderModeCS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeCS'. Did you mean: 'transponderModeS'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "PASSED", "return_code": 0, "execution_time": 84.00833916664124, "start_time": "2025-06-05T22:14:28.195650", "end_time": "2025-06-05T22:15:52.203990", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1b - MODE S ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10003000668613501\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.89,-25.12,-22.89,-21.02\n['-20.89', '-25.12', '-22.89', '-21.02']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1a - Top Ant: -21.02\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.9518750144924786\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1b Bot Ant\nTimeBase-Pulse:  0.10125465167208904\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -17.66,-26.56,-18.61,-19.99\n['-17.66', '-26.56', '-18.61', '-19.99']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1b - Bot Ant: -19.99\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.970135300379079\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09616456633619488\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -15.84,-25.83,-18.45,-20.09\n['-15.84', '-25.83', '-18.45', '-20.09']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2a - Top Ant: -20.09\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.878481917000985\nTimeBase-Pulse:  0.09198518393322896\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.80,-18.12,-19.88,-20.08\n['-22.80', '-18.12', '-19.88', '-20.08']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2b - Bot Ant: -20.08\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0997763537539287\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Power's: -21.020000,-19.990000,-20.090000,-20.080000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: ReplyDelay's: 2.951875,2.970135,2.878482,3.099776\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 6.3811657428741455, "start_time": "2025-06-05T22:15:52.204760", "end_time": "2025-06-05T22:15:58.585925", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 258, in <module>\n    res = Test_2_3_2_10_Step2a(rm,atc_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 96, in Test_2_3_2_10_Step2a\n    atc.transponderModeC()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeC'. Did you mean: 'transponderMode'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 2.376152753829956, "start_time": "2025-06-05T22:15:58.587341", "end_time": "2025-06-05T22:16:00.963494", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 239, in <module>\n    res = Test_2_3_2_10_Step2a(rm,atc_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 93, in Test_2_3_2_10_Step2a\n    atc.transponderModeC()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeC'. Did you mean: 'transponderMode'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "PASSED", "return_code": 0, "execution_time": 69.57480096817017, "start_time": "2025-06-05T22:16:00.964657", "end_time": "2025-06-05T22:17:10.539458", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2b ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestA SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.68670003125895, 92.84760372907448, 96.47724030812608, 95.0545142483888]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestB SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.9228346704916, 96.06087241666373, 97.31801042403644, 97.42882761448863]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestC SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.63538623744569, 96.09908909666507, 95.94917962868213, 96.18782498964556]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestD SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.39863980784506, 96.31030404000573, 97.25813619940796, 97.3765964042542]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test A: Reply Rates: 94.686700,92.847604\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test B: Reply Rates: 94.922835,96.060872\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test C: Reply Rates: 93.635386,96.099089\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test D: Reply Rates: 95.398640,96.310304\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 2.373288631439209, "start_time": "2025-06-05T22:17:10.540210", "end_time": "2025-06-05T22:17:12.913499", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step3 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 254, in <module>\n    res = Test_2_3_2_10_Step3(rm,atc_obj,12.75,12.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 89, in Test_2_3_2_10_Step3\n    atc.transponderModeC()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeC'. Did you mean: 'transponderMode'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.3946242332458496, "start_time": "2025-06-05T22:17:12.914755", "end_time": "2025-06-05T22:17:13.309379", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 152.57844829559326, "start_time": "2025-06-05T22:17:13.310584", "end_time": "2025-06-05T22:19:45.889034", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1 Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Cableloss passed to the script: -12.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.92932350697492, 95.17854083933581, 94.41257551431893, 96.47403904068892]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.83825680240577, 94.1116386797405, 97.47925808083782, 95.20346805467749]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.65951620442601, 93.8730850964894, 96.45444075701717, 94.94647587256632]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.0460589705524, 95.4884317333238, 94.56373983654711, 95.34790944786793]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.80283961179565, 94.11712270367045, 98.00219560453075, 95.01066784544983]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.8694122774997, 96.09788811545825, 97.46717522368456, 94.34946540201209]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.8451956810189, 94.9979813239354, 95.89750027017604, 96.12556613733767]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.86516648938158, 94.22483586300343, 94.85292885693218, 95.3209841761841]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.89212217423116, 95.28166419966308, 97.55610072116168, 96.12156025234628]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.01869796640925, 95.80926482245523, 97.99519162071395, 93.50648571268192]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.65051564306832, 96.51390542760753, 96.42605361662281, 94.99333996949379]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.66440889156496, 93.60096811844186, 94.27672833572326, 94.82246442131546]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.96846334733642, 96.34295463086416, 96.23722204767715, 95.61258054594805]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.59117787486636, 94.87660841172426, 94.29075434699078, 95.06667552279109]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.24735685661648, 95.7341880334485, 95.56716757802864, 93.55773315047043]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 0, Frequency: 1029.80, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1029.80 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.67968152133656, 93.7861026128681, 94.74223859838519, 96.09483251860041]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.81224910055332, 96.32870335261597, 94.74441144245871, 94.78107480231242]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.47833291242401, 94.56508555394538, 94.6622316661566, 94.14741922185351]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.70964643385463, 94.8831482038735, 94.6608856530344, 95.04809040822568]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.72943422397455, 95.92806409454782, 97.15417723120477, 94.74259049915614]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.3082223719766, 95.70389564334423, 94.89361572635767, 96.80874305917928]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.56590390284039, 95.09577459546655, 96.14619016129488, 96.82417553276662]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.02961716156413, 93.06768060123451, 94.78947658545337, 95.34850764707794]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.72389318831536, 93.49792304323788, 94.10797573137359, 96.22304480861209]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.7346276310641, 95.03831274907398, 96.93231913935048, 95.41939606332436]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.69292425969994, 92.885120238131, 94.59138648320099, 96.13993304235485]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.38391161225928, 96.37307555460976, 95.3870145187183, 97.17466828430433]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.40302219733084, 95.61025281084643, 97.21564121141384, 93.57389920883044]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.07789825228058, 95.49836141174178, 95.0038932202568, 97.21474072519152]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.01717394489502, 96.00303161997104, 98.00839153334209, 93.66843560551108]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 1, Frequency: 1030.00, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1030.00 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.60224915321619, 92.92559205786667, 97.68196650080769, 96.3260838273884]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.8080573473474, 95.3129231195538, 97.16231979090304, 96.40981366049289]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.66677659578215, 96.0588559009403, 97.15004179103028, 93.86358530679632]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.64245993128975, 96.50818640666598, 96.12412350691244, 94.20066477882727]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.41003016481444, 94.37026592635432, 94.29283572311948, 96.17577932370605]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.68589529305461, 95.19205999750945, 96.90104316536028, 95.85090478009347]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.09472047477976, 94.6529019642266, 95.04920306685447, 95.46143598298225]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.25160512265833, 93.3192052362529, 95.97145795642206, 97.39960046245115]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.59051653569873, 96.21776440346186, 95.83807779479156, 96.58913994378763]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.08818692615759, 93.29060576505326, 94.8497788374765, 96.04042878447336]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.07659041692722, 94.71241513789255, 95.50010012289125, 95.53152470963505]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.59817882124608, 93.56007878430367, 95.02341395357756, 97.36645045555125]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.88397758691579, 92.98495260457338, 98.00952362724703, 93.74897658795119]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.21988896883235, 95.2517052633361, 95.39977293695574, 94.91038921203938]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.20384581601411, 93.52960302292144, 96.92910938408082, 96.05915474511329]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 2, Frequency: 1030.20, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1030.20 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1 - [-63.0, -63.0, -63.0]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 142, in <module>\n    rm.close()\n    ^^^^^^^^\nAttributeError: 'ate_rm' object has no attribute 'close'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 2.3768835067749023, "start_time": "2025-06-05T22:19:45.890265", "end_time": "2025-06-05T22:19:48.267148", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 134, in <module>\n    res = Test_2_3_2_1_Step2(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 59, in Test_2_3_2_1_Step2\n    atc.transponderModeC()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeC'. Did you mean: 'transponderMode'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "PASSED", "return_code": 0, "execution_time": 137.66890573501587, "start_time": "2025-06-05T22:19:48.268542", "end_time": "2025-06-05T22:22:05.937459", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 3 ***\n\n\n['-85.0', '-75.0', '-65.0', '-55.0', '-45.0', '-33.0']\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Test_2_3_2_1_Step3 ModeA - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.07325983022994, 93.56476386490138, 94.4055981094692, 95.356873161466]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -85.0, ReplyRate 93.564764\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.52097014074488, 94.69667274214615, 94.18072046810074, 96.66528968226703]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -75.0, ReplyRate 94.696673\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.59172382903314, 93.81027810534432, 98.06952027066474, 94.59657105921629]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -65.0, ReplyRate 93.810278\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -55.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -55.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.10242986731579, 96.18141159445055, 96.07657769224984, 95.90375721705655]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -55.0, ReplyRate 96.181412\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -45.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -45.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.06869280618925, 94.97278904708705, 97.27564061746341, 94.62289022459935]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -45.0, ReplyRate 94.972789\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.00766512576735, 93.76068796624442, 94.2978635936952, 96.49161559374139]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -33.0, ReplyRate 93.760688\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Test_2_3_2_1_Step3 End: [93.56476386490138, 94.69667274214615, 93.81027810534432, 96.18141159445055, 94.97278904708705, 93.76068796624442, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.3693535327911377, "start_time": "2025-06-05T22:22:05.937985", "end_time": "2025-06-05T22:22:08.307339", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 4 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 107, in <module>\n    res = Test_2_3_2_1_Step4(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 58, in Test_2_3_2_1_Step4\n    atc.transponderModeCS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeCS'. Did you mean: 'transponderModeS'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "PASSED", "return_code": 0, "execution_time": 72.70078611373901, "start_time": "2025-06-05T22:22:08.308488", "end_time": "2025-06-05T22:23:21.009274", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 5 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Test_2_3_2_1_Step5 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.80329211797826, 96.44224793506818, 97.00763018408641, 95.74636534480278]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.10203194621083, 95.21942258563688, 97.47632454888061, 95.94662183494347]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.82146159030454, 96.01049663618205, 95.07994437616364, 96.93536087574144]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.77629775480654, 93.91022439934714, 94.15295246608807, 96.67098871989059]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.15192713402239, 93.72568719190224, 94.53755278508925, 96.46795900246529]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.81073517278016, 93.46205391611079, 97.4252778673432, 95.31648151975804]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.35091032874216, 95.65227123950123, 97.04238189148577, 97.05271730655693]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.57235428092952, 94.35368028974337, 94.17692465496609, 94.65661430751213]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.0096469877197, 93.36886668141604, 98.02628456124907, 93.93428720421198]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.28506869392464, 94.57564640338079, 94.28195189518736, 97.12385580614631]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.7004754919527, 95.86163802094585, 96.01766856853334, 94.71612625755144]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.4287014661625, 95.90420200325667, 97.10702310562503, 94.45189007962263]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.15517821977119, 95.97885304838272, 94.24053467832934, 95.90234458501168]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.72670860774778, 95.31551755073146, 96.41919719764583, 94.61261478380226]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.496832048041, 96.38954064984621, 97.00753039577609, 96.07705947451343]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Test_2_3_2_1_Step5 - Done: -63.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "PASSED", "return_code": 0, "execution_time": 107.66163372993469, "start_time": "2025-06-05T22:23:21.010057", "end_time": "2025-06-05T22:25:08.671693", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 6 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Test_2_3_2_1_Step6 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.99804458414349, 95.36851025302067, 97.17785123149132, 94.25701807132664]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.36279558965043, 95.98015176944165, 95.19544158636303, 95.88961659923433]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.74147167500553, 93.20682541853515, 97.36801581885855, 97.21632680425802]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Test_2_3_2_1_Step6 - Done: [95.36851025302067, 95.98015176944165, 93.20682541853515]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "PASSED", "return_code": 0, "execution_time": 32.47643780708313, "start_time": "2025-06-05T22:25:08.672580", "end_time": "2025-06-05T22:25:41.149019", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: ***DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 7 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Test_2_3_2_1_Step7 - Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: ATC Power Level: -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Pwr: :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.10672928275336, 94.45727590232111, 96.46631906431172, 96.00102138016948]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Test_2_3_2_1_Step7 - Done 94.45727590232111\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 36.864805698394775, "start_time": "2025-06-05T22:25:41.149704", "end_time": "2025-06-05T22:26:18.014510", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: *** DO-181E, Reply Transmission Frequency,Sect 2.3.2.2.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseFrequency: Mock Frequency: 1030.1058661235145\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: RESULT1: Frequency 1030.105866\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseFrequency: Mock Frequency: 1029.8384346412054\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: RESULT2: Frequency 1029.838435\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: Test_2_3_2_2_1 - Done: [1030.1058661235145, 1029.8384346412054]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 68.36910128593445, "start_time": "2025-06-05T22:26:18.015175", "end_time": "2025-06-05T22:27:26.384277", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10381687907545074\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -24.37,-19.26,-22.35,-24.98\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 309, in <module>\n    res = Test_2_3_2_2_2(rm,atc_obj,pwr_obj,-52.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 189, in Test_2_3_2_2_2\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 45.7352192401886, "start_time": "2025-06-05T22:27:26.385536", "end_time": "2025-06-05T22:28:12.120756", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2_11-14-23.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09322895859094645\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -17.42,-25.99,-20.21,-22.83\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 289, in <module>\n    res = Test_2_3_2_2_2(rm,atc_obj,pwr_obj,-52.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 169, in Test_2_3_2_2_2\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "PASSED", "return_code": 0, "execution_time": 38.975815534591675, "start_time": "2025-06-05T22:28:12.122089", "end_time": "2025-06-05T22:28:51.097905", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4379923383049492\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PulseWidth: 0.4379923383049492\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PulseWidth Final: 0.0004379923383049492\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 13\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 13\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.059795240627444\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Message Width: 2.059795240627444\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Message Width Final: 0.002059795240627444\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Test_2_3_2_3_1 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.138283993147849\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.138283993147849\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1609670307594837\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.1609670307594837\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0113445067965707\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.0113445067965707\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Test_2_3_2_3_1 : Done, MsgWidth: 0.002059795240627444, Widths: 0.0004379923383049492, Delays: [3.138283993147849, 3.1609670307594837, 3.0113445067965707]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "PASSED", "return_code": 0, "execution_time": 63.03432083129883, "start_time": "2025-06-05T22:28:51.098536", "end_time": "2025-06-05T22:29:54.132858", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 13 pos edges, 13 neg edges\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PEdges: 13,[0.525876763596253, 0.7879681381437018, 0.2316660944718748, 0.5686748327734732, 0.5068759572015121, 0.1513727506542124, 0.41836460907999495, 0.8625360189372113, 0.7013582498858794, 0.7677998887073035, 0.9477954754299984, 0.5893092465077164, 0.003746023673025567] \n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: NEdges: 13,[0.8932457793423441, 0.8045582135154413, 0.6963131613990804, 0.340965791644418, 0.7283394165927728, 0.3966058042495709, 0.4735148282339138, 0.475846069576758, 0.7561088303750138, 0.9782688416077467, 0.030974047582839837, 0.4467097584988413, 0.9869484106594808]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: *** Framing PulseWidth ***-522130.73992322746\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: *** ModeA Pulse Width ***983202.0869864551\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Test_2_3_2_3_1 - Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.9840089996457415\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 2.9840089996457415\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.049633459712129\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 3.049633459712129\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.159213748574049\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 3.159213748574049\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Test_2_3_2_3_1 : Done, MsgWidth: -522130.73992322746, Widths: 983202.0869864551, Delays: [2.9840089996457415, 3.049633459712129, 3.159213748574049]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "PASSED", "return_code": 0, "execution_time": 97.20091414451599, "start_time": "2025-06-05T22:29:54.133638", "end_time": "2025-06-05T22:31:31.334554", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 1\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 1\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4525268032333084\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth1: 0.4525268032333084\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth1 Final: 0.0004525268032333084\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getRiseTime: Mock Rise Time: 0.09825553096382138\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: RiseTime: 0.09825553096382138\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: RiseTime Final: 9.825553096382139e-05\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getFallTime: Mock Fall Time: 0.09432651083204316\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: FallTime: 0.09432651083204316\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: FallTime Final: 9.432651083204316e-05\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.066176798276064\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P1: 2.066176798276064\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P1 Final: 0.002066176798276064\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4488494212668384\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth2: 0.4488494212668384\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth2 Final: 0.00044884942126683844\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9346232105663606\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P2: 1.9346232105663606\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P2 Final: 0.0019346232105663607\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.43795733707763257\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth3: 0.43795733707763257\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth3 Final: 0.00043795733707763257\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.941016846991158\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P3: 1.941016846991158\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P3 Final: 0.001941016846991158\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.42102169950447577\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth4: 0.42102169950447577\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth4 Final: 0.00042102169950447577\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.974350772773972\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P4: 1.974350772773972\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P4 Final: 0.001974350772773972\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:DFORMAT 2\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:DFORMAT 2\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9197633780369556\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P5: 1.9197633780369556\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P5 Final: 0.0019197633780369556\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.1065059832511114\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.03,-18.04,-14.04,-25.69\n\n*NumPULSEs:  True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: NumPULSEs: 1\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -24.47,-23.51,-16.51,-20.87\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Final Power Max: -20.870000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Final Power Min: -20.870000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Test_2_3_2_3_2a - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "PASSED", "return_code": 0, "execution_time": 30.03186058998108, "start_time": "2025-06-05T22:31:31.335119", "end_time": "2025-06-05T22:32:01.366980", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 6 pos edges, 6 neg edges\nPEdges:  6 [0.07200081156771232, 0.9952938503917907, 0.4305567923153558, 0.4382993699682609, 0.3777047283675058, 0.694745962130887]\nNEdges:  6 [0.7309801786612051, 0.48294809482160406, 0.3754163208204435, 0.459591609727363, 0.49523210000551265, 0.8605795461210642]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n*** MESSAGE SPAN *** 788578.7345533519\n*** PreAmble Span *** 305703.9167997935\n*** Data Span *** 482874.81775355845\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.10675472409580576\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -23.81,-18.63,-21.88,-20.83\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -19.73,-26.07,-13.80,-22.94\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Test_2_3_2_3_2a - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "PASSED", "return_code": 0, "execution_time": 106.26141214370728, "start_time": "2025-06-05T22:32:01.367883", "end_time": "2025-06-05T22:33:47.629296", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000A00001\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000A00001\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.42295194892823645\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth1: 0.42295194892823645\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth1 Final: 0.00042295194892823647\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getRiseTime: Mock Rise Time: 0.09619872421560005\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: RiseTime: 0.09619872421560005\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: RiseTime Final: 9.619872421560005e-05\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getFallTime: Mock Fall Time: 0.10317097365938067\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: FallTime: 0.10317097365938067\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: FallTime Final: 0.00010317097365938068\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9692031696112342\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P1: 1.9692031696112342\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P1 Final: 0.0019692031696112344\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.47553544890794647\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth2: 0.47553544890794647\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth2 Final: 0.0004755354489079465\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.904370520096451\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P2: 1.904370520096451\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P2 Final: 0.001904370520096451\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.47557169649549297\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth3: 0.47557169649549297\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth3 Final: 0.000475571696495493\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9035604301606837\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P3: 1.9035604301606837\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P3 Final: 0.0019035604301606837\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4065858610068528\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth4: 0.4065858610068528\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth4 Final: 0.0004065858610068528\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9113814277511416\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P4: 1.9113814277511416\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P4 Final: 0.0019113814277511416\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.0942521772533427\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P5: 2.0942521772533427\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P5 Final: 0.0020942521772533427\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.10236019941135976\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.97,-25.55,-14.70,-22.66\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.77,-17.16,-19.74,-22.06\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Final Power Max: -22.060000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Final Power Min: -22.060000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Test_2_3_2_3_2b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "PASSED", "return_code": 0, "execution_time": 30.010576009750366, "start_time": "2025-06-05T22:33:47.630033", "end_time": "2025-06-05T22:34:17.640609", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 6 pos edges, 6 neg edges\nPEdges:  6 [0.9577376966330295, 0.46619822016483636, 0.21840790661414278, 0.5266900902407448, 0.7723033821807843, 0.614522923167072]\nNEdges:  6 [0.4087245708731643, 0.9523591354935542, 0.45348289492689, 0.7394810080056452, 0.5412591868431864, 0.17346052029052839]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n*** MESSAGE SPAN *** -784277.1763425012\n*** PreAmble Span *** -185434.31445224522\n*** Data Span *** -598842.8618902559\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.10880632355398191\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.97,-17.39,-15.58,-25.67\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.47,-17.27,-22.76,-24.57\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Test_2_3_2_3_2b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "PASSED", "return_code": 0, "execution_time": 88.38123178482056, "start_time": "2025-06-05T22:34:17.641136", "end_time": "2025-06-05T22:35:46.022369", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: *** DO-181E, Side Lobe Supression: Sect 2.3.2.4 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.50844577110861, 96.31304050580525, 97.7633508114803, 95.3090284282321]\nRESULT: Power -61.0, ReplyRate 96.313041\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -61.0 ReplyRate: 96.31304050580525\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -48.0\n:ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.25387795205383, 95.76732383638063, 97.80919385950409, 96.16864132087011]\nRESULT: Power -48.0, ReplyRate 95.767324\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -48.0 ReplyRate: 95.76732383638063\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -28.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -28.0\n:ATC:XPDR:POW -28.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.73298630549466, 94.58355867926817, 96.37865534042187, 93.84517238356305]\nRESULT: Power -28.0, ReplyRate 94.583559\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -28.0 ReplyRate: 94.58355867926817\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.83551110069858, 93.99932004238326, 95.23794088348775, 95.2610188485261]\nRESULT: Power -9.0, ReplyRate 93.999320\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -9.0 ReplyRate: 93.99932004238326\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P2POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P2POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.37356043697143, 95.3935364118008, 94.94311617751087, 95.51545982373804]\nRESULT: Power -61.0, ReplyRate 95.393536\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -61.0 ReplyRate: 95.3935364118008\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.97397290818019, 93.86720063983516, 95.81962808976954, 96.76294244262739]\nRESULT: Power -38.0, ReplyRate 93.867201\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -38.0 ReplyRate: 93.86720063983516\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.05576376528514, 94.51717161186546, 94.34829226677509, 93.53110580621325]\nRESULT: Power -9.0, ReplyRate 94.517172\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -9.0 ReplyRate: 94.51717161186546\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4 - Done: [96.31304050580525, 95.76732383638063, 94.58355867926817, 93.99932004238326][95.3935364118008, 93.86720063983516, 94.51717161186546]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 2.372812509536743, "start_time": "2025-06-05T22:35:46.022937", "end_time": "2025-06-05T22:35:48.395749", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step1 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 121, in <module>\n    res = Test_2_3_2_5_Step1(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 63, in Test_2_3_2_5_Step1\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 2.3687808513641357, "start_time": "2025-06-05T22:35:48.396809", "end_time": "2025-06-05T22:35:50.765591", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step2 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 121, in <module>\n    res = Test_2_3_2_5_Step2(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 60, in Test_2_3_2_5_Step2\n    atc.transponderModeA_Only()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeA_Only'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 26.888307809829712, "start_time": "2025-06-05T22:35:50.766561", "end_time": "2025-06-05T22:36:17.654871", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step3 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 7.8usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.29096137310006, 94.78409845009371, 96.43091442758431, 94.3324336278629]\nReply Rate:  [94.29096137310006, 94.78409845009371, 96.43091442758431, 94.3324336278629]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 8.2usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.73285028461048, 94.83865576568304, 95.94160104830067, 97.44792652447761]\nReply Rate:  [93.73285028461048, 94.83865576568304, 95.94160104830067, 97.44792652447761]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 9.0usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.93030554146537, 93.2970936521507, 97.35060931831993, 93.82225085005354]\nReply Rate:  [95.93030554146537, 93.2970936521507, 97.35060931831993, 93.82225085005354]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3 - Done\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 198, in <module>\n    res = Test_2_3_2_5_Step3(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 125, in Test_2_3_2_5_Step3\n    atc.transponderModeCS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeCS'. Did you mean: 'transponderModeS'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.3752849102020264, "start_time": "2025-06-05T22:36:17.656327", "end_time": "2025-06-05T22:36:20.031613", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step4 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 129, in <module>\n    res = Test_2_3_2_5_Step4(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 58, in Test_2_3_2_5_Step4\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 80.20443820953369, "start_time": "2025-06-05T22:36:20.032462", "end_time": "2025-06-05T22:37:40.236901", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step5 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5a - Mode A\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.56367078111776, 96.15897650944254, 94.25161735282254, 96.12711546711644]\nReply Rate:  [96.56367078111776, 96.15897650944254, 94.25161735282254, 96.12711546711644]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.95299604453717, 96.48628831908827, 95.13695871471538, 94.1813798830105]\nReply Rate:  [96.95299604453717, 96.48628831908827, 95.13695871471538, 94.1813798830105]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.56990844162559, 94.84831953217105, 96.53614303909663, 94.94285733496234]\nReply Rate:  [93.56990844162559, 94.84831953217105, 96.53614303909663, 94.94285733496234]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.451220908338, 95.53752013148079, 95.19010368346285, 94.76898155113277]\nReply Rate:  [93.451220908338, 95.53752013148079, 95.19010368346285, 94.76898155113277]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.09955939049689, 93.06503845241566, 95.82172017703027, 93.89173200607657]\nReply Rate:  [95.09955939049689, 93.06503845241566, 95.82172017703027, 93.89173200607657]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.9730119410304, 92.95589937266278, 98.0660299106089, 95.85545258413305]\nReply Rate:  [95.9730119410304, 92.95589937266278, 98.0660299106089, 95.85545258413305]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5a - Done\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 199, in <module>\n    res = Test_2_3_2_5_Step5(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 136, in Test_2_3_2_5_Step5\n    atc.transponderModeC()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeC'. Did you mean: 'transponderMode'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 2.3833088874816895, "start_time": "2025-06-05T22:37:40.237651", "end_time": "2025-06-05T22:37:42.620961", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step6 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 216, in <module>\n    res = Test_2_3_2_5_Step6(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 133, in Test_2_3_2_5_Step6\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 49.79227638244629, "start_time": "2025-06-05T22:37:42.622025", "end_time": "2025-06-05T22:38:32.414302", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step7 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a ModeA:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.48991416754451, 95.2417821192188, 96.5436343219674, 95.87694681106862]\nReply Rate:  [94.48991416754451, 95.2417821192188, 96.5436343219674, 95.87694681106862]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.37645180007215, 95.7615763333313, 97.33519279646823, 94.41029051420593]\nReply Rate:  [95.37645180007215, 95.7615763333313, 97.33519279646823, 94.41029051420593]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a - Done\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 189, in <module>\n    res = Test_2_3_2_5_Step7(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 145, in Test_2_3_2_5_Step7\n    atc.transponderModeCS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeCS'. Did you mean: 'transponderModeS'?\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "PASSED", "return_code": 0, "execution_time": 148.48428058624268, "start_time": "2025-06-05T22:38:32.415556", "end_time": "2025-06-05T22:41:00.899837", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step8 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.19521340959314, 96.1223865750014, 96.33523063442802, 97.45761901858498]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.17441809456595, 93.62428144017002, 96.49054260963665, 97.38084088933213]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.56277454762665, 95.64122143404822, 94.72814408849186, 93.82460392442987]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.43155000687275, 95.36152271059323, 95.32810819987803, 96.41952794885884]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.41480193770597, 94.79545947840055, 95.27040744414074, 97.30571142487486]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.13569714319486, 93.99283226722713, 97.15101317059914, 96.6665567493444]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done: [97.45761901858498, 97.38084088933213, 95.64122143404822, 96.41952794885884, 97.30571142487486, 97.15101317059914]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 2.411891222000122, "start_time": "2025-06-05T22:41:00.900595", "end_time": "2025-06-05T22:41:03.312487", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_8.py->Test_2_3_2_8: *** DO-181E, Undesired Replies: Sect 2.3.2.8 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 124, in <module>\n    res = Test_2_3_2_8(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 58, in Test_2_3_2_8\n    atc.transponderModeAS()\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeAS'. Did you mean: 'transponderModeA'?\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 17, "failed": 18, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1800.3596994876862, "average_sequence_time": 51.43884855679104, "sequences_per_hour": 69.98601448135881, "optimization_effectiveness": {"optimization_success_rate": 48.57142857142857, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 18, "failure_by_procedure": {"DO181": 18}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 18 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}