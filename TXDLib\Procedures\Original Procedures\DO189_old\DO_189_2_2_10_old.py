# -*- coding: utf-8 -*-
"""
Created on Monday April 11 1:35:30 2020

@author: E589493
         <PERSON><PERSON><PERSON>NS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             ON and OFF Channel Signal Rejection, Section 2.2.10.
             
             "During the conditions stated in ********, ********,
             ******** and ********, the desired aural identification
             shall be clear and unambiguous.

             ******** Off-Code On-Frequency Signal Rejection:
             For a desired signal from -80 dBm up to -52 dBm
             the interrogator signal shall satisfy 2.2.8b when the 
             interfering signal:

             a. is on the desired channel frequency;
             b. has a randomly occuring fruit at a rate of 3,600pp/s;
             c. has pulse codes 12 microseconds from the desired code; and
             d. has an amplitude of 42dB greater than the desired signal.

             ******** Off-Frequency On-Code Signal Rejection:
             a. For a desired signal from -80 dBm up to -52 dBm
                the interrogator signal shall satisfy 2.2.8b when the 
                interfering signal:
                (1) is on the first adjacent frequency (+/- 1.0 MHz from
                the assigned channel frequency);
                (2) has the pulse rate of 3,600 randomly occurring pp/s;
                (3) has the same pulse pair spacing as the desired signal;
                (4) has the amplitude 42 dB above the desired signal
             b. For a desired signal from -80 dBm up to -60 dBm
                the interrogator signal shall satisfy 2.2.8b when the 
                interfering signal:
                (1) is a valid DME reply frequency (960-1215 MHz);
                (2) is 2 Mhz and further removed from the desired
                channel frequency;
                (3) has a pulse rate of 3,600 randomly occuring
                pulse pairs per second;
                (4) has the same pulse pair spacing as the desired 
                signal; and
                (5) has an amplitude 50dB above the desired signal level.
            
            ******** Off-Frequency Off-Code Rejection 
            For a desired signal from -80 dBm up to -52 dBm the interrogator 
            signal shall satisfy 2.2.8b when the interfering signal:
            a. on the image frequency (in band 960-1215);
            b. has a pulse rate of 3,600 randomly occuring pp/s;
            c. has a pulse pair spacing +/-3 microseconds from the 
            desired code;
            d. amplitude 62 dB greater than the desired signal level.

            ******** Co-Channel Signal Rejection
            When two or more DME ground signals are received on the same
            channel, and if one signal is 8 dB or greater in amplitude
            than the next stronger signal, the distance displayed shall be 
            that of the stronger signal and the equipment shall meet the 
            accuracy requirements of paragraph 2.2.1. The aural identification
            of the stronger station shal be clear and unambiguous.

            Note: In this instance, unambiguous does not preclude the 
            possibility of the audible presence of the weaker signal.

            This requirement shall comply with pragraph 2.2.8 b. In addition,
            it shall also apply to variation in fruit to a maximum of 3,600
            pulse pairs per second (pp/s) with a pulse pair spacing as
            the desired code and signal level 8dB below that of the
            desired signal whose power level ranges from the minimum
            sensitivity level of -80 dBm up to -48 dBm.  The fruit shall 
            occur randomly with the exception that, for each interrogation
            range reply of at least 40 nmi, a synchronous pulse pair shall
            be provided whose range offset is equal to three times that
            of the desired signal.

            Further, when the difference in level between two co-channel
            signals is less than 8 dBm, and there s consequent danger of
            acquiring the undesired signal, the aural identification shall
            give positive indication of this hazardous condition by garbling
            or by presenting both identifications."
             
INPUTS:      Top_Cable_Loss, ATC, SigGen, ARINC client, 
OUTPUTS:     Distance, Pass Rate (must be >= 80%)

HISTORY:

0
5/11/2020   KF    Initial Release.
06/22/2020  AS    Added tvl statements
                  Added ARINC client                                 
03/10/2021   MRS   Updates for new handlers and Lobster.                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers import N5172BSigGen

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def init_DME_Standard(rm, cable_loss, atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel 56X at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    ARINC.writeChannel(111.90)
    rm.logMessage(0,"*Test_189_2_2_10 - Initializing ATC into DME mode ") 
    atc.DMEMode()
    atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))

def atc_power_level(rm, atc, Step_PowerLevel):
    """ This function sets the power level on the atc5000 eg -50 dBm for DO-189 2.2.1b"""
    rm.logMessage(0,"*Test_189_2_2_10 - Setting ATC Power Level to " + str(Step_PowerLevel)) 

    cmd = ':ATC:DME:POWER ' + str(Step_PowerLevel)
    atc.gwrite(cmd)
    time.sleep(1)

##############################################################################
###### Off-Code and On-Frequency Rejection                     ###############
##############################################################################

def OFF_Code_ON_Freq_rejection_atc(rm,atc, ARINC):
    """ This Sets ATC at VOR Pair 0 133.7MHz -80dBM"""
    rm.logMessage(0,"*Test_189_2_2_10 - Setting ATC at VOR Pair 0, Channel= 133.7MHz, Power Level = -80dBm") 
    ARINC.writeChannel(133.7)
    time.sleep(5)
    atc.gwrite(":ATC:DME:CHANNEL:MODE 0VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 133.7")
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 34")
    time.sleep(1)
    atc.gwrite(':ATC:DME:EFF 100')
    time.sleep(1)
    atc_power_level(rm,atc,-60)
    time.sleep(10)

def OFF_Code_ON_Freq_rejection_sg(rm, sg, sg_cable_loss):
    """ Initializes sigGen to transmit 3600 pps pulse spacing 24ums, 1151MHz """
    rm.logMessage(0,"*Test_189_2_2_10 - Initializing SigGen to Transmit 3600pps, 24ums pulse spacing, 1151MHz ") 
    sg.Reset()
    # adding cable loss to ensure interference signal is -38 dBm
    sg_power = "" + str(sg_cable_loss -38) + "dBm"
    
    #Download the WaveForm
    #res = sg.downloadwaveform('2_2_10_1_DME_INTF_3600') 
    #print('File Result: ', res)

    #Set Up Freq/Power
    sg.setFrequency('1151.0MHz')
    sg.setPower('-50.0 dBm')
    #sg.setPower(sg_power) 
    #sg.setRF('on')
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('2_2_10_1_DME_INTF_3600',10e6) 
    sg.setModulationState("on")
    sg.setRF('on')
    time.sleep(5)

##############################################################################
###### On-Code and Off-Frequency Rejection                     ###############
##############################################################################

def set_Off_Freq_On_Code_Adjacent_atc(rm, atc, ARINC):
    """ This Function sets the ATC for On-Code and On-Frequency Rejection.  Inputs are
    the atc object """
    rm.logMessage(0,"*Test_189_2_2_10 - Setting ATC at VOR Pair 0, Channel = 111.9 ") 
    ARINC.writeChannel(111.90)
    time.sleep(5)    
    atc.gwrite(":ATC:DME:CHANNEL:MODE 0VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 111.9")
    time.sleep(1)
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 34")
    time.sleep(1)
    atc.gwrite(':ATC:DME:EFF 100')
    time.sleep(1)
    atc_power_level(rm,atc,-60)
    time.sleep(10)

def ON_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss):
    """ Initializes sigGen to transmit 3600 pps pulse spacing 24ums, 1018MHz """
    sg.Reset()
    rm.logMessage(0,"*Test_189_2_2_10 - Initializing SigGen to Transmit 3600pps, 24ums pulse spacing, 1018MHz ") 
    sg_power = "" + str(sg_cable_loss -38) + "dBm"

    #Download the WaveForm
    #res = sg.downloadwaveform('2_2_10_2_DME_INTF_3600') 
    #print('File Result: ', res)

    #Set Up Freq/Power
    sg.setFrequency('1018.0MHz')
    sg.setPower('-50.0 dBm')
    #sg.setPower(sg_power)
    sg.setModulationState("off")
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('2_2_10_2_DME_INTF_3600',10e6) 
    sg.setModulationState('on')
    sg.setRF('on')
    time.sleep(5)

def IF_Signal_Param(rm, sg, Level, Freq, sg_cable_loss):
    """ This Function adjusts Power Level and carrier frequncy on Signal Generator
    pass in singal gererator object, Power Level, and Freq eg. (sg, -'50dBM', '1018.0MHz') """
    rm.logMessage(0,"*Test_189_2_2_10 - Setting SigGen Power Level = " + str(Level) + ", Frequency = " + str(Freq)) 
    sg_power = str(sg_cable_loss + Level) + "dBm"
    sg.setPower(sg_power)
    sg.setFrequency(Freq)

##############################################################################
###### Off-Code and Off-Frequency Rejection                    ###############
##############################################################################
def OFF_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss):
    """ Initializes sigGen to transmit 3600 pps pulse spacing CHY, 1143MHz """
    rm.logMessage(0,"*Test_189_2_2_10 - Initializing SigGen to Transmit 3600pps, CH Y pulse spacing, 1143MHz ") 
    sg_cable_loss = 0
    sg_power = str(-18 + sg_cable_loss) + "dBm" #TODO
    sg.Reset()

    #Download the WaveForm
    #res = sg.downloadwaveform('2_2_10_3_Y_DME_INTF_3600') 
    #print('File Result: ', res)

    #Set Up Freq/Power
    sg.setFrequency('1143.0MHz')
    sg.setPower(sg_power)
    sg.setModulationState("off")
    sg.setRF('on')
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('2_2_10_3_Y_DME_INTF_3600',10e6) 
    #sg.setRF('on')
    time.sleep(5)

def set_VOR_PAIR5(rm, atc, ARINC):
    """ This FUnction sets VOR Mode 5 to 117.9MHz  """
    rm.logMessage(0,"*Test_189_2_2_10 - Swtiching ATC to Channel Y - 117.95MHz") 
    ARINC.writeChannel(111.95)
    time.sleep(5)
    atc.gwrite(":ATC:DME:CHANNEL:MODE 5VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 111.9")
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 34")
    time.sleep(1)
    atc.gwrite(':ATC:DME:EFF 100')
    time.sleep(1)


def OFF_CHX_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss):
    """ Initializes sigGen to transmit 3600 pps channel Y pulse spacing , 1017MHz """
    rm.logMessage(0,"*Test_189_2_2_10 - Initializing SigGen to Transmit 3600pps, CH Y pulse spacing, 1017MHz ") 
    sg_cable_loss = 0
    sg_power = str(-18 + sg_cable_loss) + "dBm" #TODO

    sg.Reset()

    #Download the WaveForm
    #res = sg.downloadwaveform('2_2_10_3_X_DME_INTF_3600') 
    #print('File Result: ', res)

    #Set Up Freq/Power
    sg.setFrequency('1017.0MHz')
    sg.setPower(sg_power)
    sg.setModulationState("off")
    sg.setRF('on')
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('2_2_10_3_X_DME_INTF_3600',10e6) 
    #sg.setRF('on')
    time.sleep(5)

##############################################################################
###### Co-Channel Signal Rejection                             ###############
##############################################################################

def set_Co_Channel_Rejection(atc):
    """ This Function sets the Channel to 56X and enables Code audio """
    #Set Channel into DME X mode
    atc.gwrite(":ATC:DME:CHANNEL:MODE DME X") 
    time.sleep(1)
    #Set Channel 
    atc.gwrite(":ATC:DME:CHANNEL 56") 
    time.sleep(1)
    #Set Range to 34NMi
    atc.gwrite(":ATC:DME:RANGE 34") 
    time.sleep(1)
    #Set identification pulse on
    atc.gwrite(":ATC:DME:IDENT:MODE CODE")
    time.sleep(1)
    #Turn on Equalization Pulses
    atc.gwrite(":ATC:DME:EQUAL ON")
    time.sleep(1)
    atc.gwrite(':ATC:DME:POWER -77')


################################################################################
#####                         HELPER FUNCTIONS                             #####
################################################################################

def Range_Pass_Rate(rm,ARINC):
    """ Reads distance via 429 over 15 seconds, reading every .5 seconds.
    Returns the percentage times the correct distance and SSM was read"""
    # wait two seconds
    time.sleep(2)

    # loop for 4 minutes
    correct = 0                     # number of times correct   
    total = 0                       # total number of checks 
    endTime = time.time() + 15.0    # set time for 15 seconds  
    while(time.time() < endTime):
        dist = ARINC.getDistance_201()              # get distance
        ssm = ARINC.getSSM()                        # get SSM
        # check if SSM is correct and distance is within threshold 
        if(ssm == "11" and dist >= 33.83 and dist < 34.17):
            correct += 1
        total += 1
        time.sleep(.5)

    pass_rate = (correct/total) * 100
    rm.logMessage(0,"Range and RangeRate: " + str(dist) +", " + str(pass_rate))
    return dist,pass_rate

     
    
################################################################################
#####                         MAIN                                         #####
################################################################################

def Test_2_2_10(rm,atc,sg,ARINC,PathLoss):
    
    sg_cable_loss = PathLoss
    
    #initialize results
    Dist = [-1.0,-1.0,-1.0,-1.0,-1.0,-1.0,-1.0,-1.0]
    Rate = [-1.0,-1.0,-1.0,-1.0,-1.0,-1.0,-1.0,-1.0]

    #Turn On ATC
    atc.gwrite(":ATC:DME:START")
    time.sleep(5)

    # 10.1 ------------------------------------------
    #OFF Code On Frequency 
    OFF_Code_ON_Freq_rejection_atc(rm, atc, ARINC)
    time.sleep(3)
    #OFF_Code_ON_Freq_rejection_sg(rm, sg, sg_cable_loss)
    time.sleep(5)
    # Determine percent of times correct range is read
    Dist[0],Rate[0] = Range_Pass_Rate(rm,ARINC)
    
    # Raise Desired and Interferer Signals
    atc_power_level(rm,atc, -52)
    time.sleep(5)
    IF_Signal_Param(rm,sg, -10, '1151.0MHz', sg_cable_loss)
    Dist[1],Rate[1] = Range_Pass_Rate(rm,ARINC)
    
    # 10.2 ------------------------------------------
    #On Code Off Frequency
    set_Off_Freq_On_Code_Adjacent_atc(rm, atc, ARINC)
    #ON_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss)
    # Determine percent of times correct range is read
    Dist[2],Rate[2]  = Range_Pass_Rate(rm,ARINC)


    # Raise Desired and Interferer Signals
    atc_power_level(rm, atc, -52)
    time.sleep(5)
    IF_Signal_Param(rm, sg, -10, '1016.0MHz', sg_cable_loss)
    time.sleep(4)
    # Determine percent of times correct range is read
    Dist[3],Rate[3] = Range_Pass_Rate(rm,ARINC)

    # Second Adjacent Channel - Lower Desired Levels
    atc_power_level(rm, atc, -60)
    time.sleep(5)
    IF_Signal_Param(rm, sg, -38, '1019.0MHz', sg_cable_loss)
    time.sleep(4)
    # Determine percent of times correct range is read
    Dist[4],Rate[4]  = Range_Pass_Rate(rm,ARINC)
   
    # Second Adjacent Channel - Raise Desired Levels
    atc_power_level(rm, atc, -52)
    time.sleep(5)
    IF_Signal_Param(rm, sg, -10, '1015.0MHz', sg_cable_loss)
    time.sleep(4)
    # Determine percent of times correct range is read
    Dist[5],Rate[5] = Range_Pass_Rate(rm,ARINC)
  
    
    # 10.3 ------------------------------------------
    # Off Code Off Frequency
    atc_power_level(rm, atc, -60)
    OFF_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss)
    time.sleep(4)
    # Determine percent of times correct range is read
    Dist[6],Rate[6]  = Range_Pass_Rate(rm,ARINC)

    # Channel Y VOR Pair 5
    set_VOR_PAIR5(rm, atc, ARINC)
    time.sleep(5)
    atc_power_level(rm, atc, -60)
    time.sleep(4)
    OFF_CHX_Code_OFF_Freq_rejection_sg(rm, sg, sg_cable_loss)
    time.sleep(4)
    # Determine percent of times correct range is read
    Dist[7],Rate[7]  = Range_Pass_Rate(rm,ARINC)

    # 10.4 ------------------------------------------
    # Co-Channel Signal Rejection
    # switch to 111.90?
    #set_Co_Channel_Rejection(atc)
    # TODO: ? 


    #Trun off signal generators.
    sg.setRF("OFF")
    atc.gwrite(":ATC:DME:STOP")

    rm.logMessage(0,"Reuslts: " + str(Dist) + ", " + str(Rate))

    rm.logMessage(2,"Done,Closing Session.")

    return Dist + Rate
    
################################################################################

if __name__ == "__main__":

    #Initialize Intruuments
    rm = ate_rm()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)
	
    #Initialize Signal Generator
    sg = N5172BSigGen(rm)
    sg.Reset()
    
    
    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    
    PathLoss = 12.25

    Test_2_2_10(rm,atc,sg,ARINC,PathLoss)
    
    
    #Close the ATC and Signal Generator. 
    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    sg.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
