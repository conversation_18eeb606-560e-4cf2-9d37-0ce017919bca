#!/usr/bin/env python3
"""
Debug script to test mock import behavior in subprocess
"""

import subprocess
import sys
import os
from pathlib import Path

# Simulate the exact environment from sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

print("Environment setup:")
print(f"PYTHONPATH: {env.get('PYTHONPATH')}")

# Test script that mimics sequence import pattern
test_script = '''
import sys
print("Python path in subprocess:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

print("\\nTesting ate_rm import...")
try:
    from TXDLib.Handlers import ate_rm
    print(f"ate_rm type: {type(ate_rm)}")
    print(f"ate_rm location: {ate_rm.__module__ if hasattr(ate_rm, '__module__') else 'N/A'}")
    
    # Test instantiation
    rm = ate_rm()
    print(f"rm type: {type(rm)}")
    print("SUCCESS: ate_rm instantiation worked!")
    
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()
'''

print("\nRunning subprocess test...")
result = subprocess.run(
    [sys.executable, '-c', test_script],
    capture_output=True,
    text=True,
    env=env
)

print("STDOUT:")
print(result.stdout)
if result.stderr:
    print("STDERR:")
    print(result.stderr)
print(f"Return code: {result.returncode}")
