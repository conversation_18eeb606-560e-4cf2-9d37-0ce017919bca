#!/usr/bin/env python3
"""
Legacy ATC module compatibility layer for DO181 sequence DO_181E_2_3_2_12.py
Maps legacy atc.ATC class to modern TXDLib.Handlers.ATC5000NG
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path to access modern handlers
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

try:
    from TXDLib.Handlers.ATC5000NG import ATC5000NG
    from TXDLib.Handlers.ate_rm import ate_rm
except ImportError:
    # Fallback if direct import fails
    import importlib.util
    
    # Load ATC5000NG
    atc_spec = importlib.util.spec_from_file_location(
        "ATC5000NG", 
        current_dir / "TXDLib" / "Handlers" / "ATC5000NG.py"
    )
    atc_module = importlib.util.module_from_spec(atc_spec)
    atc_spec.loader.exec_module(atc_module)
    ATC5000NG = atc_module.ATC5000NG
    
    # Load ate_rm
    rm_spec = importlib.util.spec_from_file_location(
        "ate_rm", 
        current_dir / "TXDLib" / "Handlers" / "ate_rm.py"
    )
    rm_module = importlib.util.module_from_spec(rm_spec)
    rm_spec.loader.exec_module(rm_module)
    ate_rm = rm_module.ate_rm


class ATC:
    """Legacy ATC class that wraps modern ATC5000NG"""

    def __init__(self, resource_manager):
        """Initialize with legacy resource manager"""
        # Create modern resource manager if needed
        if hasattr(resource_manager, 'logMessage'):
            self.rm = resource_manager
        else:
            # Convert pyvisa ResourceManager to ate_rm
            self.rm = ate_rm()

        # Create modern ATC5000NG instance
        try:
            self.atc = ATC5000NG(self.rm)
        except Exception as e:
            print(f"Warning: Could not create ATC5000NG: {e}")
            # Create a minimal mock for testing
            self.atc = self._create_minimal_mock()
        
    def Reset(self):
        """Legacy reset method"""
        return self.atc.reset()
        
    def Ident(self):
        """Legacy identification method"""
        return self.atc.ident()
        
    def transponderMode(self):
        """Legacy transponder mode method"""
        return self.atc.transponderMode()
        
    def transponderModeS(self):
        """Legacy transponder Mode S method"""
        return self.atc.transponderModeS()
        
    def init_own_aircraft_pos(self):
        """Legacy aircraft position initialization"""
        # Mock implementation
        self.rm.logMessage(1, "Mock: Aircraft position initialized")
        return True
        
    def set_cable_loss(self, top_loss, bot_loss):
        """Legacy cable loss setting"""
        self.rm.logMessage(1, f"Mock: Cable loss set - Top: {top_loss}, Bottom: {bot_loss}")
        return True
        
    def gwrite(self, command):
        """Legacy write command"""
        return self.atc.write(command)
        
    def waitforstatus(self):
        """Legacy wait for status"""
        import time
        time.sleep(0.1)  # Reduced timing
        return True
        
    def data_log_start(self):
        """Legacy data logging start"""
        return self.atc.data_log_start()
        
    def data_log_stop(self, filename):
        """Legacy data logging stop"""
        return self.atc.data_log_stop(filename)
        
    def getPercentReply(self, duration):
        """Legacy reply rate measurement"""
        import random
        import time
        time.sleep(duration * 0.1)  # Reduced timing
        
        # Return realistic reply rates [ATCRBS_Top, ModeS_Top, ATCRBS_Bottom, ModeS_Bottom]
        reply_rates = [
            random.uniform(85.0, 95.0),  # ATCRBS Top
            random.uniform(90.0, 98.0),  # ModeS Top  
            random.uniform(85.0, 95.0),  # ATCRBS Bottom
            random.uniform(90.0, 98.0)   # ModeS Bottom
        ]
        
        self.rm.logMessage(1, f"Mock: Reply rates measured: {reply_rates}")
        return reply_rates
        
    def closeATC(self):
        """Legacy close method"""
        return self.atc.close() if hasattr(self.atc, 'close') else True

    def _create_minimal_mock(self):
        """Create minimal mock ATC for testing"""
        class MinimalMockATC:
            def __init__(self):
                pass
            def reset(self): return True
            def ident(self): return "Mock ATC5000NG,Model123,SN456789,FW1.0"
            def write(self, cmd): print(f"Mock ATC Write: {cmd}"); return True
            def data_log_start(self): print("Mock: Data logging started"); return True
            def data_log_stop(self, filename): print(f"Mock: Data logging stopped - {filename}"); return True
            def close(self): return True

        return MinimalMockATC()
