# -*- coding: utf-8 -*-
"""
Created on Wed Mar 11 14:31:43 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS Qualification Test Group
         
Description:
    This class provides utility functions for the Boonton 4500C Power
    Meter
    
Inputs:  see class methods
Outputs: see class methods

History:

03/27/2020   MRS  Initial Release
03/30/2020   MRS  Added methods for Power for Specific Pulse.
04/16/2020   MRS  Adjusted marker positions (lead/fall edge) by 1 to 2 counts.
                  Changed READ to FETCH for pulse position.
06/10/2020   MRS  Added new routine for finding pulses based on local Maximim.
10/05/2020   NEZ  Updated for new Instrument Resource Manager.
02/09/2021   MRS  Added titles for plots, created new methond to clear screen.

"""

# CS - Modified for TXD Pre-Pre-Qual - Added functions for Initiate:Continuous, Read Marker Power 8/4/22

import time, sys, os
import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks


class B4500CPwrMeter():
    def __init__(self, ate_rm):
        self.pwrmeterIP = "TCPIP0::************::INSTR"
        self.resourceManager = ate_rm
        self.pwrmeter = self.resourceManager.rm.open_resource(self.pwrmeterIP)
        self.pwrmeter.write("")
        tvlTxt = "Resource Opened."
        self.basicTvlMsg(tvlTxt)
        
        #Init Storage Arrays
        self.t = np.arange(500, dtype=np.float)
        self.y = np.arange(500, dtype=np.float)
        self.s_time = np.arange(128, dtype=np.int)
        self.e_time = np.arange(128, dtype=np.int)
        self.npulse = 0

        

##################################################
# Basic Commands
##################################################

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.pwrmeter.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=False):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.pwrmeter.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp

    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, tvlTxt)

    def Ident(self):
        """ Returns Instrument Ident. """
        return self.basicQuery("*IDN?")

    def Reset(self):
        """ Resets the Instrument. """
        self.pwrmeter.write("*RST")
        self.resourceManager.logMessage(1, "PwrMeter Resetting...")
        time.sleep(5)
        self.resourceManager.logMessage(1, "PwrMeter complete\n")
    
    def close(self):
        """ Closes the Session. """
        self.pwrmeter.close()

##################################################
# Frequency, Amplitude, Power, Trigger  Commands
##################################################
    def getError(self):
        """ Returns most recent error, repeated reads will get next error. """
        return self.basicQuery("SYSTem:ERRor?")
    
    def autoset(self):
        """ Initializes Meter to Defaults. """
        self.basicWrite("SYSTem:AUTOSET")
    
    def displyClear(self):
        """ Clears the display. """
        self.basicWrite("DISPlay:CLEar")
        
    def setCalculateMode(self, Mode):
        """ Sets the Meter to PULSE or MODULATED mode. """
        self.basicWrite("CALCulate:MODE {}".format(str(Mode)))

    def setTriggerMode(self, Mode):
        """ Sets the Trigger TRIGger:MODE {AUTOPKPK, AUTO,NORMAL, FREERUN} mode. """
        self.basicWrite("TRIGger:MODE {}".format(str(Mode)))
    
    def getTriggerMode(self):
        """ Gets the Trigger TRIGger:MODE. """
        return self.basicQuery("TRIGger:MODE?")
    
    def setTriggerSource(self, source):
        """ Sets TRIGger:SOURce {CH1, CH2, TRIG1,TRIG2} . """
        self.basicWrite("TRIGger:SOURce {}".format(str(source)))
    
    def getTriggerSource(self):
        """ Gets the TRIGger:SOURce {CH1, CH2, TRIG1,TRIG2}. """
        return self.basicQuery("TRIGger:SOURce?")

    def setTriggerLevel(self, level):
        """ TRIGger:LEVel <-5 to +5> . """
        self.basicWrite("TRIGger:LEVel {}".format(str(level)))
    
    def getTriggerLevel(self):
        """ TRIGger:LEVel. """
        return self.basicQuery("TRIGger:LEVel?")

    def setTriggerSlope(self, slope):
        """ TRIGger:SLOPe {POS, NEG} """
        self.basicWrite("TRIGger:SLOPe {}".format(str(slope)))
    
    def getTriggerSlope(self):
        """ TRIGger:SLOPe """
        return self.basicQuery("TRIGger:SLOPe?")
    
    def setTriggerHoldoff(self, holdoff):
        """ TRIGger:HOLDoff <0 to 65000e-6> (sec) """
        self.basicWrite("TRIGger:HOLDoff {}".format(str(holdoff)))
    
    def getTriggerHoldoff(self):
        """ TRIGger:HOLDoff """
        return self.basicQuery("TRIGger:HOLDoff?")

    def setTriggerDelay(self, delay):
        """ TRIGger:DELay """
        self.basicWrite("TRIGger:DELay {}".format(str(delay)))
    
    def getTriggerDelay(self):
        """ TRIGger:DELay """
        return self.basicQuery("TRIGger:DELay?")

    def setVerticalCenter(self, center):
        """ DISPlay:TRACe[1|2|3|4|5|6|7]:VCENTer <n>
        Argument: <n> = -200.00 to +200.00 dBm for dBm units """
        self.basicWrite("DISPlay:TRACe1:VCENTer {}".format(str(center)))
    
    def getVerticalCenter(self):
        """ DISPlay:TRACe[1|2|3|4|5|6|7]:VCENTer """
        return self.basicQuery("DISPlay:TRACe1:VCENTer?")
    
    def setVerticalScale(self, Scale):
        """ Syntax: DISPlay:TRACe[1|2|3|4|5|6|7]:VSCALe <n>
        Argument: Units = dBm, dBV, dBmV, dBuV , <n> = range in dB/division  """
        self.basicWrite("DISPlay:TRACe1:VSCALe {}".format(str(Scale)))
    
    def getVerticalScale(self):
        """ DISPlay:TRACe[1|2|3|4|5|6|7]:VSCALe """
        return self.basicQuery("DISPlay:TRACe1:VSCALe?")

    def setCalculateUnits(self, Units):
        """ Sets both Chanels Meter Display Units, 'dBm, W, V, dBv, dBmV, dBuV' """
        self.basicWrite("CALCulate1:UNIT {}".format(str(Units)))
        self.basicWrite("CALCulate2:UNIT {}".format(str(Units)))

    def setCalculate1_on(self, onoff):
        """ Turns on Channel 1. """
        self.basicWrite("CALCulate1:STATe {}".format(str(onoff)))
    
    def setCalculate2_on(self, onoff):
        """ Turns on Channel 2. """
        self.basicWrite("CALCulate2:STATe {}".format(str(onoff)))

    def getCalculate1_on(self):
        """ Get Chan1 State. """
        return self.basicQuery("CALCulate1:STATe?")

    def getCalculate2_on(self):
        """ Get Chan2 State. """
        return self.basicQuery("CALCulate1:STATe?")
    
    def setTimeBase(self, time):
        """ Sets the time base for the display, input is a string."""
        self.basicWrite("DISPlay:PULSE:TIMEBASE {}".format(str(time)))

    def getTimeBase(self):
        """ Sets the time base for the display, input is a string."""
        return self.basicQuery("DISPlay:PULSE:TIMEBASE?")
   
    def setFrequency1(self, Frequency):
        """ Sets Chan1 Frequency. """
        self.basicWrite("SENSe1:CORRection:FREQuency {}".format(str(Frequency)))   
        
    def getPower1(self):
        """ Get Chan1 Power Level. """
        return self.basicQuery("MEAS1:POWER?")
    
    def getVoltage1(self):
        """ Get Chan1 Voltage level. """
        return self.basicQuery("MEAS1:VOLTAGE?")
    
    def setFrequency2(self, Frequency):
        """ Sets Chan2 Frequency. """
        self.basicWrite("SENSe2:CORRection:FREQuency {}".format(str(Frequency)))   
        
    def getPower2(self):
        """ Get Chan1 Power Level. """
        return self.basicQuery("MEAS1:POWER?")
    
    def getVoltage2(self):
        """ Get Chan1 Voltage level. """
        return self.basicQuery("MEAS1:VOLTAGE?")

    def initiate_cont(self, cont_onoff):
        """ Turns continuous measurements on/off """
        self.basicWrite("INITiate:CONTinuous {}".format(str(cont_onoff)))
    
    def fetch1_pwr(self):
        """ Returns a Ch1 comma separated string with pulse power parametrs.
        returns: CC1, PulsePeak, CC2, PulseCycleAvg, CC3, PulseOnAvg, CC4, IEEE Top, CC5, IEEE Bot, CC6, Overshoot."""
        #Simple Whole Window Power Measurements
        """
        pwr = self.fetch1_pwr()
        print("Fetch Pwr: ",pwr)
        pwr_str = pwr.split(',')
        print("PulsePeak",pwr_str[1])
        print("PulseCycleAvg",pwr_str[3]),
        print("PulseOnAvg",pwr_str[5]),
        print("IEEE Top",pwr_str[7]),
        print("IEEE Bot",pwr_str[9]),
        print("Overshoot",pwr_str[11])
        """
        return self.basicQuery("FETCh1:ARRay:AMEAsure:POWer?")
    
    def fetch1_time(self):
        """ Returns a Ch1 comma separated string with pulse timing parametrs.
        returns CC1, PulseFreq, CC2, PulsePeriod, CC3, PulseWidth, CC4, Offtime, CC5, DutyCycle, CC6, Risetime, CC7, Falltime, CC8, EdgeDly, CC9, Skew in Hz, percent or seconds."""
        """
        #Simple Whole Window Power Measurements
        tme =self.fetch1_time()
        print("Fetch Tim: :",tme)
        tme_str = tme.split(',')
        print("PulseFreq",tme_str[1])
        print("PulsePeriod",tme_str[3])
        print("PulseWidth",tme_str[5])
        print("Offtime",tme_str[7])
        print("DutyCycle",tme_str[9])
        print("Risetime",tme_str[11])
        print("Falltime",tme_str[13])
        print("EdgeDly",tme_str[15])
        print("Skew",tme_str[17])
        """
        return self.basicQuery("FETCh1:ARRay:AMEAsure:TIMe?")

    def fetch2_pwr(self):
        """ Returns a Ch2 comma separated string with pulse power parametrs. 
        returns: CC1, PulsePeak, CC2, PulseCycleAvg, CC3, PulseOnAvg, CC4, IEEE Top, CC5, IEEE Bot, CC6, Overshoot."""
        return self.basicQuery("FETCh2:ARRay:AMEAsure:POWer?")
    
    def fetch2_time(self):
        """ Returns a Ch2 comma separated string with pulse timing parametrs.
        returns CC1, PulseFreq, CC2, PulsePeriod, CC3, PulseWidth, CC4, Offtime, CC5, DutyCycle, CC6, Risetime, CC7, Falltime, CC8, EdgeDly, CC9, Skew in Hz, percent or seconds."""
        return self.basicQuery("FETCh2:ARRay:AMEAsure:TIMe?")
    
    def Marker1_Power(self):   
        power_array = self.basicQuery('FETCh:ARRay:MARKer:POWer?')
        power_array = power_array.split(',')
        return float(power_array[9])

    def Marker2_Power(self):   
        power_array = self.basicQuery('FETCh:ARRay:MARKer:POWer?')
        power_array = power_array.split(',')
        return float(power_array[11])

    def CH1_Marker_Power(self):   
        power_array = self.basicQuery('FETCh1:ARRay:MARKer:POWer?')
        power_array = power_array.split(',')
        marker_1 = (power_array[9])
        marker_2 = (power_array[11])
        return [marker_1, marker_2]
        
    def CH2_Marker_Power(self):   
        power_array = self.basicQuery('FETCh2:ARRay:MARKer:POWer?')
        power_array = power_array.split(',')
        marker_1 = (power_array[9])
        marker_2 = (power_array[11])
        return [marker_1, marker_2]

    def Read_Marker_Power(self):   
        power_array = self.basicQuery('READ:ARRay:MARKer:POWer?')
        power_array = power_array.split(',')
        marker_1 = float(power_array[9])
        marker_2 = float(power_array[11])
        #return [marker_1, marker_2]
        return power_array

    def Marker1_Avg(self):   
        return self.basicQuery('FETCh1:MARKer:AVERage?')
    
    def Marker2_Avg(self):   
        return self.basicQuery('FETCh2:MARKer:AVERage?')

##################################################
# Plot
##################################################
    def plot(self, timebase):
        """ Plots the Display Trace, sclales the time axis by
        the 'timebase' in seconds (i.e. '10-3').   """
        
        for i in range(500):
           self.t[i] = self.t[i]*float(timebase)/5.0
       
        fig, ax = plt.subplots()
        ax.set(xlabel='time (sec)', ylabel='power (dBm)',title='Pulse Power')
        #ax.set_ylim(2.0,0.25)
        ax.set_xlim(0.0,100.0*float(timebase))
        ax.plot(self.t,self.y)
        plt.show()
        
    def getpwrmeasuremet(self):
        """Gets Power Measurements, depends on Marker Settings for
        window,returns a string with the measurment paramters. """
        
        #Power Values between markers
        pwr = self.basicQuery("FETCh1:ARRay:MARKer:POWer?")
        self.resourceManager.logMessage(1, "Read Pwr: "+ pwr)
        pwr_str = pwr.split(',')
        self.resourceManager.logMessage(1, "PulseAvg: "+ pwr_str[1])
        self.resourceManager.logMessage(1, "PulseMax: "+ pwr_str[3]),
        self.resourceManager.logMessage(1, "PulseMin: "+ pwr_str[5]),
        self.resourceManager.logMessage(1, "PulseAvgRatio: "+ pwr_str[7]),
        self.resourceManager.logMessage(1, "PulsePwr1: "+ pwr_str[9]),
        self.resourceManager.logMessage(1, "PulsePwr2: "+ pwr_str[11]) 
        self.resourceManager.logMessage(1, "Mk1/Mk2 Ratio: "+ pwr_str[13])
        return pwr
    
    
    def gettimemeasurement(self, timeBase_str):
        """Gets Time Measurements, input is the TimeBase as a string,
        depends on Marker Settings for window,
        returns a float with the measurment paramters. """
        
        timeBase = float(timeBase_str)
        
        #Time value between markers
        t1_s = self.basicQuery("MARKer1:POSition:PIXel?")
        t2_s = self.basicQuery("MARKer2:POSition:PIXel?")
        t1 = int(t1_s)
        t2 = int(t2_s)
        tme = float(t2 - t1 - 1)*timeBase/500.0
        self.resourceManager.logMessage(1, "Delta Time: "+ str(tme))
        return tme
    
##################################################
# Find Pulses, Set Up Pulse Markers
##################################################
    def getnumpulses(self, threshold_str):
        """Get Pulse Start/EndTimes. Input threshold as string
        in dB: '10' for 10 dBm """
        #start/end times, number of pulses
        threshold = int(threshold_str)
        
        #get trace data
        self.basicWrite("TRACe:SOURce CH1")
        self.basicWrite("TRACE1:INDEX 0")
        self.basicWrite("TRACE1:COUNT 501")
        time.sleep(3)
        
        ystr = self.basicQuery("TRACe1:MAXimum:DATA?")
        time.sleep(3)
        ylin=ystr.split(',')
        for i in range(500):
            self.y[i]=float(ylin[i])
            #print(i,ylin[i],str(self.y[i]))
            
        #compute floor and peak
        """
        floor = 0.0
        for i in range(50):
            floor = floor + self.y[i]
        #floor = floor/50
        """
        floor = -10.0
        self.resourceManager.logMessage(1, "FLOOR: " + str(floor))
                
        #determine transitions
        ptrans = 0
        ntrans = 0
        y_s = floor
        for i in range(500):
            delta = ( self.y[i] - y_s)
            y_s = self.y[i]       #past value
            #positive transitions
            if delta > threshold:
                self.s_time[ptrans] = i
                self.npulse = self.npulse + 1
                self.resourceManager.logMessage(1, ("+Delta %d, %f: %f, %f") % (i,delta, self.y[i],y_s))
                ptrans = ptrans+ 1
                
            #negative transitons
            if -delta > threshold:
                self.e_time[ntrans] = i
                self.resourceManager.logMessage(1, ("-Delta %d, %f: %f, %f") % (i,delta, self.y[i],y_s))
                ntrans = ntrans + 1
                
        
        self.resourceManager.logMessage(1, ("PTRANS: %d: %d %d") % (self.npulse, ptrans, ntrans))
        return self.npulse
                
    
    
    def setpulsepositions(self, pulse_str):
       """ Sets the Marker Positions, based input string,the pulse number.
       The 'getnumpulses(threshold)' needs to be run first for this routine to 
       work. Use getpwrmeasurement(), gettimemeasurement() to display pulse power
       and time measurements for the specified pulse"""
       pulse = int(pulse_str)
       if (pulse > self.npulse):
           self.resourceManager.logMessage(3, "ERROR: pulse number exceeds number of pulses")
       
       #Set Up Markers
       self.basicWrite("MARKer1:ASSIgn CH1")
       self.basicWrite("MARKer2:ASSIgn CH1")
       self.basicWrite("MARKer1:POSition:PIXel " + str(self.s_time[pulse]))
       self.basicWrite("MARKer2:POSition:PIXel " + str(self.e_time[pulse]))
       self.basicWrite("MARKer:MATH:BOTH AVG,MINMAX,dBm")

       
       
    def findpeaks(self, threshold_str):
        """Gets the number of pulses by determining local maximum.  Threshold_str, is the 
        level in dBm where maximums are searched.  Returns number of Pulse (or maximums)."""

        self.npulse = 0
        threshold = int(threshold_str)
        self.resourceManager.logMessage(1, "Passed through threshold: " + str(threshold))
        
        #get trace data
        self.basicWrite("TRACe:SOURce CH1")
        self.basicWrite("TRACE1:INDEX 0")
        self.basicWrite("TRACE1:COUNT 501")
        time.sleep(3)
        
        ystr = self.basicQuery("TRACe1:MAXimum:DATA?")
        time.sleep(3)
        ylin=ystr.split(',')
        for i in range(500):
            self.y[i]=float(ylin[i])
            #print(i,ylin[i],str(self.y[i]))

        #SciPy Max detector
        self.peaks,_ = find_peaks(self.y, height=threshold, distance=15)                #added distance param to compensate for clipped dme sig
        self.resourceManager.logMessage(1, "\nPeaks: " + str(self.peaks))

		#Arrays to set Markers for power measurement
        for i in range(len(self.peaks)):
            p = self.peaks[i]
            self.s_time[i] = p - 2
            self.e_time[i] = p + 2
        
        
        self.npulse = len(self.peaks)
     
        return self.npulse    #number of pulses
            
    def plotpeaks(self, threshold_str,title='PowerMeter'):       
        """Plots the Peaks in the data array, input threshold as str."""
        threshold = int(threshold_str)

        #Plot Results.
        fig = plt.figure()
        plt.plot(self.y)
        plt.plot(self.peaks,self.y[self.peaks], "X")
        plt.plot(np.zeros_like(threshold), "--", color="red")  
        plt.xlabel('time')
        plt.ylabel('dBm')
        fig.suptitle(title)         
        plt.show()
        
    def screenshot(self, filename):
        ''' Saves screenshot of display into a bitmap(.bmp) file '''
        directory = "C:\\Honeywell\\Boonton ScreenShots"
        if os.path.isdir(directory) == False:
            os.mkdir(directory)
        bitmapFile = directory + "\\" + str(filename) + ".bmp"
        self.basicWrite("SYSTem:DISPlay:BMP?")
        screen = self.pwrmeter.read_raw() # IEEE-488.2 definite length format
        with open(bitmapFile, 'w+b') as f:
            f.write(screen[9:]) # remove IEEE-488.2 header block, keep only bitmap data
        
      
        
    
