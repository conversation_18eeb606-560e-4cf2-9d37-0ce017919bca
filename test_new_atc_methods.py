#!/usr/bin/env python3
"""
Test new ATC5000NG transponder methods implementation
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path to simulate mock mode
current_dir = Path.cwd()
mock_handlers_path = current_dir / "MockHandlers"
sys.path.insert(0, str(mock_handlers_path))

print("Testing new ATC5000NG transponder methods...")

try:
    from TXDLib.Handlers import ate_rm, ATC5000NG
    
    # Create resource manager
    rm = ate_rm()
    print("✅ ate_rm created successfully")
    
    # Test rm.close() method
    rm.close()
    print("✅ rm.close() - SUCCESS")
    
    # Create new rm for ATC test
    rm = ate_rm()
    
    # Create ATC5000NG instance
    atc = ATC5000NG(rm)
    print("✅ ATC5000NG created successfully")
    
    # Test new transponder methods
    new_methods = ['transponderModeC', 'transponderModeCS', 'transponderModeAS', 'transponderModeA_Only']
    
    for method_name in new_methods:
        if hasattr(atc, method_name):
            method = getattr(atc, method_name)
            method()
            print(f"✅ {method_name}() - SUCCESS")
        else:
            print(f"❌ {method_name}() - MISSING")
    
    print("\n✅ All new ATC5000NG transponder methods working!")
    
except Exception as e:
    print(f"❌ FAILED: {e}")
    import traceback
    traceback.print_exc()
