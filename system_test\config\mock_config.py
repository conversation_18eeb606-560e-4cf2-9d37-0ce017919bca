#!/usr/bin/env python3
"""
TXD Qualification Test System - Mock Configuration
Configuration settings for mock execution mode
"""

# from typing import Dict, Any  # Not available in Python 3.4


class MockConfig:
    """Configuration settings for mock mode execution"""
    
    # Mock timing settings (optimized values)
    MOCK_TIMINGS = {
        'connection_time': 0.1,          # Time to establish mock connections
        'scenario_loading_time': 2.0,    # Optimized from 50s
        'scenario_start_time': 1.0,      # Optimized from 30s
        'rf_stabilization_time': 1.5,    # Optimized from 15s
        'instrument_reset_time': 1.0,    # Optimized from 15s
        'measurement_time': 0.5,         # Time for mock measurements
        'communication_delay': 0.05,     # Mock communication delays
        'status_check_time': 0.1         # Time for status checks
    }
    
    # Mock hardware responses
    MOCK_RESPONSES = {
        'atc5000ng': {
            'default_frequency': 1090e6,
            'power_range': (-30.0, 10.0),
            'status_responses': {
                'connected': True,
                'scenario_loaded': False,
                'rf_enabled': False,
                'ready': False
            }
        },
        'spectrum_analyzer': {
            'frequency_range': (1e6, 26.5e9),
            'power_range': (-120.0, 30.0),
            'measurement_accuracy': 0.5,  # dB
            'noise_floor': -100.0
        },
        'signal_generator': {
            'frequency_range': (9e3, 6e9),
            'power_range': (-144.0, 19.0),
            'frequency_accuracy': 1e-9,
            'power_accuracy': 0.1
        },
        'power_meter': {
            'power_range': (-70.0, 44.0),
            'measurement_accuracy': 0.01,
            'default_reading': 25.5
        },
        'oscilloscope': {
            'bandwidth': 1e9,  # 1 GHz
            'sample_rate': 5e9,  # 5 GSa/s
            'memory_depth': 1000000,
            'channels': 4
        }
    }
    
    # Mock error simulation settings
    ERROR_SIMULATION = {
        'enable_random_errors': False,    # Set to True to simulate random errors
        'error_probability': 0.01,        # 1% chance of random errors
        'timeout_probability': 0.005,     # 0.5% chance of timeouts
        'communication_error_rate': 0.02  # 2% chance of communication errors
    }
    
    # Mock data generation settings
    DATA_GENERATION = {
        'waveform_samples': 1000,
        'spectrum_points': 401,
        'measurement_noise_level': 0.1,
        'realistic_variations': True
    }
    
    @classmethod
    def get_mock_timing(cls, operation: str) -> float:
        """
        Get mock timing for a specific operation
        
        Args:
            operation: Name of the operation
        
        Returns:
            Time in seconds for the mock operation
        """
        return cls.MOCK_TIMINGS.get(operation, 0.1)
    
    @classmethod
    def get_mock_response(cls, device: str, parameter: str = None) -> Any:
        """
        Get mock response for a device
        
        Args:
            device: Device name
            parameter: Specific parameter (optional)
        
        Returns:
            Mock response value
        """
        device_config = cls.MOCK_RESPONSES.get(device, {})
        if parameter:
            return device_config.get(parameter)
        return device_config
    
    @classmethod
    def should_simulate_error(cls, error_type: str = 'random') -> bool:
        """
        Check if an error should be simulated
        
        Args:
            error_type: Type of error to check
        
        Returns:
            True if error should be simulated
        """
        if not cls.ERROR_SIMULATION.get('enable_random_errors', False):
            return False
        
        import random
        probability = cls.ERROR_SIMULATION.get(f'{error_type}_probability', 0.01)
        return random.random() < probability
    
    @classmethod
    def get_configuration_summary(cls) -> Dict[str, Any]:
        """
        Get summary of mock configuration
        
        Returns:
            Dictionary containing configuration summary
        """
        return {
            'mode': 'MOCK',
            'optimization_status': 'ACTIVE',
            'timing_optimizations': {
                'scenario_loading': "{0}s (optimized from 50s)".format(cls.MOCK_TIMINGS['scenario_loading_time']),
                'scenario_start': "{0}s (optimized from 30s)".format(cls.MOCK_TIMINGS['scenario_start_time']),
                'rf_stabilization': "{0}s (optimized from 15s)".format(cls.MOCK_TIMINGS['rf_stabilization_time']),
                'instrument_reset': "{0}s (optimized from 15s)".format(cls.MOCK_TIMINGS['instrument_reset_time'])
            },
            'mock_devices': list(cls.MOCK_RESPONSES.keys()),
            'error_simulation': cls.ERROR_SIMULATION['enable_random_errors'],
            'total_mock_devices': len(cls.MOCK_RESPONSES)
        }
