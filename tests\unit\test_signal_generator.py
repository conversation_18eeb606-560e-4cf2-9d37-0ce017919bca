#!/usr/bin/env python3
"""
Unit Tests for N5172BSigGen Handler
Tests signal generator functionality and RF stabilization optimizations
"""

import unittest
import time
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_signal_generator import MockSignalGenerator
from tests.mocks.mock_resource_manager import MockResourceManager


class TestSignalGenerator(unittest.TestCase):
    """Test cases for N5172BSigGen handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = MockResourceManager()
        self.mock_siggen = MockSignalGenerator(self.mock_rm, "TCPIP0::************::INSTR")
        
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self, 'siggen'):
            try:
                self.siggen.close()
            except:
                pass
                
    def test_signal_generator_initialization(self):
        """Test signal generator initialization"""
        siggen = self.mock_siggen
        self.assertIsNotNone(siggen)
        self.assertEqual(siggen.address, "TCPIP0::************::INSTR")
        
    def test_frequency_setting(self):
        """Test frequency setting and retrieval"""
        siggen = self.mock_siggen
        
        # Test frequency setting
        test_freq = 1030e6  # 1030 MHz
        siggen.set_frequency(test_freq)
        
        # Verify frequency was set
        actual_freq = siggen.get_frequency()
        self.assertAlmostEqual(actual_freq, test_freq, delta=1000)  # 1kHz tolerance
        
    def test_power_setting(self):
        """Test power level setting and retrieval"""
        siggen = self.mock_siggen
        
        # Test power setting
        test_power = -10.0  # -10 dBm
        siggen.set_power(test_power)
        
        # Verify power was set
        actual_power = siggen.get_power()
        self.assertAlmostEqual(actual_power, test_power, delta=0.1)
        
    def test_rf_output_control(self):
        """Test RF output enable/disable"""
        siggen = self.mock_siggen
        
        # Test RF output enable
        siggen.set_rf_output(True)
        self.assertTrue(siggen.get_rf_output_state())
        
        # Test RF output disable
        siggen.set_rf_output(False)
        self.assertFalse(siggen.get_rf_output_state())
        
    def test_modulation_settings(self):
        """Test modulation configuration"""
        siggen = self.mock_siggen
        
        # Test AM modulation
        siggen.set_am_modulation(True, depth=50.0, freq=1000.0)
        am_state = siggen.get_am_modulation_state()
        self.assertTrue(am_state['enabled'])
        self.assertEqual(am_state['depth'], 50.0)
        self.assertEqual(am_state['frequency'], 1000.0)
        
        # Test FM modulation
        siggen.set_fm_modulation(True, deviation=10000.0, freq=1000.0)
        fm_state = siggen.get_fm_modulation_state()
        self.assertTrue(fm_state['enabled'])
        self.assertEqual(fm_state['deviation'], 10000.0)
        
    def test_pulse_modulation(self):
        """Test pulse modulation for transponder testing"""
        siggen = self.mock_siggen
        
        # Configure pulse modulation
        siggen.set_pulse_modulation(True)
        siggen.set_pulse_width(0.5e-6)  # 0.5 microseconds
        siggen.set_pulse_period(1000e-6)  # 1000 microseconds
        
        # Verify pulse settings
        pulse_state = siggen.get_pulse_modulation_state()
        self.assertTrue(pulse_state['enabled'])
        self.assertAlmostEqual(pulse_state['width'], 0.5e-6, delta=1e-9)
        self.assertAlmostEqual(pulse_state['period'], 1000e-6, delta=1e-9)
        
    def test_waveform_loading(self):
        """Test arbitrary waveform loading"""
        siggen = self.mock_siggen
        
        # Test waveform loading
        waveform_name = "DME_PULSE_TRAIN"
        waveform_data = [0.5, 1.0, 0.5, 0.0] * 100  # Simple pulse train
        
        result = siggen.load_waveform(waveform_name, waveform_data)
        self.assertTrue(result)
        
        # Verify waveform is loaded
        loaded_waveforms = siggen.get_loaded_waveforms()
        self.assertIn(waveform_name, loaded_waveforms)
        
    def test_frequency_sweep(self):
        """Test frequency sweep functionality"""
        siggen = self.mock_siggen
        
        # Configure frequency sweep
        start_freq = 1000e6  # 1000 MHz
        stop_freq = 1100e6   # 1100 MHz
        sweep_time = 1.0     # 1 second
        
        siggen.configure_frequency_sweep(start_freq, stop_freq, sweep_time)
        siggen.start_sweep()
        
        # Verify sweep is running
        self.assertTrue(siggen.is_sweep_running())
        
        # Stop sweep
        siggen.stop_sweep()
        self.assertFalse(siggen.is_sweep_running())
        
    def test_error_handling(self):
        """Test error handling and recovery"""
        siggen = self.mock_siggen
        
        # Test invalid frequency
        with self.assertRaises(ValueError):
            siggen.set_frequency(-1000)  # Negative frequency
            
        # Test invalid power
        with self.assertRaises(ValueError):
            siggen.set_power(50.0)  # Power too high
            
    def test_rf_stabilization_timing(self):
        """Test RF stabilization timing (HIGH PRIORITY optimization)"""
        siggen = self.mock_siggen
        
        # Test optimized RF stabilization
        start_time = time.time()
        
        # Set frequency and wait for stabilization
        siggen.set_frequency(1030e6)
        siggen.wait_for_rf_stabilization()  # Should use optimized 15s instead of 25s
        
        end_time = time.time()
        
        # Should complete faster than original 25s timing
        self.assertLess(end_time - start_time, 20.0)  # Allow margin for mock
        
    def test_phase_noise_measurement(self):
        """Test phase noise measurement capability"""
        siggen = self.mock_siggen
        
        # Configure for phase noise measurement
        siggen.set_frequency(1030e6)
        siggen.set_power(-10.0)
        siggen.set_rf_output(True)
        
        # Measure phase noise
        phase_noise = siggen.measure_phase_noise(offset_freq=10000)  # 10 kHz offset
        
        # Should return reasonable phase noise value
        self.assertIsInstance(phase_noise, float)
        self.assertLess(phase_noise, -80.0)  # Should be better than -80 dBc/Hz
        
    def test_spurious_emissions(self):
        """Test spurious emissions measurement"""
        siggen = self.mock_siggen
        
        # Configure signal generator
        siggen.set_frequency(1030e6)
        siggen.set_power(0.0)
        siggen.set_rf_output(True)
        
        # Measure spurious emissions
        spurious_data = siggen.measure_spurious_emissions()
        
        # Verify spurious data format
        self.assertIsInstance(spurious_data, dict)
        self.assertIn('frequencies', spurious_data)
        self.assertIn('levels', spurious_data)
        
    def test_iq_modulation(self):
        """Test I/Q modulation for complex signals"""
        siggen = self.mock_siggen
        
        # Configure I/Q modulation
        i_data = [1.0, 0.0, -1.0, 0.0] * 100
        q_data = [0.0, 1.0, 0.0, -1.0] * 100
        
        result = siggen.set_iq_modulation(i_data, q_data, sample_rate=1e6)
        self.assertTrue(result)
        
        # Enable I/Q modulation
        siggen.enable_iq_modulation(True)
        iq_state = siggen.get_iq_modulation_state()
        self.assertTrue(iq_state['enabled'])
        
    def test_signal_quality_metrics(self):
        """Test signal quality measurement"""
        siggen = self.mock_siggen
        
        # Configure signal
        siggen.set_frequency(1030e6)
        siggen.set_power(-10.0)
        siggen.set_rf_output(True)
        
        # Measure signal quality
        quality_metrics = siggen.get_signal_quality_metrics()
        
        # Verify metrics
        self.assertIn('evm', quality_metrics)  # Error Vector Magnitude
        self.assertIn('frequency_error', quality_metrics)
        self.assertIn('power_accuracy', quality_metrics)
        
    def test_optimization_validation(self):
        """Test that RF stabilization optimizations work correctly"""
        siggen = self.mock_siggen
        
        # Test optimized RF stabilization timing
        start_time = time.time()
        
        # Perform operations that would have used 25s delays (now 15s)
        siggen.set_frequency(1030e6)
        siggen.wait_for_rf_stabilization()  # Optimized delay
        siggen.set_frequency(1090e6)
        siggen.wait_for_rf_stabilization()  # Optimized delay
        
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 2 operations × 25s = 50s minimum
        # Optimized: 2 operations × 15s = 30s minimum
        self.assertLess(end_time - start_time, 35.0)  # Allow some margin


if __name__ == '__main__':
    unittest.main()
