"""
Mock implementation of B4500CPwrMeter.py
Provides mock functionality without external dependencies
"""

import time
import random
from typing import Any, List, Dict, Optional, Union


class MockB4500CPwrMeter:
    """Mock implementation of B4500CPwrMeter"""

    def __init__(self, *args, **kwargs):
        """Mock initialization"""
        self.connected = True
        self.initialized = True

        # Handle common initialization patterns
        if args and hasattr(args[0], 'logMessage'):
            self.resourceManager = args[0]
            self.resourceManager.logMessage(1, f"Mock B4500CPwrMeter initialized successfully")
        else:
            print(f"Mock B4500CPwrMeter initialized successfully")

    def getpwrmeasuremet(self):
        """Mock power measurement - returns comma-separated string"""
        import time
        import random
        time.sleep(0.01)  # Minimal delay

        # Return realistic power measurement data as comma-separated string
        # Ensure at least 4 values for sequences that expect index [3]
        power1 = -20.0 + random.uniform(-5.0, 5.0)
        power2 = -22.0 + random.uniform(-5.0, 5.0)
        power3 = -18.0 + random.uniform(-5.0, 5.0)
        power4 = -21.0 + random.uniform(-5.0, 5.0)

        result = f"{power1:.2f},{power2:.2f},{power3:.2f},{power4:.2f}"

        if hasattr(self, 'resourceManager') and hasattr(self.resourceManager, 'logMessage'):
            self.resourceManager.logMessage(1, f"Mock power measurement: {result}")
        else:
            print(f"Mock power measurement: {result}")

        return result
    
    def __getattr__(self, name):
        """Mock any missing methods dynamically"""
        def mock_method(*args, **kwargs):
            """Dynamic mock method"""
            time.sleep(0.001)  # Minimal realistic delay
            
            # Handle common method patterns
            if name.lower().startswith(('get', 'read', 'query', 'measure')):
                # Return realistic mock data for getters/queries
                if 'freq' in name.lower():
                    return 1030.0 + random.uniform(-1.0, 1.0)
                elif 'power' in name.lower() or 'pwr' in name.lower():
                    return -20.0 + random.uniform(-10.0, 10.0)
                elif 'voltage' in name.lower() or 'volt' in name.lower():
                    return 5.0 + random.uniform(-0.5, 0.5)
                elif 'current' in name.lower():
                    return 0.1 + random.uniform(-0.05, 0.05)
                elif 'temp' in name.lower():
                    return 25.0 + random.uniform(-5.0, 5.0)
                elif 'status' in name.lower():
                    return "OK" if random.random() > 0.1 else "BUSY"
                elif 'id' in name.lower():
                    return f"Mock B4500CPwrMeter,Model123,SN456789,FW1.0"
                elif 'time' in name.lower():
                    return 0.1 + random.uniform(-0.01, 0.01)
                elif 'width' in name.lower():
                    return 0.45 + random.uniform(-0.05, 0.05)
                elif 'delay' in name.lower():
                    return 3.0 + random.uniform(-0.2, 0.2)
                else:
                    return "OK"
            elif name.lower().startswith(('set', 'write', 'send', 'config', 'init')):
                # Setters/writers return success
                return True
            elif name.lower() in ['close', 'cleanup', 'disconnect', 'stop']:
                # Cleanup methods
                self.connected = False
                return True
            elif name.lower() in ['reset', 'restart']:
                # Reset methods with optimized timing
                time.sleep(0.5)  # Reduced reset time
                return True
            else:
                # Default return for unknown methods
                return True
                
        return mock_method

# Create a callable function that acts like the class
def B4500CPwrMeter(*args, **kwargs):
    """Factory function that returns B4500CPwrMeter class instance"""
    return MockB4500CPwrMeter(*args, **kwargs)

# Also make the class available directly
B4500CPwrMeter.MockB4500CPwrMeter = MockB4500CPwrMeter

# Additional common patterns for specific handlers
