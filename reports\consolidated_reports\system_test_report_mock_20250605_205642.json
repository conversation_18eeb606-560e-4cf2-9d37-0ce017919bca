{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T20:56:42.815379", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 9, "failed": 2, "errors": 0, "timeouts": 0, "success_rate": 81.81818181818183, "total_execution_time": 1699.572419166565, "start_time": "2025-06-05T20:28:23.242163", "end_time": "2025-06-05T20:56:42.814582"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1381540298461914, "start_time": "2025-06-05T20:28:23.243063", "end_time": "2025-06-05T20:28:23.381217", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "PASSED", "return_code": 0, "execution_time": 100.181631565094, "start_time": "2025-06-05T20:28:23.381888", "end_time": "2025-06-05T20:30:03.563521", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->__init__: Mock Spectrum Analyzer Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Transmit Frequency\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: SetUp SpecAnz, Capture Data\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:CENTer 1030 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:RESolution 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:VIDeo 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > SWE:TIME 1 s\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:SPAN 5 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :DISPlay:WINDow:TRACe:Y:RLEVel -10 dBm\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :TRAC:TYPE MAXHold\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:STATE ON\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:MAX\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Frq: 1029993643.882827 Lmt: 1.0\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Test_2.2.3..5: Transmit Frequency, DONE.\n[MOCK] TXD Python Lib: ate_rm.py->cleanup: Mock Resource Manager Closed.\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "PASSED", "return_code": 0, "execution_time": 35.67809867858887, "start_time": "2025-06-05T20:30:03.564045", "end_time": "2025-06-05T20:30:39.242145", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *** DO-385, ModeS Transmit Pulse Characteristics: Sect 2.2.3.8***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2.2.3.8: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2_2_3_8 - Start Pulse Measurements\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 8 pos edges, 8 neg edges\nPEdges:  8 [0.6156724792917029, 0.30215141361716213, 0.031184071819857162, 0.6295932450164711, 0.23760368109638896, 0.8298375201141476, 0.20444574251577852, 0.5788187907447404]\nNEdges:  8 [0.30076743945895323, 0.8624623611910011, 0.21491651325383232, 0.2717171252084095, 0.105572571167458, 0.8232234636825698, 0.6921004310263809, 0.40620026353916616]\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n*** MESSAGE SPAN *** -209472.21575253672\n*** PreAmble Span *** -378068.7981953139\n*** Data Span *** 168596.5824427772\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "PASSED", "return_code": 0, "execution_time": 552.5751466751099, "start_time": "2025-06-05T20:30:39.243005", "end_time": "2025-06-05T20:39:51.818155", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD qual_test_a350.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario qual_test_a350.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [46]\nRange:  [38.103557538818514]\nAltitude:  [32073]\nBearing:  [285.2303724033202]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [46]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [38.103557538818514]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [32073]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [285.2303724033202]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [65, 21, 25]\nRange:  [0.2742024474591154, 29.99881116504739, 18.22217179190607]\nAltitude:  [10393, 2071, 32968]\nBearing:  [207.60643706529024, 316.93503903639754, 216.40145650997547]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [65, 21, 25]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [0.2742024474591154, 29.99881116504739, 18.22217179190607]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [10393, 2071, 32968]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [207.60643706529024, 316.93503903639754, 216.40145650997547]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [12, 70, 75, 61, 60]\nRange:  [42.78776435878106, 10.769581167311493, 22.92473545310515, 47.62762400743454, 20.716332088882368]\nAltitude:  [7179, 4986, 25401, 11121, 13487]\nBearing:  [273.75619775482704, 229.17251514896716, 55.56204766633101, 71.89105622296184, 213.6671757978241]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [12, 70, 75, 61, 60]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [42.78776435878106, 10.769581167311493, 22.92473545310515, 47.62762400743454, 20.716332088882368]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [7179, 4986, 25401, 11121, 13487]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [273.75619775482704, 229.17251514896716, 55.56204766633101, 71.89105622296184, 213.6671757978241]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [34, 67, 74, 24]\nRange:  [20.37420447234925, 25.61731514325869, 30.051890088096282, 6.846711845345633]\nAltitude:  [32521, 30773, 37981, 23090]\nBearing:  [300.98787209297916, 329.967339097339, 171.04385976996562, 46.699151428769504]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [34, 67, 74, 24]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [20.37420447234925, 25.61731514325869, 30.051890088096282, 6.846711845345633]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [32521, 30773, 37981, 23090]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [300.98787209297916, 329.967339097339, 171.04385976996562, 46.699151428769504]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [44, 61, 28]\nRange:  [11.455170845782071, 25.044725667588054, 16.72795411642391]\nAltitude:  [31680, 33988, 23000]\nBearing:  [8.455587279334239, 21.871260623060287, 125.8891541756499]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [44, 61, 28]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [11.455170845782071, 25.044725667588054, 16.72795411642391]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [31680, 33988, 23000]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [8.455587279334239, 21.871260623060287, 125.8891541756499]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [16, 26, 43, 75, 89]\nRange:  [11.902507066471586, 34.317677273277276, 2.8428501327188105, 35.513014919942044, 31.882154693767383]\nAltitude:  [8455, 19548, 33362, 10214, 6107]\nBearing:  [266.41638849511486, 41.94858888594616, 68.04357883557532, 85.79160147100924, 290.0456427465374]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [16, 26, 43, 75, 89]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [11.902507066471586, 34.317677273277276, 2.8428501327188105, 35.513014919942044, 31.882154693767383]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [8455, 19548, 33362, 10214, 6107]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [266.41638849511486, 41.94858888594616, 68.04357883557532, 85.79160147100924, 290.0456427465374]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [97, 5]\nRange:  [40.93889499601685, 2.505039241573365]\nAltitude:  [18046, 32559]\nBearing:  [79.85858157542161, 139.04296029909307]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [97, 5]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [40.93889499601685, 2.505039241573365]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [18046, 32559]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [79.85858157542161, 139.04296029909307]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [44, 41, 99]\nRange:  [34.09736461487425, 28.9255285126238, 40.73941135563424]\nAltitude:  [19702, 7193, 2083]\nBearing:  [0.33333767743555676, 224.69106962520485, 272.96708920793907]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [44, 41, 99]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [34.09736461487425, 28.9255285126238, 40.73941135563424]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [19702, 7193, 2083]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [0.33333767743555676, 224.69106962520485, 272.96708920793907]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [68, 20, 98, 7, 23]\nRange:  [16.161804191519874, 17.063216181092734, 20.157430058984144, 22.65921489144032, 1.313820957204559]\nAltitude:  [3143, 6496, 37067, 26058, 8202]\nBearing:  [108.89025748031465, 56.073279110872974, 326.2921529483171, 64.09476978376384, 19.649419332296315]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [68, 20, 98, 7, 23]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [16.161804191519874, 17.063216181092734, 20.157430058984144, 22.65921489144032, 1.313820957204559]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [3143, 6496, 37067, 26058, 8202]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [108.89025748031465, 56.073279110872974, 326.2921529483171, 64.09476978376384, 19.649419332296315]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [6, 16, 23, 52, 29]\nRange:  [40.57510607185375, 24.014160296039073, 24.23663743660187, 2.565791135891754, 22.835398402456676]\nAltitude:  [3128, 23038, 23171, 25064, 12891]\nBearing:  [278.4412316492479, 77.00853864429878, 196.8629400406723, 28.019868389587344, 122.7817877087462]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [6, 16, 23, 52, 29]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [40.57510607185375, 24.014160296039073, 24.23663743660187, 2.565791135891754, 22.835398402456676]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [3128, 23038, 23171, 25064, 12891]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [278.4412316492479, 77.00853864429878, 196.8629400406723, 28.019868389587344, 122.7817877087462]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [98, 74, 50, 70, 82]\nRange:  [32.85642400499713, 40.53361167349257, 41.3098268915854, 49.74017707585542, 7.420145924075855]\nAltitude:  [8903, 29492, 10407, 779, 21069]\nBearing:  [191.0604326999737, 139.97912293922454, 30.603842255184603, 194.53806344058376, 148.07616259618618]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [98, 74, 50, 70, 82]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [32.85642400499713, 40.53361167349257, 41.3098268915854, 49.74017707585542, 7.420145924075855]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [8903, 29492, 10407, 779, 21069]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [191.0604326999737, 139.97912293922454, 30.603842255184603, 194.53806344058376, 148.07616259618618]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [25, 79, 77, 92]\nRange:  [7.892696862507787, 47.54211094052606, 15.224915600787408, 36.27732396671055]\nAltitude:  [643, 24704, 32570, 999]\nBearing:  [308.419730510855, 35.948754377304354, 60.922467421975355, 113.81537063079739]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [25, 79, 77, 92]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [7.892696862507787, 47.54211094052606, 15.224915600787408, 36.27732396671055]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [643, 24704, 32570, 999]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [308.419730510855, 35.948754377304354, 60.922467421975355, 113.81537063079739]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [46]\nRange:  [2.6063713616843676]\nAltitude:  [9524]\nBearing:  [81.46410523875774]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [46]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [2.6063713616843676]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [9524]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [81.46410523875774]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [63]\nRange:  [45.440403396453426]\nAltitude:  [5155]\nBearing:  [71.34878442438232]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [63]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [45.440403396453426]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [5155]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [71.34878442438232]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [6, 54, 46, 37]\nRange:  [14.280596126304202, 3.2074717354037494, 35.401285689460884, 39.503715156106]\nAltitude:  [6380, 529, 453, 27380]\nBearing:  [259.4797164576048, 305.7122044274738, 253.48453739679502, 96.42907108953118]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [6, 54, 46, 37]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [14.280596126304202, 3.2074717354037494, 35.401285689460884, 39.503715156106]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [6380, 529, 453, 27380]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [259.4797164576048, 305.7122044274738, 253.48453739679502, 96.42907108953118]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [33, 66, 98]\nRange:  [38.21568582074705, 8.96361307185199, 5.406886134866151]\nAltitude:  [14139, 39919, 27597]\nBearing:  [68.22137531592962, 125.58493902338859, 60.62184287103762]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [33, 66, 98]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [38.21568582074705, 8.96361307185199, 5.406886134866151]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [14139, 39919, 27597]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [68.22137531592962, 125.58493902338859, 60.62184287103762]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [80, 7]\nRange:  [37.42257986588325, 2.459060050112699]\nAltitude:  [20141, 14729]\nBearing:  [29.116374945714256, 74.54056923019319]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [80, 7]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [37.42257986588325, 2.459060050112699]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [20141, 14729]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [29.116374945714256, 74.54056923019319]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [12, 89, 53]\nRange:  [22.82467876399782, 9.329723659354345, 1.135221929581931]\nAltitude:  [8303, 29830, 39268]\nBearing:  [295.63899376568594, 3.27446475752589, 236.7493469178319]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [12, 89, 53]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [22.82467876399782, 9.329723659354345, 1.135221929581931]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [8303, 29830, 39268]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [295.63899376568594, 3.27446475752589, 236.7493469178319]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 309.5074951648712, "start_time": "2025-06-05T20:39:51.818783", "end_time": "2025-06-05T20:45:01.326280", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode C Reply Reception, Sect 2.2.4.4.2.1 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: *Test_2.2.4.4.2.1 Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 9600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario A\nStep1:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [6, 47, 15, 49, 27]\nRange:  [35.90427571968607, 22.277530991248756, 47.155050649933976, 23.31957435322598, 27.049411151600573]\nAltitude:  [11568, 27500, 10887, 23771, 13872]\nBearing:  [292.7411076413316, 350.0599354663006, 237.64672948197173, 30.15610933568834, 8.217196680630977]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario B\nStep1:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [3, 1]\nRange:  [48.300743775095235, 46.12313965445461]\nAltitude:  [30238, 7398]\nBearing:  [343.06570560467964, 93.8556647125727]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario C\nStep1:Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [26, 19, 12, 16, 15]\nRange:  [23.635315408402313, 24.302158262503966, 25.57551020849058, 20.168726424080635, 15.980798862747225]\nAltitude:  [26841, 31712, 10987, 34594, 17241]\nBearing:  [324.76891442056893, 220.74516045989589, 97.39610116352021, 298.2212142690818, 317.3923476999545]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario D\nStep1:Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [89, 39, 46]\nRange:  [31.57946476742806, 15.504278451754633, 39.74905889438109]\nAltitude:  [19658, 2418, 21200]\nBearing:  [214.79980176017298, 225.0720944285642, 269.71383795153497]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario E\nStep1:Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [9, 99, 91, 89, 1]\nRange:  [6.136979840035478, 19.57269438916099, 14.516497980919308, 34.71908873632303, 35.494966960393626]\nAltitude:  [36526, 24575, 26705, 8001, 8672]\nBearing:  [147.38172253078497, 217.6290290651014, 22.927582643591812, 319.70350867034955, 226.61869414227797]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario F\nStep1:Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [9, 61]\nRange:  [44.70327783951647, 23.506651459715115]\nAltitude:  [4952, 10382]\nBearing:  [199.33294812781344, 156.64579442873958]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 33400\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario A\nStep2:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [34, 83, 56, 80, 26]\nRange:  [46.97112362491653, 5.937570782677243, 45.47731566018536, 49.22248064087117, 8.661165778902424]\nAltitude:  [4383, 39960, 22163, 13545, 29737]\nBearing:  [142.62036712894832, 343.80928221872625, 314.0188314210339, 173.22399301777145, 287.1797244182943]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario B\nStep2:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [70, 74]\nRange:  [42.856849215663914, 43.99507156965803]\nAltitude:  [29110, 32335]\nBearing:  [67.09028898086694, 139.59285558813153]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario A\nStep3:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [68, 53, 63, 98, 17]\nRange:  [29.75459344590593, 45.13868342413945, 13.149516590205318, 46.704067057280156, 19.193743929687567]\nAltitude:  [17812, 735, 12522, 15519, 28697]\nBearing:  [8.640599954700686, 45.59543471894742, 107.92323152761863, 260.11233328019637, 67.93246233542921]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario B\nStep3:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [11, 41, 9]\nRange:  [17.645938090718673, 33.20077853433612, 10.330985900042894]\nAltitude:  [20044, 33711, 30866]\nBearing:  [176.6019186017068, 83.8068418588547, 130.39757324587447]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 6000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4:Test_385_Mode C Reply Reception - Scenario A\nStep4:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Garb.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Garb.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [40, 70, 13]\nRange:  [15.580892910346028, 21.119199976541324, 40.76079042386516]\nAltitude:  [18792, 38845, 35728]\nBearing:  [278.72230693254323, 106.54730276723558, 180.4651266898443]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1_Results: [0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4_Results: [1]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Done,Closing Session\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.34280610084534, "start_time": "2025-06-05T20:45:01.327137", "end_time": "2025-06-05T20:49:33.669946", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [9, 36, 35, 81]\nRange:  [40.491246294620645, 6.179182519118603, 12.096162403862337, 17.473177327948875]\nAltitude:  [16110, 7503, 16898, 38577]\nBearing:  [224.0441746378889, 174.796940921307, 203.5100778929185, 63.09128878792071]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [95]\nRange:  [29.973086375724407]\nAltitude:  [14680]\nBearing:  [52.52860571886414]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [32, 8, 87, 98, 9]\nRange:  [10.5049798496556, 3.8289117543546136, 19.744806631448675, 13.378891440191442, 42.59313584379982]\nAltitude:  [4074, 3233, 29062, 36107, 22535]\nBearing:  [194.5522354472662, 20.49004314195423, 288.79098726446205, 30.074571522297706, 33.503676146334186]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [11]\nRange:  [24.54937532709844]\nAltitude:  [6428]\nBearing:  [117.01247755962686]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [68, 1, 44, 69]\nRange:  [32.23032108780762, 27.882180025975785, 37.68498647346621, 29.065881825470385]\nAltitude:  [29731, 28700, 3420, 39139]\nBearing:  [241.40468571700774, 164.84062947455868, 61.08707104310403, 288.4537636758938]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [54]\nRange:  [17.764131456761838]\nAltitude:  [33989]\nBearing:  [4.682215752988332]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [69, 70, 66]\nRange:  [48.65866398270123, 45.38947644143352, 37.50672895718541]\nAltitude:  [19239, 39918, 18230]\nBearing:  [106.45344936960404, 104.98178655723177, 257.2943851211642]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [2, 84]\nRange:  [31.112898265728102, 43.920849580079185]\nAltitude:  [3011, 1428]\nBearing:  [337.5354448487053, 95.68218083644871]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [61, 28, 2]\nRange:  [22.311287434585005, 30.309694837402585, 3.669364370763267]\nAltitude:  [27971, 27090, 2261]\nBearing:  [103.46487984226177, 34.615877700759704, 315.6956903238]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [22, 88, 6]\nRange:  [24.668342129349917, 38.47893865502774, 40.90074626273534]\nAltitude:  [23105, 22744, 19703]\nBearing:  [286.7836497186783, 79.7411508254926, 238.89541229095724]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: [0, 1, 0, 1, 0, 0, 0, 0, 0, 0]\nStep1_Results:  [0, 1, 0, 1, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.12212014198303223, "start_time": "2025-06-05T20:49:33.670785", "end_time": "2025-06-05T20:49:33.792905", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "PASSED", "return_code": 0, "execution_time": 123.25642490386963, "start_time": "2025-06-05T20:49:33.793570", "end_time": "2025-06-05T20:51:37.049995", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *** DO-185E/385, Mode C Surveillance Initiation, Sect  2.2.4.6.2.1.2***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *Test_2.2.4.6.2.1.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 11000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step1:Test_385_Mode C Surveillance Initiation - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [46, 91, 80, 74, 12]\nRange:  [35.05539036001631, 10.665932646023807, 1.3189124734701907, 47.36238575172192, 1.645729981940458]\nAltitude:  [13358, 13711, 12346, 4402, 16727]\nBearing:  [280.47908520682864, 108.22374578021898, 326.3917300130869, 326.0972496971229, 270.132899451296]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step2:Test_385_Mode C Surveillance Initiation - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR ALTRPT,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR REPLY,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [80, 1, 41]\nRange:  [35.6129369542723, 41.79209204030391, 20.171911707884977]\nAltitude:  [13718, 14602, 2817]\nBearing:  [75.4812426872417, 261.7806824565056, 259.605488381455]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step3:Test_385_Mode C Surveillance Initiation - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR VERTICAL,0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [4, 66, 94, 92]\nRange:  [25.204687862813017, 27.55245372625893, 49.08958088722158, 13.188447790593688]\nAltitude:  [30625, 6505, 22786, 31272]\nBearing:  [173.95099226766786, 93.93568740236375, 59.28020630314991, 118.38371994415495]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Test_2.2.4.6.2.1.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nStep1_Results:  [0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.33040857315063, "start_time": "2025-06-05T20:51:37.050587", "end_time": "2025-06-05T20:56:09.380999", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [7, 34, 85, 98]\nRange:  [14.544759026283154, 4.1938818185837885, 43.240271909607, 44.442680162147816]\nAltitude:  [11415, 422, 6619, 25470]\nBearing:  [302.72858320136277, 272.0363789056267, 338.38244954730766, 112.91932590435042]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [3, 10, 67]\nRange:  [13.112186176481854, 26.40454871318464, 33.236693470074925]\nAltitude:  [38020, 8629, 24536]\nBearing:  [188.84687345365512, 279.07719596307106, 249.33914476737823]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [77]\nRange:  [6.542244212918947]\nAltitude:  [31475]\nBearing:  [247.70561000769905]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [28]\nRange:  [39.67583452215766]\nAltitude:  [17093]\nBearing:  [142.44979078795524]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [61, 84, 20]\nRange:  [13.27389692992313, 42.58452031079249, 17.397528528649804]\nAltitude:  [873, 17029, 26250]\nBearing:  [245.95386265369265, 111.37925502913778, 255.4610930019232]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [98, 5, 13, 94]\nRange:  [39.13389420536561, 19.64844809109475, 5.453666749447173, 3.797673994088785]\nAltitude:  [8300, 27371, 34809, 22982]\nBearing:  [139.42282234956815, 172.264812834573, 357.0503861205436, 122.4359080029429]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [80, 83, 71, 2]\nRange:  [16.890872693809495, 13.88114462368495, 38.91544135822866, 6.734895379181621]\nAltitude:  [9942, 23841, 27209, 5813]\nBearing:  [289.12675621371426, 47.85920152308992, 79.99051268189459, 332.02752535154207]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [97, 14, 45, 96, 94]\nRange:  [45.81739176842489, 17.498016078215784, 44.88181373756574, 6.160320859141688, 17.335508589128956]\nAltitude:  [29778, 35623, 7786, 28697, 6269]\nBearing:  [261.98483431897813, 85.68030809623153, 58.494124208115025, 47.35415596478492, 248.31094674443094]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [30, 19]\nRange:  [23.291316708208548, 5.9999757095486554]\nAltitude:  [39383, 28095]\nBearing:  [98.16587715924277, 299.00874738714896]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [15, 14, 12]\nRange:  [2.7830308550990113, 30.60282176245346, 6.102084842210065]\nAltitude:  [24542, 20858, 39600]\nBearing:  [143.5086166303723, 69.59964596446493, 38.068793534207934]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1_Results: [0, 0, 1, 1, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 33.26345491409302, "start_time": "2025-06-05T20:56:09.381863", "end_time": "2025-06-05T20:56:42.645319", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: *** DO-185E/385, Bearing Accuracy, Sect 2.2.4.6.4.2***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 232, in <module>\n    Test_2_2_4_6_4_2(rm, rgs, ARINC)\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 192, in Test_2_2_4_6_4_2\n    brg_avg[2],brg_max[2] = compute_BearingAccuracy(rm,ARINC)\n                            ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 108, in compute_BearingAccuracy\n    avg2 = avg2 + brg[2]\n                  ~~~^^^\nIndexError: list index out of range\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16759991645812988, "start_time": "2025-06-05T20:56:42.646453", "end_time": "2025-06-05T20:56:42.814053", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 178, in <module>\n    pwr_obj = B4500CPwrMeter(rm)\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 9, "failed": 2, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1699.572419166565, "average_sequence_time": 154.50658356059682, "sequences_per_hour": 23.29997801412841, "optimization_effectiveness": {"optimization_success_rate": 81.81818181818183, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 2, "failure_by_procedure": {"DO385": 2}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 2 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}