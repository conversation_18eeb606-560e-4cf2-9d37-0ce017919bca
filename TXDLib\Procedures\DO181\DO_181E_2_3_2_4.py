# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Side Lobe Supression, Section *******
             
             Step1: SLS Decoding: Interrogate the transponder with a standared Mode A
			 interrogation plus P2 RF signal level to be at MTL + 3 dB; P2 Level = P1 Level.
			 Verify that the reply ration is no more than 1%.
             
             Step2: SLS Dynamic Range Repeat Step 1 at RF Signal Levels of
			 -60dBm, -40dBm and -21dBm.
             
             Step3: SLS Pulse Ratio Repeat Step 1 at RF signal levels MTL+3 dB,
			 -50 dBm, and -21 dBm.  Set P2 level 9 dB below P1 level.  Verify that
			 the reply efficiency is at least 90%.
             
             Step4: Suppression Duration: Interrogate the transponder with a P1-P2 ATCRBS suppression pulse pair 
			 (2 microsecond spacing), followed after 50 microsecnds with a P1-P3 pair until the transponder reply
			 rate is below 1%.  The time interval between the leading edges of the P2 and P1 (of the P1 - P3 pair) 
			 is the supression duration.  repreat the procedure using P1-P3 (Mode C 21 microseconds) pulse pair 
			 interrogations..
              
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ReplyRatio_1percent - array of %replies at 1% or less
             ReplyRatio_90percent= array of %replies at 90% or greater


HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import B4500CPwrMeter

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_4(rm,atc,PathLoss):
    """ *** DO-181E, Side Lobe Supression: Sect ******* *** """

    rm.logMessage(2,"*** DO-181E, Side Lobe Supression: Sect ******* ***\r\n")
    
   
    #Results read by TestStand
    Power_Levels_1 = ['-73.0', '-60.0', '-40.0', '-21.0']
    Power_Levels_2 = ['-73.0', '-50.0',  '-21.0']
    ReplyRatio_1percent = [0.0,0.0,0.0,0.0]
    ReplyRatio_90percent= [0.0,0.0,0.0]

    #Adjust Power Levels by PathLoss
    Power_Levels_1[0] = str(float(Power_Levels_1[0]) + PathLoss)
    Power_Levels_1[1] = str(float(Power_Levels_1[1]) + PathLoss)
    Power_Levels_1[2] = str(float(Power_Levels_1[2]) + PathLoss)
    Power_Levels_1[3] = str(float(Power_Levels_1[3]) + PathLoss)
    Power_Levels_2[0] = str(float(Power_Levels_2[0]) + PathLoss)
    Power_Levels_2[1] = str(float(Power_Levels_2[1]) + PathLoss)
    Power_Levels_2[2] = str(float(Power_Levels_2[2]) + PathLoss)
     
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     

    #Turn SLS ON
    atc.gwrite(":ATC:XPDR:SLS ON")
    #atc.gwrite(":ATC:XPDR:PUL:P2POWER)
    time.sleep(5)
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    #Step1 and 2: Measure Reply Ratio at MTL+3
    #loop thru the four power levels
    rm.logMessage(0,"Test_2_3_2_4_Step1_2 - Start")   
    k=0
    for P in Power_Levels_1:
        cmd = ':ATC:XPDR:POW ' + P
        atc.gwrite(cmd)
        print(cmd)
        time.sleep(10)
        
        atc.waitforstatus()            
        
        replyrate = atc.getPercentReply(2)
            
        val = replyrate[1]         #ATCRBS Bottom
        
        # fix for erroneous reply rate
        count = 0
        while val == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            val = replyrate[1]
            count = count + 1
            
        #print result at this frequency
        print(("RESULT: Power %s, ReplyRate %f") % (P,val)) 
        ReplyRatio_1percent[k] = val
        msg_str = "Pwr: "+ P + " ReplyRate: " + str(val)
        rm.logMessage(0,"Test_2_3_2_4_Step1_2" + msg_str)    
        k=k+1
     
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     

    #Turn SLS ON, Set P2 Power -9 dB below P1
    atc.gwrite(":ATC:XPDR:SLS ON")
    atc.gwrite(":ATC:XPDR:PUL:P2POWER -9")
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    #Step3: Measure Reply Ratio at MTL+3
    #loop thru the four power levels
    rm.logMessage(0,"Test_2_3_2_4_Step3 - Start")   
    k=0
    for P in Power_Levels_2:
        cmd = ':ATC:XPDR:POW ' + P
        atc.gwrite(cmd)
        print(cmd)
        time.sleep(10)
        
        atc.waitforstatus()            
        
        replyrate = atc.getPercentReply(2)
            
        val = replyrate[1]         #ATCRBS Bottom
        
        # fix for erroneous reply rate
        count = 0
        while val == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            val = replyrate[1]
            count = count + 1
        
        #print result at this frequency
        print(("RESULT: Power %s, ReplyRate %f") % (P,val)) 
        ReplyRatio_90percent[k] = val
        msg_str = "Pwr: "+ P + " ReplyRate: " + str(val)
        rm.logMessage(0,"Test_2_3_2_4_Step3" + msg_str)    
        k=k+1

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    rm.logMessage(0,"Test_2_3_2_4 - Done: " + str(ReplyRatio_1percent) + str(ReplyRatio_90percent))   
    rm.logMessage(2,"Done, closing session")

    
    #concatinate lists.
    return ReplyRatio_1percent + ReplyRatio_90percent

##################################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

     #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_4(rm,atc_obj,12.0)
    
    atc_obj.close()

