#!/usr/bin/env python3
"""
Test RFBOB factory function implementation
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path to simulate mock mode
current_dir = Path.cwd()
mock_handlers_path = current_dir / "MockHandlers"
sys.path.insert(0, str(mock_handlers_path))

print("Testing RFBOB factory function...")

try:
    from TXDLib.Handlers import RFBOB
    print(f"RFBOB type: {type(RFBOB)}")
    
    # Test instantiation
    rf_obj = RFBOB()
    print(f"rf_obj type: {type(rf_obj)}")
    print("✅ SUCCESS: RFBOB instantiation working!")
    
except Exception as e:
    print(f"❌ FAILED: {e}")
    import traceback
    traceback.print_exc()
