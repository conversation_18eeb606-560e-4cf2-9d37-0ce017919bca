# -*- coding: utf-8 -*-
"""
Created on Fri Mar 20 9:02:30 2020

@author: E589493
         K. <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Interrogator Pulse Charectoristics, Section 2.2.3
             
             "Pulse Rise Time: The maximum time required for the pulse to rise
             from 10% to 90% of its maximum voltage amplitude shall not exceed 3.0 micro seconds

             Pulse Decay Time: The maximum time required for the pulse to fall
             from 90% to 10% of its maximum voltage amplitude shall not exceed 3.5 micro seconds

             Pulse Duration: The time between the points on the leading and trailing edges of 
             the pulse which are 50% of the maximum voltage and amplitude if the pulse, shall be 
             3.5 +/- 0.5 microseconds.

             Pulse Top: Between the point on the leading edge of the pusle which is 95% of
             maximum amplitude and the point on the trailing edge which 95% of the maximum 
             amplitude, the instataneous amplitude shall not fall below 95% of the maximum 
             voltage amplitude of the pulse.
             
INPUTS:      Top_Cable_Loss, Scope, ARINC server, ATC
OUTPUTS:     Results[8]: List of width, rise time, fall time, and top noise for both P1 and P2 (4 parameters for each pulse)

HISTORY:

02/26/2020   KF    Initial Release.
06/22/2020   AS    Added tvl statements, Added ARINC
03/10/2021   MRS   Updates for new handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers.D3054Scope import D3054Scope
       

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def measurePulseTopNoise(scope_obj):
    """ Measures the top noise of DME pulse.  Pass in edge position and pulse characteristics.
    returns percent of top noise """

    #Get max and min and calculate top noise % 
    vMax = scope_obj.measVMax()
    vMin = scope_obj.measVMin()
    return abs(((0.95 * vMax)-vMin)/(0.95 * vMax))


def init_DME_Standard(atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel 56X with VOR Pair 0 119.9MHz at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    ARINC.writeChannel(111.90)
    time.sleep(5)
    atc.DMEMode()
    #atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))
    
    
def modeX_Setup(scope_obj,timescale,threshold,trigger):
    """ Basic Scope setup for ModeX/DME Pulse. 
    RFBOB must be set up apriori."""
    
    scope_obj.Reset()
    #Display Chan 2 and 3 (only use 3 for this test)
    scope_obj.chanDisplay(1,0)
    scope_obj.chanDisplay(2,1)
    scope_obj.chanDisplay(3,1)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 100, "mV")
    scope_obj.voltDiv(2, 100, "mV")
    scope_obj.voltDiv(3, 20, "mV")
    scope_obj.voltDiv(4, 20, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    scope_obj.chanInvert(3,1)
    scope_obj.chanInvert(4,1)
    #Set Impedance of channels 3 and 4 to 50 Ohms
    scope_obj.setChannelTermination(3,50)
    scope_obj.setChannelTermination(4,50)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 3 is trigger
    scope_obj.trigRun()

def measurePulseChar(scope_obj, pEdge,nEdge, source = 1):
    """Function that measures PWidth, Rise, and fall for a given Pulse rising edge position.
    Fall time is taken by moving the cursor edgePPosition + PWidth. """

    #Measure Chan 3
    scope_obj.setMeasureSource(source)
    result = []

    scope_obj.timeScale(.5, "us")
    time.sleep(.1)
    scope_obj.setTimePosition(pEdge)
    time.sleep(.1)
    pwidth = scope_obj.measPWidth()
    #Estimate Width if result invalid
    if (pwidth > 1.0):
        pwidth = (nEdge - pEdge) - 0.1e-6
        print("Estimated Value: ",pwidth)
    result.append(pwidth)
    scope_obj.timeScale(.5, "us")
    time.sleep(.1)
    result.append(scope_obj.measRiseTime())
    scope_obj.setTimePosition(nEdge)
    time.sleep(.1)
    result.append(scope_obj.measFallTime())
    time.sleep(.1)

    return result  

def trigDetect(scope_obj):
    """ Function that triggers a given waveform and returns all detected edge positions based
    on trigger position. """
    
    scope_obj.trigRun()
    #Trigger Source Chan is set in setup (modex_setup)
    if scope_obj.trigSingle(2):
        return 1
    return 0
    
def getPulses(rm,scope_obj):
    #Set Up the OScope for Pulse Measurements   
    tmescale = 20.0            #scope time scale (usec/div), large enough to capture both pulses
    threshold = 20.0           #threshold (mV)
    trigger = 3                #use Chan 3 as trigger
    modeX_Setup(scope_obj,tmescale,threshold,trigger)
    time.sleep(1)
    
    #Results
    result = []
    
    #Get DME Pulse Parameters
    count = 0
    while ((count < 5) and (len(result) == 0)):    
        if trigDetect(scope_obj) == 1:
            # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
            PEdges,NEdges = scope_obj.digiEdgePos(threshold/1000.0,source = 3)
            #scope_obj.plotWave(3)               #comment this out when running from TestStand    
       
            #Number of Positive/Negative Edges
            rm.logMessage(0,("PEdges: %d,%s " % (len(PEdges),str(PEdges))))    
            rm.logMessage(0,("NEdges: %d,%s" % (len(NEdges),str(NEdges))))
            if len(PEdges) == 0:
                rm.logMessage(3, ("Test_2_3_2_3_1 - Error, No Edges Detected, Cnt: " + str(count)))
            else:
                #Loop thru Positive Edges and gather pulse data
                i=0
                for edge_pos in PEdges:
                    pulse_char = measurePulseChar(scope_obj,edge_pos,NEdges[i], 3)
                    rm.logMessage(0,"Pulse duration: " + str(pulse_char[0]))
                    rm.logMessage(0,"Pulse RiseTime: " + str(pulse_char[1]))
                    rm.logMessage(0,"Pulse FallTime: " + str(pulse_char[2]))           
                    result.append(pulse_char[0])           
                    result.append(pulse_char[1])           
                    result.append(pulse_char[2])
                    #Get Topnoise for Pulse
                    midpoint = edge_pos + (pulse_char[0]/2.0)
                    scope_obj.setTimePosition(midpoint)
                    time.sleep(.1)
                    scope_obj.timeScale(.5, 'us')
                    pulse_noise = measurePulseTopNoise(scope_obj)
                    rm.logMessage(0,"Pulse Noise: " + str(pulse_noise))
                    result.append(pulse_noise)
                    time.sleep(5) 
                    i=i+1
   
        #try 5 times      
        count = count + 1   
            
            
    return result

##############################################################################
################# MAIN     ##################################################
##############################################################################
def main():
    #Initialize Intrsumets

    rm = ate_rm()

    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    

    #Initialize Scope
    scope = D3054Scope(rm)
    scope.Reset()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Tune DME
    ARINC.writeChannel(111.90)
    #Put ATC in DME Mode.
    atc.DMEMode()

    
    #Setup Scope to trigger on a pulse pair
    result = getPulses(rm,scope)


    if(result != []):
        #Pulse 1 Results
        print("Pulse 1 Width: " + str(result[0]))
        print("Pulse 1 Rise Time: " + str(result[1]))
        print("Pulse 1 Fall Time: " + str(result[2]))
        print("Pulse 1 Top Noise: " + str(result[3]))
 
        #Pulse 2Results
        print("Pulse 2 Width: " + str(result[4]))
        print("Pulse 2 Rise Time: " + str(result[5]))
        print("Pulse 2 Fall Time: " + str(result[6]))
        print("Pulse 2 Top Noise: " + str(result[7]))



    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    scope.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()



if __name__ == "__main__":
    main()

