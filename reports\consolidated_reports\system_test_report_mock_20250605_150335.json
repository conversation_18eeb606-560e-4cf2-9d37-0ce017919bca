{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T15:03:35.529311", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 8, "failed": 3, "errors": 0, "timeouts": 0, "success_rate": 72.72727272727273, "total_execution_time": 1692.806203365326, "start_time": "2025-06-05T14:35:22.722018", "end_time": "2025-06-05T15:03:35.528222"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1725757122039795, "start_time": "2025-06-05T14:35:22.723083", "end_time": "2025-06-05T14:35:22.895658", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "PASSED", "return_code": 0, "execution_time": 100.22892928123474, "start_time": "2025-06-05T14:35:22.896489", "end_time": "2025-06-05T14:37:03.125421", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->__init__: Mock Spectrum Analyzer Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Transmit Frequency\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: SetUp SpecAnz, Capture Data\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:CENTer 1030 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:RESolution 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:VIDeo 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > SWE:TIME 1 s\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:SPAN 5 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :DISPlay:WINDow:TRACe:Y:RLEVel -10 dBm\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :TRAC:TYPE MAXHold\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:STATE ON\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:MAX\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Frq: 1030008282.9779483 Lmt: 1.0\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Test_2.2.3..5: Transmit Frequency, DONE.\n[MOCK] TXD Python Lib: ate_rm.py->cleanup: Mock Resource Manager Closed.\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 33.26187205314636, "start_time": "2025-06-05T14:37:03.127073", "end_time": "2025-06-05T14:37:36.388947", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *** DO-385, ModeS Transmit Pulse Characteristics: Sect 2.2.3.8***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2.2.3.8: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2_2_3_8 - Start Pulse Measurements\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 225, in <module>\n    res = Test_2_2_3_8(rm,rgs,scope_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 163, in Test_2_2_3_8\n    PEdges,NEdges = scope.digiEdgePos(50/1000.0,source = 1)\n    ^^^^^^^^^^^^^\nTypeError: cannot unpack non-iterable bool object\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "PASSED", "return_code": 0, "execution_time": 552.6585502624512, "start_time": "2025-06-05T14:37:36.390900", "end_time": "2025-06-05T14:46:49.049452", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD qual_test_a350.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario qual_test_a350.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [96, 92]\nRange:  [47.37194577186931, 29.760889170341038]\nAltitude:  [38343, 32510]\nBearing:  [181.32981822003367, 27.037530753097258]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [96, 92]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [47.37194577186931, 29.760889170341038]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [38343, 32510]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [181.32981822003367, 27.037530753097258]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [68, 59, 53, 75, 54]\nRange:  [17.086150237555557, 5.710691582599936, 23.43674782840187, 45.14018902412823, 2.4538463463449514]\nAltitude:  [4523, 14323, 17148, 26415, 8684]\nBearing:  [235.8588158454966, 11.42420057569935, 186.0970202306669, 91.03258262050245, 267.0822197838967]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [68, 59, 53, 75, 54]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [17.086150237555557, 5.710691582599936, 23.43674782840187, 45.14018902412823, 2.4538463463449514]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [4523, 14323, 17148, 26415, 8684]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [235.8588158454966, 11.42420057569935, 186.0970202306669, 91.03258262050245, 267.0822197838967]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [29, 84, 99, 5]\nRange:  [42.117113737249234, 31.237567938263872, 15.397890000071573, 15.781016997952213]\nAltitude:  [34643, 26925, 3659, 554]\nBearing:  [345.75985269683196, 63.74586794094785, 347.88354301078243, 328.51157506518246]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [29, 84, 99, 5]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [42.117113737249234, 31.237567938263872, 15.397890000071573, 15.781016997952213]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [34643, 26925, 3659, 554]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [345.75985269683196, 63.74586794094785, 347.88354301078243, 328.51157506518246]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [87, 24]\nRange:  [28.78912072530333, 37.08939206065093]\nAltitude:  [3971, 11673]\nBearing:  [213.13156336878384, 45.71853006685256]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [87, 24]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [28.78912072530333, 37.08939206065093]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [3971, 11673]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [213.13156336878384, 45.71853006685256]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [38, 4]\nRange:  [22.4457566046048, 5.266979112040692]\nAltitude:  [7097, 9030]\nBearing:  [54.823920793632624, 323.61537802000464]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [38, 4]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [22.4457566046048, 5.266979112040692]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [7097, 9030]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [54.823920793632624, 323.61537802000464]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [50, 22]\nRange:  [25.213263735931797, 47.632488323849664]\nAltitude:  [36215, 1478]\nBearing:  [334.4668181523116, 78.02443727040897]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [50, 22]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [25.213263735931797, 47.632488323849664]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [36215, 1478]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [334.4668181523116, 78.02443727040897]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [67]\nRange:  [27.404922471446568]\nAltitude:  [6322]\nBearing:  [120.621643058016]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [67]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [27.404922471446568]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [6322]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [120.621643058016]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [32, 45, 39, 52]\nRange:  [49.906619243709784, 9.419907806613015, 8.545160422427362, 6.116447122531893]\nAltitude:  [12589, 25328, 10893, 6356]\nBearing:  [222.4275837076909, 129.18999746991162, 100.83420931954142, 141.92874795338656]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [32, 45, 39, 52]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [49.906619243709784, 9.419907806613015, 8.545160422427362, 6.116447122531893]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [12589, 25328, 10893, 6356]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [222.4275837076909, 129.18999746991162, 100.83420931954142, 141.92874795338656]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [64, 30, 100, 59]\nRange:  [10.209183721845111, 35.31064958908475, 43.58134133752071, 7.872058801642595]\nAltitude:  [14466, 20484, 27820, 21064]\nBearing:  [15.360366730327812, 63.11107843155411, 253.6378280056759, 43.74830416359274]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [64, 30, 100, 59]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [10.209183721845111, 35.31064958908475, 43.58134133752071, 7.872058801642595]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [14466, 20484, 27820, 21064]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [15.360366730327812, 63.11107843155411, 253.6378280056759, 43.74830416359274]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [94]\nRange:  [31.852077274788147]\nAltitude:  [26214]\nBearing:  [322.7876184281004]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [94]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [31.852077274788147]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [26214]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [322.7876184281004]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [75, 81, 41, 59]\nRange:  [46.01272029670202, 27.67536148914606, 16.3389457188159, 20.25796567195165]\nAltitude:  [9003, 23393, 21789, 9979]\nBearing:  [6.311168739745314, 146.19188187138278, 127.62080554245, 239.8182453604388]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [75, 81, 41, 59]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [46.01272029670202, 27.67536148914606, 16.3389457188159, 20.25796567195165]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [9003, 23393, 21789, 9979]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [6.311168739745314, 146.19188187138278, 127.62080554245, 239.8182453604388]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [37]\nRange:  [14.616540884477885]\nAltitude:  [15257]\nBearing:  [47.04269016440546]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [37]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [14.616540884477885]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [15257]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [47.04269016440546]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [73]\nRange:  [1.540574642933673]\nAltitude:  [5452]\nBearing:  [88.24142946694184]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [73]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [1.540574642933673]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [5452]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [88.24142946694184]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [65, 68, 42, 31]\nRange:  [47.83348895772863, 26.504927667707847, 42.54469646690629, 1.3167715909380984]\nAltitude:  [38394, 38459, 10470, 22674]\nBearing:  [169.7383482981304, 29.847006299373035, 189.58504250103942, 180.1930463877502]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [65, 68, 42, 31]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [47.83348895772863, 26.504927667707847, 42.54469646690629, 1.3167715909380984]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [38394, 38459, 10470, 22674]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [169.7383482981304, 29.847006299373035, 189.58504250103942, 180.1930463877502]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [29, 1, 63, 13, 33]\nRange:  [12.571119566092715, 27.680444034371206, 46.157083671960415, 47.87758361079522, 6.668112945092402]\nAltitude:  [3710, 33376, 31541, 39147, 32219]\nBearing:  [53.53498320860696, 301.1112142809377, 29.41507468718965, 20.89747327155092, 76.06890051675958]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [29, 1, 63, 13, 33]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [12.571119566092715, 27.680444034371206, 46.157083671960415, 47.87758361079522, 6.668112945092402]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [3710, 33376, 31541, 39147, 32219]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [53.53498320860696, 301.1112142809377, 29.41507468718965, 20.89747327155092, 76.06890051675958]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [26, 47, 34]\nRange:  [2.0215624977727886, 6.98052383717303, 44.32354517533234]\nAltitude:  [24974, 26613, 14735]\nBearing:  [263.5527713332925, 232.9429576759114, 175.25010627886712]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [26, 47, 34]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [2.0215624977727886, 6.98052383717303, 44.32354517533234]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [24974, 26613, 14735]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [263.5527713332925, 232.9429576759114, 175.25010627886712]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [15, 1, 44, 67]\nRange:  [13.80287451811829, 12.214694318971492, 17.15671127285952, 9.036794359452394]\nAltitude:  [13525, 27107, 9210, 32096]\nBearing:  [302.2896824101666, 263.68046686175484, 305.02989198506896, 182.8456337520036]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [15, 1, 44, 67]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [13.80287451811829, 12.214694318971492, 17.15671127285952, 9.036794359452394]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [13525, 27107, 9210, 32096]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [302.2896824101666, 263.68046686175484, 305.02989198506896, 182.8456337520036]\nNumber of Intruders True\nIntruder Return String:  True\nIntruders:  [26, 66, 30, 37, 73]\nRange:  [8.801544675546136, 5.994261106269139, 42.311106431938164, 15.98305134592049, 23.001647629393002]\nAltitude:  [20459, 17599, 23420, 535, 34290]\nBearing:  [311.90377873267505, 210.48520776213974, 172.64101016209946, 216.5324705785823, 193.1814941469722]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [26, 66, 30, 37, 73]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [8.801544675546136, 5.994261106269139, 42.311106431938164, 15.98305134592049, 23.001647629393002]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [20459, 17599, 23420, 535, 34290]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [311.90377873267505, 210.48520776213974, 172.64101016209946, 216.5324705785823, 193.1814941469722]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 310.49030447006226, "start_time": "2025-06-05T14:46:49.050895", "end_time": "2025-06-05T14:51:59.541201", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode C Reply Reception, Sect 2.2.4.4.2.1 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: *Test_2.2.4.4.2.1 Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 9600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario A\nStep1:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [4, 84, 26]\nRange:  [30.75697585002528, 29.171677137355704, 44.01151594463366]\nAltitude:  [18850, 8952, 16566]\nBearing:  [251.91412992815006, 70.03947163696456, 346.1264201753623]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario B\nStep1:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [70, 33, 91]\nRange:  [26.689299964340798, 21.0381165404682, 41.472041007372106]\nAltitude:  [3411, 39956, 31757]\nBearing:  [175.54066208211876, 80.84692347855645, 182.0200213325315]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario C\nStep1:Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [19, 82]\nRange:  [35.324666858801656, 12.621901584934475]\nAltitude:  [32077, 29396]\nBearing:  [42.123206964616024, 41.729359286174805]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario D\nStep1:Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [81]\nRange:  [45.897483676199194]\nAltitude:  [15802]\nBearing:  [196.29259065466192]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario E\nStep1:Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [35]\nRange:  [45.390558620982354]\nAltitude:  [2288]\nBearing:  [65.40255216704885]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario F\nStep1:Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [1, 83, 39, 32, 36]\nRange:  [32.68840188370496, 11.222870317374124, 22.982891935031475, 33.58360146497387, 29.022878700041083]\nAltitude:  [1852, 36967, 28174, 12639, 6652]\nBearing:  [138.08497757720608, 149.8033177577783, 115.20454273034004, 211.8750161319546, 269.77839660389213]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 33400\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario A\nStep2:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [28, 81, 43, 100, 91]\nRange:  [26.282159466846778, 0.5983565853575346, 44.22180494494705, 47.4192447008569, 18.920109093759073]\nAltitude:  [6505, 2894, 39324, 31300, 23070]\nBearing:  [35.851633892899265, 98.82507278912554, 229.51938484041332, 25.572422189385435, 11.884211909882278]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario B\nStep2:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [5, 42]\nRange:  [10.51730683591306, 7.395301202304233]\nAltitude:  [9894, 24474]\nBearing:  [112.75292438177581, 158.99033461459524]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario A\nStep3:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [78, 55, 75]\nRange:  [13.278189921471666, 27.714153448648005, 21.53181814425635]\nAltitude:  [23125, 28479, 12443]\nBearing:  [58.40733121209633, 189.80127992706082, 203.00124040190795]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario B\nStep3:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [98, 81]\nRange:  [0.7778659631624718, 48.364240625191364]\nAltitude:  [23350, 16164]\nBearing:  [54.87966049518592, 93.976792563059]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 6000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4:Test_385_Mode C Reply Reception - Scenario A\nStep4:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Garb.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Garb.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [32, 95, 50, 26]\nRange:  [26.59987418697965, 22.807455096504892, 0.6549521001869174, 29.030014447632666]\nAltitude:  [6950, 31930, 4018, 17882]\nBearing:  [182.88514478232196, 10.300874764175706, 207.1873385571761, 107.91191612763802]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1_Results: [0, 0, 0, 1, 0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3_Results: [0, 1]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4_Results: [0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Done,Closing Session\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.4412806034088, "start_time": "2025-06-05T14:51:59.542572", "end_time": "2025-06-05T14:56:31.983855", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [29, 87, 90, 41, 91]\nRange:  [41.52766633232772, 5.040011511422859, 37.343376425953906, 28.309282536660202, 45.69001851411389]\nAltitude:  [10321, 21180, 24003, 37780, 8476]\nBearing:  [27.948709027632766, 209.05745717809185, 264.8420370721095, 296.2757267818818, 43.343257169692876]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [38, 8]\nRange:  [23.131120886630264, 21.250132052797404]\nAltitude:  [643, 36522]\nBearing:  [314.4980965037901, 359.13948405019136]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [35]\nRange:  [46.48033415550377]\nAltitude:  [6455]\nBearing:  [30.027211913053495]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [28, 71, 45, 67]\nRange:  [1.0351659743297437, 6.209737123994125, 17.98744690679773, 4.01815384754034]\nAltitude:  [31206, 34227, 33541, 27832]\nBearing:  [205.9216869061276, 207.6646262807698, 98.03686106258421, 56.31938019172355]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [5, 55, 60, 31, 61]\nRange:  [29.40444827664178, 17.597605507157336, 1.8438808074539015, 39.67600646685694, 30.42468242046873]\nAltitude:  [21469, 17422, 16492, 17746, 13814]\nBearing:  [260.84963102301367, 335.50313492962596, 85.0332024596791, 40.34755715419052, 257.0395118191947]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [59, 96, 79, 32, 39]\nRange:  [24.903022331443797, 18.59334622814282, 35.31733667548343, 26.819455365536488, 19.333608402949032]\nAltitude:  [2702, 17761, 26034, 12077, 5835]\nBearing:  [26.563258752929602, 283.2549465190308, 90.18552388397353, 285.24133928646495, 24.722212063943214]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [45, 47]\nRange:  [30.863410860706313, 33.085871501087425]\nAltitude:  [32345, 750]\nBearing:  [234.02262967533375, 109.48378552894287]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [19]\nRange:  [12.791252805137281]\nAltitude:  [32824]\nBearing:  [63.72170170939194]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [11, 37]\nRange:  [35.70882987802994, 36.604493316807456]\nAltitude:  [35268, 33722]\nBearing:  [71.58824592675737, 237.6767593991861]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [40, 36, 32, 25]\nRange:  [13.160699134340376, 7.172102814689662, 3.0016112535310837, 32.84191850785253]\nAltitude:  [38892, 35638, 15306, 28649]\nBearing:  [79.40405210622095, 252.62196944067583, 310.8426514760126, 235.31781370941832]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: [0, 0, 1, 0, 0, 0, 0, 0, 0, 0]\nStep1_Results:  [0, 0, 1, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.424452543258667, "start_time": "2025-06-05T14:56:31.985191", "end_time": "2025-06-05T14:56:32.409643", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "PASSED", "return_code": 0, "execution_time": 123.29054093360901, "start_time": "2025-06-05T14:56:32.410972", "end_time": "2025-06-05T14:58:35.701514", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *** DO-185E/385, Mode C Surveillance Initiation, Sect  2.2.4.6.2.1.2***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *Test_2.2.4.6.2.1.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 11000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step1:Test_385_Mode C Surveillance Initiation - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\nIntruders:  [34, 66]\nRange:  [11.05860710335858, 12.14782721413361]\nAltitude:  [6340, 35551]\nBearing:  [42.86983620974699, 31.99108284796036]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step2:Test_385_Mode C Surveillance Initiation - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR ALTRPT,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR REPLY,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [9, 9, 97]\nRange:  [39.437088183726395, 27.984536731391668, 35.80568181058709]\nAltitude:  [1750, 21744, 25487]\nBearing:  [39.77283064942225, 334.2502616250511, 37.55640433976922]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step3:Test_385_Mode C Surveillance Initiation - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR VERTICAL,0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [56, 15, 32, 77, 63]\nRange:  [30.09132052121528, 37.3231168051873, 33.02513975823341, 18.20903531356476, 41.69668165532923]\nAltitude:  [15069, 35896, 36508, 28481, 29181]\nBearing:  [236.2995459965721, 181.96394182867093, 353.47626690385687, 321.77758500435306, 21.465304676128262]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Test_2.2.4.6.2.1.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nStep1_Results:  [0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.4245104789734, "start_time": "2025-06-05T14:58:35.702936", "end_time": "2025-06-05T15:03:08.127447", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [39, 71, 11]\nRange:  [45.83336638684219, 47.379386462652796, 23.277273641331863]\nAltitude:  [28771, 20666, 11250]\nBearing:  [103.56936200233739, 342.7494300122499, 299.34848102126506]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [75, 3, 30, 59]\nRange:  [14.945879370772753, 46.128961424212164, 8.525246360700367, 34.42322337239507]\nAltitude:  [10610, 12472, 34145, 23271]\nBearing:  [110.58288138670477, 145.15068765878755, 7.190357752565593, 193.05091645171458]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [38, 46, 48, 81, 84]\nRange:  [10.47837308373072, 43.601231333252485, 2.2352167108946497, 24.28256748679094, 5.5841880858604585]\nAltitude:  [20630, 20066, 21410, 6757, 5260]\nBearing:  [154.17422049239383, 243.72902036825477, 79.4941630196915, 161.71064724933936, 110.95080497316856]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [54]\nRange:  [22.569275085705065]\nAltitude:  [17147]\nBearing:  [318.19363025158736]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [75, 96, 9, 60]\nRange:  [40.00343746467665, 46.73891855462669, 31.86448724977532, 42.90234073475304]\nAltitude:  [25107, 27444, 21484, 257]\nBearing:  [201.4597551603436, 228.63844867115588, 258.4655152472805, 326.092524991251]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [50, 10, 45]\nRange:  [49.14996354560092, 31.919356651768677, 23.499031834555527]\nAltitude:  [20633, 32688, 31477]\nBearing:  [280.91895486702174, 323.86286446392256, 8.35510283549346]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 1 intruders\nIntruders:  [64]\nRange:  [41.98738120585219]\nAltitude:  [16716]\nBearing:  [197.95542888788532]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [61, 44, 2, 96, 89]\nRange:  [39.00663296281417, 41.28998065385561, 0.5948718426512012, 28.853584997297222, 43.86558758394339]\nAltitude:  [8787, 18024, 27913, 21403, 15529]\nBearing:  [281.16057060422025, 113.217565669841, 227.29318219318156, 232.18911046209624, 253.3563537603988]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [52, 42, 63]\nRange:  [21.65401942602308, 1.6559682802839593, 33.84381510326506]\nAltitude:  [2534, 24527, 3503]\nBearing:  [186.9051675443818, 78.81772704825327, 323.7497405226181]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [72, 44, 23, 62]\nRange:  [27.946213563029552, 24.888144226990384, 10.344176605935623, 44.159618401797346]\nAltitude:  [2106, 28149, 29902, 13900]\nBearing:  [127.28741481927041, 3.822588622135612, 345.67612650470534, 134.49022690990844]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1_Results: [0, 0, 0, 1, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 27.21966528892517, "start_time": "2025-06-05T15:03:08.128858", "end_time": "2025-06-05T15:03:35.348524", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: *** DO-185E/385, Bearing Accuracy, Sect 2.2.4.6.4.2***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 2 intruders\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 232, in <module>\n    Test_2_2_4_6_4_2(rm, rgs, ARINC)\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 192, in Test_2_2_4_6_4_2\n    brg_avg[2],brg_max[2] = compute_BearingAccuracy(rm,ARINC)\n                            ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 108, in compute_BearingAccuracy\n    avg2 = avg2 + brg[2]\n                  ~~~^^^\nIndexError: list index out of range\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1767566204071045, "start_time": "2025-06-05T15:03:35.350700", "end_time": "2025-06-05T15:03:35.527456", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 178, in <module>\n    pwr_obj = B4500CPwrMeter(rm)\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 8, "failed": 3, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1692.806203365326, "average_sequence_time": 153.89147303321144, "sequences_per_hour": 23.393108981568336, "optimization_effectiveness": {"optimization_success_rate": 72.72727272727273, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 3, "failure_by_procedure": {"DO385": 3}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 3 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}