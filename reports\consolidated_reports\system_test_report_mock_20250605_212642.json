{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T21:26:42.557291", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 9, "failed": 2, "errors": 0, "timeouts": 0, "success_rate": 81.81818181818183, "total_execution_time": 1738.3008823394775, "start_time": "2025-06-05T20:57:44.255286", "end_time": "2025-06-05T21:26:42.556168"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.15634822845458984, "start_time": "2025-06-05T20:57:44.256081", "end_time": "2025-06-05T20:57:44.412429", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "PASSED", "return_code": 0, "execution_time": 100.1886055469513, "start_time": "2025-06-05T20:57:44.413139", "end_time": "2025-06-05T20:59:24.601746", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->__init__: Mock Spectrum Analyzer Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Transmit Frequency\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: SetUp SpecAnz, Capture Data\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:CENTer 1030 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:RESolution 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:VIDeo 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > SWE:TIME 1 s\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:SPAN 5 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :DISPlay:WINDow:TRACe:Y:RLEVel -10 dBm\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :TRAC:TYPE MAXHold\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:STATE ON\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:MAX\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Frq: 1030009346.7504808 Lmt: 1.0\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Test_2.2.3..5: Transmit Frequency, DONE.\n[MOCK] TXD Python Lib: ate_rm.py->cleanup: Mock Resource Manager Closed.\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "PASSED", "return_code": 0, "execution_time": 37.23929977416992, "start_time": "2025-06-05T20:59:24.602586", "end_time": "2025-06-05T21:00:01.841887", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *** DO-385, ModeS Transmit Pulse Characteristics: Sect 2.2.3.8***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2.2.3.8: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2_2_3_8 - Start Pulse Measurements\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 13 pos edges, 13 neg edges\nPEdges:  13 [0.5873611983043667, 0.6914992450736094, 0.9302109487339268, 0.6148276692086506, 0.6114407541587834, 0.880024601808469, 0.2133249034894612, 0.4822884334727804, 0.9171594837058397, 0.6158825911097064, 0.5067646582896942, 0.800664184526438, 0.15831375293130423]\nNEdges:  13 [0.4352193584101387, 0.02559312368052824, 0.06555758129090006, 0.9845785210457807, 0.9041448713001382, 0.16421987222752676, 0.4590780132415885, 0.23435438473915637, 0.36121428492914687, 0.43672228578593875, 0.8338520727070254, 0.29459759519269657, 0.32908500739128277]\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n*** MESSAGE SPAN *** -258276.19091308396\n*** PreAmble Span *** 24079.555854416678\n*** Data Span *** -282355.74676750065\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "PASSED", "return_code": 0, "execution_time": 552.7275559902191, "start_time": "2025-06-05T21:00:01.842470", "end_time": "2025-06-05T21:09:14.570029", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD qual_test_a350.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario qual_test_a350.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [56, 76, 40, 30, 80]\nRange:  [6.854733860911521, 31.12595335555862, 15.376807528039777, 42.70196068567476, 24.605038042255266]\nAltitude:  [1780, 10774, 37765, 10403, 447]\nBearing:  [122.1426016625263, 138.9325052139049, 16.451317369008812, 17.736916389744014, 76.48835943511996]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [56, 76, 40, 30, 80]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [6.854733860911521, 31.12595335555862, 15.376807528039777, 42.70196068567476, 24.605038042255266]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [1780, 10774, 37765, 10403, 447]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [122.1426016625263, 138.9325052139049, 16.451317369008812, 17.736916389744014, 76.48835943511996]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [19, 26, 100, 80, 14]\nRange:  [24.79292492898775, 34.62220132061966, 10.356079718652161, 24.478999945214756, 30.199580107617958]\nAltitude:  [28362, 37447, 4381, 37655, 13689]\nBearing:  [352.07879316086775, 217.7011105069427, 256.77223837850255, 209.58366155346582, 102.11648595049604]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [19, 26, 100, 80, 14]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [24.79292492898775, 34.62220132061966, 10.356079718652161, 24.478999945214756, 30.199580107617958]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [28362, 37447, 4381, 37655, 13689]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [352.07879316086775, 217.7011105069427, 256.77223837850255, 209.58366155346582, 102.11648595049604]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [44, 97, 32, 31]\nRange:  [43.7169705565388, 26.991846711034405, 6.814563506203836, 23.78021509881199]\nAltitude:  [34722, 36260, 33068, 8300]\nBearing:  [16.710493930492344, 139.34681650143116, 254.924409896409, 12.424607483102438]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [44, 97, 32, 31]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [43.7169705565388, 26.991846711034405, 6.814563506203836, 23.78021509881199]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [34722, 36260, 33068, 8300]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [16.710493930492344, 139.34681650143116, 254.924409896409, 12.424607483102438]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [13, 83, 21, 63]\nRange:  [4.967502371284571, 34.54435305495394, 2.9151864906497957, 46.27635730998598]\nAltitude:  [32923, 39494, 1545, 37285]\nBearing:  [247.62275384237313, 330.911261860936, 332.4042013982594, 160.96557868903628]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [13, 83, 21, 63]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [4.967502371284571, 34.54435305495394, 2.9151864906497957, 46.27635730998598]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [32923, 39494, 1545, 37285]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [247.62275384237313, 330.911261860936, 332.4042013982594, 160.96557868903628]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [38, 56, 26, 24, 67]\nRange:  [18.683987928690833, 15.606371373444421, 41.0912686048358, 0.3991554715041312, 0.674693997567305]\nAltitude:  [15858, 2134, 5505, 22382, 6300]\nBearing:  [78.86353836338169, 260.7684703014837, 349.3901893501479, 38.76695156471052, 29.037603795204653]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [38, 56, 26, 24, 67]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [18.683987928690833, 15.606371373444421, 41.0912686048358, 0.3991554715041312, 0.674693997567305]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [15858, 2134, 5505, 22382, 6300]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [78.86353836338169, 260.7684703014837, 349.3901893501479, 38.76695156471052, 29.037603795204653]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [62, 18, 69]\nRange:  [39.294360183916375, 27.38812985283787, 28.217784773509138]\nAltitude:  [31731, 20654, 22011]\nBearing:  [93.54535438111661, 17.976408766262058, 228.80092471409785]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [62, 18, 69]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [39.294360183916375, 27.38812985283787, 28.217784773509138]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [31731, 20654, 22011]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [93.54535438111661, 17.976408766262058, 228.80092471409785]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [33, 18, 31, 71, 66]\nRange:  [46.268743026691524, 7.476226826710219, 38.47431161972866, 37.26247433311639, 10.330788455797707]\nAltitude:  [25529, 18864, 21437, 13706, 27596]\nBearing:  [20.360198839188644, 279.9233488715086, 186.80067105617147, 117.47330466567058, 319.4786172302426]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [33, 18, 31, 71, 66]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [46.268743026691524, 7.476226826710219, 38.47431161972866, 37.26247433311639, 10.330788455797707]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [25529, 18864, 21437, 13706, 27596]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [20.360198839188644, 279.9233488715086, 186.80067105617147, 117.47330466567058, 319.4786172302426]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [43, 44, 25]\nRange:  [6.83636861806452, 11.179175303103673, 2.1969921100324754]\nAltitude:  [13599, 766, 14927]\nBearing:  [119.67039402486603, 82.52504287649678, 356.90150545728784]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [43, 44, 25]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [6.83636861806452, 11.179175303103673, 2.1969921100324754]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [13599, 766, 14927]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [119.67039402486603, 82.52504287649678, 356.90150545728784]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [71, 8, 12]\nRange:  [48.671906384672916, 35.110175653582445, 48.076822693344845]\nAltitude:  [18302, 22070, 38634]\nBearing:  [109.2965122127489, 162.3887459457536, 202.11230796175246]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [71, 8, 12]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [48.671906384672916, 35.110175653582445, 48.076822693344845]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [18302, 22070, 38634]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [109.2965122127489, 162.3887459457536, 202.11230796175246]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [1, 11, 91, 85]\nRange:  [24.04122225644418, 49.592194613502485, 33.72825882083343, 27.324711262284566]\nAltitude:  [4499, 34196, 14728, 5666]\nBearing:  [301.1937566464941, 130.36522700888546, 202.59446403256206, 294.48700573160846]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [1, 11, 91, 85]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [24.04122225644418, 49.592194613502485, 33.72825882083343, 27.324711262284566]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [4499, 34196, 14728, 5666]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [301.1937566464941, 130.36522700888546, 202.59446403256206, 294.48700573160846]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [28, 74, 19, 56]\nRange:  [38.4269379529868, 16.795487423065246, 33.78339925535185, 31.28745450092903]\nAltitude:  [11451, 25915, 32659, 1645]\nBearing:  [294.3578660177855, 267.4191946945411, 91.5213004152325, 165.92856607455562]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [28, 74, 19, 56]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [38.4269379529868, 16.795487423065246, 33.78339925535185, 31.28745450092903]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [11451, 25915, 32659, 1645]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [294.3578660177855, 267.4191946945411, 91.5213004152325, 165.92856607455562]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [29, 98, 16, 43]\nRange:  [19.106859721550546, 19.47379368360337, 12.00380024307856, 5.484749533315752]\nAltitude:  [1885, 33839, 25382, 7040]\nBearing:  [1.310256345201255, 305.9709039696948, 145.9880854256106, 145.3054567362038]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [29, 98, 16, 43]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [19.106859721550546, 19.47379368360337, 12.00380024307856, 5.484749533315752]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [1885, 33839, 25382, 7040]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [1.310256345201255, 305.9709039696948, 145.9880854256106, 145.3054567362038]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [68, 22, 74, 54, 32]\nRange:  [8.958827528930197, 17.2418901782728, 20.71950381855646, 35.1417271343842, 33.45343265576779]\nAltitude:  [19551, 5353, 33972, 13866, 20442]\nBearing:  [216.29888692941938, 234.43858468718196, 214.02650559102258, 70.99978997822397, 135.52279296172685]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [68, 22, 74, 54, 32]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [8.958827528930197, 17.2418901782728, 20.71950381855646, 35.1417271343842, 33.45343265576779]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [19551, 5353, 33972, 13866, 20442]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [216.29888692941938, 234.43858468718196, 214.02650559102258, 70.99978997822397, 135.52279296172685]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [92, 8, 96, 36, 85]\nRange:  [49.531014664437365, 1.9097676851752066, 8.730301789176071, 39.12140478238215, 26.669724211916186]\nAltitude:  [28059, 37747, 12420, 9599, 9274]\nBearing:  [67.62235285055522, 266.834090166174, 294.125605125461, 137.57626071171103, 351.1413684701119]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [92, 8, 96, 36, 85]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [49.531014664437365, 1.9097676851752066, 8.730301789176071, 39.12140478238215, 26.669724211916186]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [28059, 37747, 12420, 9599, 9274]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [67.62235285055522, 266.834090166174, 294.125605125461, 137.57626071171103, 351.1413684701119]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [80, 6, 93, 6, 27]\nRange:  [26.784418178180363, 3.271937784325951, 23.155794678982407, 41.27014705457157, 28.445082853885147]\nAltitude:  [2848, 9506, 20361, 12782, 15526]\nBearing:  [271.26175873952974, 54.060978174796524, 106.64199702103284, 353.83815294665135, 58.149918570984006]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [80, 6, 93, 6, 27]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [26.784418178180363, 3.271937784325951, 23.155794678982407, 41.27014705457157, 28.445082853885147]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [2848, 9506, 20361, 12782, 15526]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [271.26175873952974, 54.060978174796524, 106.64199702103284, 353.83815294665135, 58.149918570984006]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [52, 10, 82]\nRange:  [15.953954191350045, 43.838759657496546, 10.830930761682488]\nAltitude:  [14320, 6529, 19108]\nBearing:  [85.70576366567794, 206.48621594052884, 204.36746025928716]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [52, 10, 82]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [15.953954191350045, 43.838759657496546, 10.830930761682488]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [14320, 6529, 19108]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [85.70576366567794, 206.48621594052884, 204.36746025928716]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [84, 17, 9]\nRange:  [19.647075724256723, 18.22392073708289, 39.70973495633788]\nAltitude:  [6376, 39532, 29646]\nBearing:  [169.6874961694562, 46.802108535946765, 275.564179368266]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [84, 17, 9]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [19.647075724256723, 18.22392073708289, 39.70973495633788]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [6376, 39532, 29646]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [169.6874961694562, 46.802108535946765, 275.564179368266]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [73, 88, 84, 91]\nRange:  [5.756500651009527, 5.0505077711592685, 24.79530452660677, 0.0013927101636557193]\nAltitude:  [31551, 26287, 19961, 26381]\nBearing:  [16.362983493813744, 290.15006143663476, 231.5411661802979, 17.165262998506194]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [73, 88, 84, 91]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [5.756500651009527, 5.0505077711592685, 24.79530452660677, 0.0013927101636557193]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [31551, 26287, 19961, 26381]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [16.362983493813744, 290.15006143663476, 231.5411661802979, 17.165262998506194]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 310.4327256679535, "start_time": "2025-06-05T21:09:14.570747", "end_time": "2025-06-05T21:14:25.003474", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode C Reply Reception, Sect 2.2.4.4.2.1 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: *Test_2.2.4.4.2.1 Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 9600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario A\nStep1:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [83, 22, 22, 24, 86]\nRange:  [39.45031667305465, 35.616340564882485, 22.60348699534144, 36.38485853712836, 11.84878779227369]\nAltitude:  [20736, 39451, 356, 17094, 14969]\nBearing:  [162.90925027696724, 241.0164034193384, 129.98110612589937, 140.20584290081214, 213.5155242240955]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario B\nStep1:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [74, 81, 49]\nRange:  [44.770465936956874, 32.421544946581726, 43.46541112597979]\nAltitude:  [34706, 4761, 13956]\nBearing:  [108.0643925418768, 21.785729959067382, 149.46940500161284]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario C\nStep1:Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [32, 29, 97, 73, 44]\nRange:  [12.331232551779998, 31.237298455268004, 37.34604739926745, 40.046825209223165, 43.71432547178636]\nAltitude:  [28236, 13184, 23330, 10860, 34111]\nBearing:  [47.368319599468215, 352.8729989172723, 327.77999938714333, 196.93271329450147, 90.71096537737282]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario D\nStep1:Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [34, 27, 78]\nRange:  [0.2810881963932643, 3.685794874159903, 32.92925093944938]\nAltitude:  [7516, 19720, 13273]\nBearing:  [55.40003313643942, 339.74994811397613, 60.66748588682565]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario E\nStep1:Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [40, 89, 58, 37, 17]\nRange:  [17.868097724441, 23.0660079386652, 7.7435244386547755, 2.702130742217268, 48.79384091175806]\nAltitude:  [21242, 11084, 11945, 8062, 37763]\nBearing:  [349.92688117370386, 158.19721444064277, 91.63612439376041, 172.2897211716867, 51.10384765807259]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario F\nStep1:Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [36, 81, 76, 36]\nRange:  [19.42499313402325, 43.61327698736439, 17.3953347620253, 13.35314537377042]\nAltitude:  [32710, 29733, 11150, 17854]\nBearing:  [353.1126498028283, 1.9232509082251648, 323.33314961599285, 334.3961599048034]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 33400\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario A\nStep2:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [11, 27, 18, 45]\nRange:  [41.75244335351687, 21.313134884070617, 34.27570086271634, 21.869849654427014]\nAltitude:  [14642, 6097, 3492, 25771]\nBearing:  [36.408032819900626, 211.99794359247613, 290.8327563975507, 182.3319161967027]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario B\nStep2:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [91, 31, 64, 85, 97]\nRange:  [42.46498413630121, 22.564103603973688, 21.807007605027124, 9.58362874026118, 29.355410631555156]\nAltitude:  [6624, 36997, 8022, 22570, 20514]\nBearing:  [254.97047946391586, 205.32353157510087, 208.367706264724, 237.51746846125778, 56.993288445389524]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario A\nStep3:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [17, 22, 24, 54, 86]\nRange:  [15.47048045869463, 21.947305478442146, 42.19517228341461, 13.8175698039332, 33.76284207438995]\nAltitude:  [7963, 24572, 2014, 33375, 36163]\nBearing:  [258.71841698750075, 136.10468959971396, 139.94411732699885, 169.66488293093892, 340.8574976900429]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario B\nStep3:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [71, 3, 60, 24]\nRange:  [5.982122774223569, 47.72687756741124, 38.78078417948388, 21.28618226032644]\nAltitude:  [27794, 31022, 30602, 13505]\nBearing:  [279.51080469308573, 283.71031870843535, 317.9737034653489, 293.53901252616856]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 6000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4:Test_385_Mode C Reply Reception - Scenario A\nStep4:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Garb.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Garb.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [37, 77, 1, 50, 15]\nRange:  [4.566570917119794, 29.41080796338663, 33.5096537424122, 28.540455686159017, 25.3694860588054]\nAltitude:  [25725, 26868, 31644, 22445, 39327]\nBearing:  [227.27801763614735, 239.32163440134883, 85.58875900521495, 11.395728638802884, 157.71537992526245]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1_Results: [0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4_Results: [0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Done,Closing Session\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.35539174079895, "start_time": "2025-06-05T21:14:25.004240", "end_time": "2025-06-05T21:18:57.359633", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [6, 59, 79]\nRange:  [46.00520839974284, 28.209736210035945, 0.4621493265590415]\nAltitude:  [14079, 29196, 17865]\nBearing:  [109.10560627221133, 134.56450767375426, 194.0028838959866]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [4, 59, 61]\nRange:  [30.133409543069845, 49.155158684210676, 17.969877500988375]\nAltitude:  [6917, 7160, 29120]\nBearing:  [343.78354254689964, 289.2335575259449, 277.64355075216713]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [70, 4, 76, 15, 70]\nRange:  [47.93675851182879, 36.2948967586002, 23.895044164902718, 14.846226666427409, 23.299137407342542]\nAltitude:  [17153, 1450, 1685, 16327, 1565]\nBearing:  [353.65975586986576, 79.70835217505432, 44.00729398413433, 23.45943715098666, 239.83972413451116]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [60, 6, 63, 19]\nRange:  [2.899946155359312, 15.704933030273255, 47.52217965654253, 2.0307630561652577]\nAltitude:  [32948, 28688, 7718, 28162]\nBearing:  [5.040394988349992, 312.5173479047387, 352.1352148467752, 259.3616939687485]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [43, 80, 39, 65]\nRange:  [9.688869216274165, 5.012668949293275, 44.81534732497619, 47.39038182225067]\nAltitude:  [12316, 6970, 27539, 27235]\nBearing:  [255.55032349825368, 156.4267303552753, 166.06872568847604, 291.0513009327931]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [21, 17, 73]\nRange:  [13.113305198412316, 13.483646799477306, 18.684130476256932]\nAltitude:  [530, 35432, 13976]\nBearing:  [193.31177206097408, 234.6948654268766, 214.6365175944731]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [70, 86, 47]\nRange:  [45.337602643200356, 30.741513582173653, 3.678864399568554]\nAltitude:  [18458, 10330, 7398]\nBearing:  [237.39208990875468, 59.12027202685598, 26.019393742977414]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [74, 69, 76, 92]\nRange:  [0.27909363256038433, 4.6800534551024064, 46.490611035976215, 15.845260721343458]\nAltitude:  [30804, 22644, 22761, 21904]\nBearing:  [20.460019062407078, 197.46873844178901, 192.33444982614583, 265.31989159679364]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [41, 64, 92, 66, 50]\nRange:  [33.54739429931914, 17.334374789590324, 29.92139511555528, 36.21782990546213, 26.5129685863227]\nAltitude:  [24296, 18326, 38348, 21723, 10673]\nBearing:  [303.3897617084719, 124.8435385841939, 165.8019044623254, 224.76867624954707, 113.93520665792423]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [42, 90, 15, 61]\nRange:  [13.006529954005536, 19.817189710220585, 17.495881705804788, 4.638186211326767]\nAltitude:  [21800, 30338, 29328, 1337]\nBearing:  [62.13953990879664, 221.12129942901103, 228.38910302413274, 156.20876263600684]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\nStep1_Results:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1175224781036377, "start_time": "2025-06-05T21:18:57.360393", "end_time": "2025-06-05T21:18:57.477916", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "PASSED", "return_code": 0, "execution_time": 123.24961805343628, "start_time": "2025-06-05T21:18:57.478680", "end_time": "2025-06-05T21:21:00.728302", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *** DO-185E/385, Mode C Surveillance Initiation, Sect  2.2.4.6.2.1.2***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *Test_2.2.4.6.2.1.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 11000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step1:Test_385_Mode C Surveillance Initiation - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [59, 15, 81]\nRange:  [2.199897079405405, 39.05724912268387, 23.60476587857076]\nAltitude:  [23338, 37291, 16588]\nBearing:  [141.18416862364896, 53.49171535649943, 88.88045764625434]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step2:Test_385_Mode C Surveillance Initiation - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR ALTRPT,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR REPLY,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [50, 46, 94, 93, 38]\nRange:  [0.7555304956421816, 9.999300872304506, 20.741104966385905, 15.194906881101566, 0.7343614874954718]\nAltitude:  [29757, 22453, 9355, 25735, 22894]\nBearing:  [91.35195800274634, 140.08257168979577, 171.07720274414248, 336.0732494204858, 339.80514963313175]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step3:Test_385_Mode C Surveillance Initiation - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR VERTICAL,0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [89, 16, 93, 95, 94]\nRange:  [4.495395559650245, 24.394499782494062, 25.040012345098432, 12.19462914829203, 1.0343514001272647]\nAltitude:  [16410, 38082, 3473, 26476, 13832]\nBearing:  [336.2751360330626, 108.12388670031388, 45.66474045995675, 232.38418981841428, 132.81030326545698]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Test_2.2.4.6.2.1.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nStep1_Results:  [0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.3730821609497, "start_time": "2025-06-05T21:21:00.729004", "end_time": "2025-06-05T21:25:33.102087", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [17, 28, 18, 58, 86]\nRange:  [25.084462945251, 3.975703594607516, 17.493649655741894, 49.33201686611896, 28.737021053847318]\nAltitude:  [7781, 847, 9515, 9822, 32395]\nBearing:  [175.8087628595879, 320.7841111516675, 232.14003488414002, 355.5131701701478, 241.22129938901597]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [91, 48, 2]\nRange:  [36.62921190190189, 37.02403181469941, 35.35261808188855]\nAltitude:  [8990, 7014, 16736]\nBearing:  [128.5144160364964, 213.93343841571004, 84.00018620750951]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [36, 13, 78, 23]\nRange:  [48.60023497513845, 16.45112565720479, 16.792765382532437, 6.266959973350877]\nAltitude:  [27284, 16096, 13844, 8182]\nBearing:  [200.93143752383105, 321.4154501406943, 56.75911693820837, 300.12982322637725]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [82, 43, 59]\nRange:  [36.036265176595315, 16.344653829013485, 22.15787859039994]\nAltitude:  [23974, 27238, 21463]\nBearing:  [45.21765897442725, 100.64803558749756, 216.93949556577041]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\nIntruders:  [13, 58, 86]\nRange:  [12.478405839618173, 34.99362624071755, 45.29830843963555]\nAltitude:  [27295, 15298, 23042]\nBearing:  [98.81759385616542, 46.5548738712884, 355.1483544530725]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [43, 51, 82, 22]\nRange:  [32.03550062857737, 40.65522811390911, 4.186725528799723, 20.105040458009615]\nAltitude:  [4943, 31385, 13061, 708]\nBearing:  [183.5971487714986, 272.15407434837243, 81.92558157901657, 93.51385846863293]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 4 intruders\nIntruders:  [14, 33, 69, 40]\nRange:  [23.240715264901528, 33.59005879100943, 7.946790442753509, 29.20025817367382]\nAltitude:  [11662, 23133, 7622, 37989]\nBearing:  [135.95223292716025, 298.1492492991112, 135.8132361195855, 181.01276767870942]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [31, 28, 95, 16, 26]\nRange:  [2.33727639328532, 46.291688518163554, 39.86765704323635, 43.226518021414904, 0.3834784146007575]\nAltitude:  [15585, 6631, 12901, 13130, 31]\nBearing:  [256.4490880173615, 96.47953132556482, 195.11592635385716, 14.110615071872306, 135.3074296043493]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [85, 85, 66, 9, 79]\nRange:  [18.26458635388245, 44.483439101111465, 25.93949745007148, 46.9139290909776, 23.68034349175122]\nAltitude:  [35716, 10538, 3838, 33409, 39218]\nBearing:  [233.83707497088344, 82.73794794452881, 283.0196785008052, 112.22079092646592, 184.18436295480447]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [55, 24, 3, 77, 50]\nRange:  [32.05230825179583, 0.4293235582249144, 34.918572066705174, 31.32108951475438, 6.9638407318817706]\nAltitude:  [9938, 5693, 18884, 27555, 24043]\nBearing:  [31.88864338476026, 120.32812879500614, 297.02151546249587, 17.648150449479022, 207.7241869205017]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1_Results: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 27.240995168685913, "start_time": "2025-06-05T21:25:33.102740", "end_time": "2025-06-05T21:26:00.343736", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: *** DO-185E/385, Bearing Accuracy, Sect 2.2.4.6.4.2***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 3 intruders\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 232, in <module>\n    Test_2_2_4_6_4_2(rm, rgs, ARINC)\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 192, in Test_2_2_4_6_4_2\n    brg_avg[2],brg_max[2] = compute_BearingAccuracy(rm,ARINC)\n                            ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 109, in compute_BearingAccuracy\n    avg3 = avg3 + brg[3]\n                  ~~~^^^\nIndexError: list index out of range\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 42.21036219596863, "start_time": "2025-06-05T21:26:00.344928", "end_time": "2025-06-05T21:26:42.555291", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->Test_2_3_3_1: *** DO-185E/385, Radiated Output Power, Sect 2.3.3.1 ***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->setup: *Radiated Output Power -Start\nERROR?:  OK\nTimeBase-Pulse:  0.10332095144139974\nTSPAN:  True\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->trigger: *Radiated Output Power: Pulses: True\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 181, in <module>\n    res = Test_2_3_3_1(rm, rgs, pwr_obj)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 153, in Test_2_3_3_1\n    res[0] = measurePP(pw, 0)\n             ~~~~~~~~~^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 108, in measurePP\n    pwr = pw.getpwrmeasuremet().split(',')\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'float' object has no attribute 'split'\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 9, "failed": 2, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1738.3008823394775, "average_sequence_time": 158.0273529399525, "sequences_per_hour": 22.780866305897902, "optimization_effectiveness": {"optimization_success_rate": 81.81818181818183, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 2, "failure_by_procedure": {"DO385": 2}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 2 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}