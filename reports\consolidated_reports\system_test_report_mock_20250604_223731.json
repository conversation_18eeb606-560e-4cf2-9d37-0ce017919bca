{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-04T22:37:31.359051", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 73, "passed": 4, "failed": 69, "errors": 0, "timeouts": 0, "success_rate": 5.**************, "total_execution_time": 12.***************, "start_time": "2025-06-04T22:37:18.930596", "end_time": "2025-06-04T22:37:31.358045"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1830000877380371, "start_time": "2025-06-04T22:37:18.932596", "end_time": "2025-06-04T22:37:19.115596", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17583394050598145, "start_time": "2025-06-04T22:37:19.115596", "end_time": "2025-06-04T22:37:19.291429", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1679985523223877, "start_time": "2025-06-04T22:37:19.292425", "end_time": "2025-06-04T22:37:19.460424", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1679999828338623, "start_time": "2025-06-04T22:37:19.460424", "end_time": "2025-06-04T22:37:19.628424", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16800713539123535, "start_time": "2025-06-04T22:37:19.629416", "end_time": "2025-06-04T22:37:19.797423", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16900992393493652, "start_time": "2025-06-04T22:37:19.798415", "end_time": "2025-06-04T22:37:19.967425", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1690080165863037, "start_time": "2025-06-04T22:37:19.968416", "end_time": "2025-06-04T22:37:20.137424", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17101001739501953, "start_time": "2025-06-04T22:37:20.138416", "end_time": "2025-06-04T22:37:20.309426", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18299436569213867, "start_time": "2025-06-04T22:37:20.310431", "end_time": "2025-06-04T22:37:20.493425", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 32, in <module>\n    import pyvisa\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16600537300109863, "start_time": "2025-06-04T22:37:20.495419", "end_time": "2025-06-04T22:37:20.661425", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17000031471252441, "start_time": "2025-06-04T22:37:20.662415", "end_time": "2025-06-04T22:37:20.832416", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16699957847595215, "start_time": "2025-06-04T22:37:20.833422", "end_time": "2025-06-04T22:37:21.000421", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17000031471252441, "start_time": "2025-06-04T22:37:21.001423", "end_time": "2025-06-04T22:37:21.171423", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17500591278076172, "start_time": "2025-06-04T22:37:21.172423", "end_time": "2025-06-04T22:37:21.347429", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1709887981414795, "start_time": "2025-06-04T22:37:21.347429", "end_time": "2025-06-04T22:37:21.518418", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17000436782836914, "start_time": "2025-06-04T22:37:21.519417", "end_time": "2025-06-04T22:37:21.689422", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16799044609069824, "start_time": "2025-06-04T22:37:21.690427", "end_time": "2025-06-04T22:37:21.858417", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1690070629119873, "start_time": "2025-06-04T22:37:21.859414", "end_time": "2025-06-04T22:37:22.028422", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16699838638305664, "start_time": "2025-06-04T22:37:22.029424", "end_time": "2025-06-04T22:37:22.196422", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1704239845275879, "start_time": "2025-06-04T22:37:22.197414", "end_time": "2025-06-04T22:37:22.367839", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 50, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17599964141845703, "start_time": "2025-06-04T22:37:22.368839", "end_time": "2025-06-04T22:37:22.544838", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16600847244262695, "start_time": "2025-06-04T22:37:22.545840", "end_time": "2025-06-04T22:37:22.711849", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16598916053771973, "start_time": "2025-06-04T22:37:22.711849", "end_time": "2025-06-04T22:37:22.877838", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 53, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1659984588623047, "start_time": "2025-06-04T22:37:22.878840", "end_time": "2025-06-04T22:37:23.044838", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1640002727508545, "start_time": "2025-06-04T22:37:23.045838", "end_time": "2025-06-04T22:37:23.209839", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16451215744018555, "start_time": "2025-06-04T22:37:23.210838", "end_time": "2025-06-04T22:37:23.375351", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1656193733215332, "start_time": "2025-06-04T22:37:23.376342", "end_time": "2025-06-04T22:37:23.541961", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16604280471801758, "start_time": "2025-06-04T22:37:23.542974", "end_time": "2025-06-04T22:37:23.709017", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16599416732788086, "start_time": "2025-06-04T22:37:23.710022", "end_time": "2025-06-04T22:37:23.876016", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649930477142334, "start_time": "2025-06-04T22:37:23.876016", "end_time": "2025-06-04T22:37:24.041009", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16901111602783203, "start_time": "2025-06-04T22:37:24.042008", "end_time": "2025-06-04T22:37:24.211019", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16801047325134277, "start_time": "2025-06-04T22:37:24.212009", "end_time": "2025-06-04T22:37:24.380020", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 39, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17200756072998047, "start_time": "2025-06-04T22:37:24.382007", "end_time": "2025-06-04T22:37:24.554015", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1650071144104004, "start_time": "2025-06-04T22:37:24.555007", "end_time": "2025-06-04T22:37:24.720014", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1629958152770996, "start_time": "2025-06-04T22:37:24.721019", "end_time": "2025-06-04T22:37:24.884014", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1659986972808838, "start_time": "2025-06-04T22:37:24.885008", "end_time": "2025-06-04T22:37:25.051006", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 101, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1669926643371582, "start_time": "2025-06-04T22:37:25.052021", "end_time": "2025-06-04T22:37:25.219014", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 52, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16299962997436523, "start_time": "2025-06-04T22:37:25.220007", "end_time": "2025-06-04T22:37:25.383007", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1670069694519043, "start_time": "2025-06-04T22:37:25.385006", "end_time": "2025-06-04T22:37:25.552013", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16701722145080566, "start_time": "2025-06-04T22:37:25.553017", "end_time": "2025-06-04T22:37:25.720034", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1656177043914795, "start_time": "2025-06-04T22:37:25.721007", "end_time": "2025-06-04T22:37:25.886624", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1676011085510254, "start_time": "2025-06-04T22:37:25.887627", "end_time": "2025-06-04T22:37:26.055228", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16899752616882324, "start_time": "2025-06-04T22:37:26.056235", "end_time": "2025-06-04T22:37:26.225232", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1667933464050293, "start_time": "2025-06-04T22:37:26.226233", "end_time": "2025-06-04T22:37:26.393026", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18040204048156738, "start_time": "2025-06-04T22:37:26.394038", "end_time": "2025-06-04T22:37:26.574440", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1790010929107666, "start_time": "2025-06-04T22:37:26.575439", "end_time": "2025-06-04T22:37:26.754440", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1790015697479248, "start_time": "2025-06-04T22:37:26.754440", "end_time": "2025-06-04T22:37:26.933442", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18000125885009766, "start_time": "2025-06-04T22:37:26.934439", "end_time": "2025-06-04T22:37:27.114441", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 40, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1809999942779541, "start_time": "2025-06-04T22:37:27.115439", "end_time": "2025-06-04T22:37:27.296439", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16404008865356445, "start_time": "2025-06-04T22:37:27.297454", "end_time": "2025-06-04T22:37:27.461494", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1919996738433838, "start_time": "2025-06-04T22:37:27.462494", "end_time": "2025-06-04T22:37:27.654493", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16600584983825684, "start_time": "2025-06-04T22:37:27.655494", "end_time": "2025-06-04T22:37:27.821500", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16500163078308105, "start_time": "2025-06-04T22:37:27.821500", "end_time": "2025-06-04T22:37:27.986501", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_3_3.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1650078296661377, "start_time": "2025-06-04T22:37:27.987494", "end_time": "2025-06-04T22:37:28.152502", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16700077056884766, "start_time": "2025-06-04T22:37:28.153493", "end_time": "2025-06-04T22:37:28.320494", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16399908065795898, "start_time": "2025-06-04T22:37:28.321494", "end_time": "2025-06-04T22:37:28.485493", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 53, in <module>\n    import numpy as np\nModuleNotFoundError: No module named 'numpy'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1660003662109375, "start_time": "2025-06-04T22:37:28.486492", "end_time": "2025-06-04T22:37:28.652492", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 70, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16699981689453125, "start_time": "2025-06-04T22:37:28.652492", "end_time": "2025-06-04T22:37:28.819492", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16201567649841309, "start_time": "2025-06-04T22:37:28.820493", "end_time": "2025-06-04T22:37:28.982508", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1679847240447998, "start_time": "2025-06-04T22:37:28.982508", "end_time": "2025-06-04T22:37:29.150493", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1650099754333496, "start_time": "2025-06-04T22:37:29.150493", "end_time": "2025-06-04T22:37:29.315503", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1660001277923584, "start_time": "2025-06-04T22:37:29.316492", "end_time": "2025-06-04T22:37:29.482492", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17055273056030273, "start_time": "2025-06-04T22:37:29.482492", "end_time": "2025-06-04T22:37:29.653045", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16899895668029785, "start_time": "2025-06-04T22:37:29.654046", "end_time": "2025-06-04T22:37:29.823045", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_A_Frequency.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1699962615966797, "start_time": "2025-06-04T22:37:29.824049", "end_time": "2025-06-04T22:37:29.994045", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_B_Supression.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16600823402404785, "start_time": "2025-06-04T22:37:29.995046", "end_time": "2025-06-04T22:37:30.161054", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16599106788635254, "start_time": "2025-06-04T22:37:30.161054", "end_time": "2025-06-04T22:37:30.327045", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_D_Power.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16699957847595215, "start_time": "2025-06-04T22:37:30.328045", "end_time": "2025-06-04T22:37:30.495045", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_E_Diversity.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16599798202514648, "start_time": "2025-06-04T22:37:30.496047", "end_time": "2025-06-04T22:37:30.662044", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1679997444152832, "start_time": "2025-06-04T22:37:30.663045", "end_time": "2025-06-04T22:37:30.831044", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 62, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1830000877380371, "start_time": "2025-06-04T22:37:30.832044", "end_time": "2025-06-04T22:37:31.015044", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16900992393493652, "start_time": "2025-06-04T22:37:31.016045", "end_time": "2025-06-04T22:37:31.185055", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17199945449829102, "start_time": "2025-06-04T22:37:31.186045", "end_time": "2025-06-04T22:37:31.358044", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"TXDLib\\Procedures\\FAR43\\FAR43_J_Squitter.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 1, in <module>\n    import pyvisa, clr, sys\nModuleNotFoundError: No module named 'pyvisa'\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 1, "failed": 10, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 12.***************, "average_sequence_time": 0.170239023966332, "sequences_per_hour": 21146.737781532207, "optimization_effectiveness": {"optimization_success_rate": 5.**************, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 69, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 10, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 69 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}