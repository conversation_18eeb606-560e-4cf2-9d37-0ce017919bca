#!/usr/bin/env python3
"""
Unit Tests for Oscilloscope Handlers (D3054, MSO56, NI5110)
Tests oscilloscope functionality and measurement settling optimizations
"""

import unittest
import time
import sys
import os
import numpy as np
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_oscilloscope import MockOscilloscope
from tests.mocks.mock_resource_manager import MockResourceManager


class TestOscilloscope(unittest.TestCase):
    """Test cases for oscilloscope handlers"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = MockResourceManager()
        self.mock_scope = MockOscilloscope(self.mock_rm, "TCPIP0::************::INSTR")
        
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self, 'scope'):
            try:
                self.scope.close()
            except:
                pass
                
    def test_oscilloscope_initialization(self):
        """Test oscilloscope initialization"""
        scope = self.mock_scope
        self.assertIsNotNone(scope)
        self.assertEqual(scope.address, "TCPIP0::************::INSTR")
        
    def test_channel_configuration(self):
        """Test channel configuration"""
        scope = self.mock_scope
        
        # Configure channel 1
        scope.configure_channel(1, scale=1.0, offset=0.0, coupling='DC')
        
        # Verify channel configuration
        config = scope.get_channel_config(1)
        self.assertEqual(config['scale'], 1.0)
        self.assertEqual(config['offset'], 0.0)
        self.assertEqual(config['coupling'], 'DC')
        
    def test_timebase_configuration(self):
        """Test timebase configuration"""
        scope = self.mock_scope
        
        # Set timebase
        time_scale = 1e-6  # 1 microsecond per division
        scope.set_timebase(time_scale)
        
        # Verify timebase
        actual_timebase = scope.get_timebase()
        self.assertAlmostEqual(actual_timebase, time_scale, delta=1e-9)
        
    def test_trigger_configuration(self):
        """Test trigger configuration"""
        scope = self.mock_scope
        
        # Configure edge trigger
        scope.configure_trigger(
            source='CH1',
            trigger_type='EDGE',
            level=0.5,
            slope='POSITIVE'
        )
        
        # Verify trigger configuration
        trigger_config = scope.get_trigger_config()
        self.assertEqual(trigger_config['source'], 'CH1')
        self.assertEqual(trigger_config['type'], 'EDGE')
        self.assertEqual(trigger_config['level'], 0.5)
        self.assertEqual(trigger_config['slope'], 'POSITIVE')
        
    def test_waveform_acquisition(self):
        """Test waveform data acquisition"""
        scope = self.mock_scope
        
        # Configure for acquisition
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.set_timebase(1e-6)
        scope.configure_trigger(source='CH1', trigger_type='EDGE', level=0.5)
        
        # Acquire waveform
        waveform_data = scope.acquire_waveform(channel=1)
        
        # Verify waveform data
        self.assertIsInstance(waveform_data, dict)
        self.assertIn('time', waveform_data)
        self.assertIn('voltage', waveform_data)
        self.assertGreater(len(waveform_data['voltage']), 0)
        
    def test_pulse_measurement(self):
        """Test pulse parameter measurements"""
        scope = self.mock_scope
        
        # Configure for pulse measurement
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.set_timebase(1e-6)
        
        # Simulate pulse signal
        scope.simulate_pulse_signal(
            channel=1,
            amplitude=3.3,
            width=0.5e-6,
            period=1000e-6
        )
        
        # Measure pulse parameters
        pulse_width = scope.measure_pulse_width(channel=1)
        pulse_period = scope.measure_pulse_period(channel=1)
        pulse_amplitude = scope.measure_pulse_amplitude(channel=1)
        
        # Verify measurements
        self.assertAlmostEqual(pulse_width, 0.5e-6, delta=1e-9)
        self.assertAlmostEqual(pulse_period, 1000e-6, delta=1e-9)
        self.assertAlmostEqual(pulse_amplitude, 3.3, delta=0.1)
        
    def test_frequency_measurement(self):
        """Test frequency measurement"""
        scope = self.mock_scope
        
        # Configure for frequency measurement
        scope.configure_channel(1, scale=1.0, offset=0.0)
        
        # Simulate sine wave
        test_frequency = 1000.0  # 1 kHz
        scope.simulate_sine_wave(channel=1, frequency=test_frequency, amplitude=1.0)
        
        # Measure frequency
        measured_freq = scope.measure_frequency(channel=1)
        
        # Verify frequency measurement
        self.assertAlmostEqual(measured_freq, test_frequency, delta=1.0)
        
    def test_dme_pulse_analysis(self):
        """Test DME pulse analysis functionality"""
        scope = self.mock_scope
        
        # Configure for DME pulse analysis
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.set_timebase(10e-6)  # 10 microseconds per division
        
        # Simulate DME pulse pair
        scope.simulate_dme_pulse_pair(
            channel=1,
            pulse_separation=12e-6,  # 12 microsecond separation
            amplitude=3.3
        )
        
        # Analyze DME pulses
        dme_analysis = scope.analyze_dme_pulses(channel=1)
        
        # Verify DME analysis
        self.assertIn('pulse_separation', dme_analysis)
        self.assertIn('pulse_count', dme_analysis)
        self.assertAlmostEqual(dme_analysis['pulse_separation'], 12e-6, delta=1e-9)
        
    def test_transponder_pulse_analysis(self):
        """Test transponder pulse analysis"""
        scope = self.mock_scope
        
        # Configure for transponder analysis
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.set_timebase(1e-6)
        
        # Simulate Mode S transponder reply
        scope.simulate_mode_s_reply(
            channel=1,
            preamble_amplitude=3.3,
            data_bits=[1, 0, 1, 1, 0, 1, 0, 1]  # Sample data
        )
        
        # Analyze transponder reply
        reply_analysis = scope.analyze_transponder_reply(channel=1)
        
        # Verify analysis
        self.assertIn('preamble_detected', reply_analysis)
        self.assertIn('data_bits', reply_analysis)
        self.assertTrue(reply_analysis['preamble_detected'])
        
    def test_audio_acquisition(self):
        """Test audio data acquisition for DME/TCAS"""
        scope = self.mock_scope
        
        # Configure for audio acquisition
        scope.configure_channel(1, scale=0.1, offset=0.0)
        scope.set_sample_rate(44100)  # Audio sample rate
        
        # Acquire audio data
        audio_data = scope.acquire_audio_data(channel=1, duration=1.0)
        
        # Verify audio data
        self.assertIsInstance(audio_data, dict)
        self.assertIn('samples', audio_data)
        self.assertIn('sample_rate', audio_data)
        self.assertEqual(audio_data['sample_rate'], 44100)
        
    def test_measurement_settling_optimization(self):
        """Test measurement settling time optimization (MEDIUM PRIORITY)"""
        scope = self.mock_scope
        
        # Test optimized measurement settling
        start_time = time.time()
        
        # Perform operations that would have used longer settling times
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.wait_for_settling()  # Should use optimized 0.1s instead of 0.3s
        
        scope.set_timebase(1e-6)
        scope.wait_for_settling()  # Optimized settling
        
        scope.configure_trigger(source='CH1', trigger_type='EDGE', level=0.5)
        scope.wait_for_settling()  # Optimized settling
        
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 3 operations × 0.3s = 0.9s minimum
        # Optimized: 3 operations × 0.1s = 0.3s minimum
        self.assertLess(end_time - start_time, 0.7)  # Allow some margin
        
    def test_continuous_acquisition(self):
        """Test continuous acquisition mode"""
        scope = self.mock_scope
        
        # Start continuous acquisition
        scope.start_continuous_acquisition(channel=1)
        self.assertTrue(scope.is_acquiring())
        
        # Get continuous data
        time.sleep(0.1)  # Brief acquisition
        data = scope.get_continuous_data(channel=1)
        
        # Stop acquisition
        scope.stop_continuous_acquisition()
        self.assertFalse(scope.is_acquiring())
        
        # Verify data
        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)
        
    def test_math_functions(self):
        """Test oscilloscope math functions"""
        scope = self.mock_scope
        
        # Configure channels
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.configure_channel(2, scale=1.0, offset=0.0)
        
        # Configure math function (CH1 - CH2)
        scope.configure_math_function('SUBTRACT', source1='CH1', source2='CH2')
        
        # Acquire math waveform
        math_data = scope.acquire_math_waveform()
        
        # Verify math data
        self.assertIsInstance(math_data, dict)
        self.assertIn('time', math_data)
        self.assertIn('voltage', math_data)
        
    def test_measurement_statistics(self):
        """Test measurement statistics"""
        scope = self.mock_scope
        
        # Configure for statistics
        scope.configure_channel(1, scale=1.0, offset=0.0)
        scope.enable_measurement_statistics(True)
        
        # Perform multiple measurements
        for _ in range(10):
            scope.measure_pulse_width(channel=1)
            
        # Get statistics
        stats = scope.get_measurement_statistics('PULSE_WIDTH', channel=1)
        
        # Verify statistics
        self.assertIn('mean', stats)
        self.assertIn('std_dev', stats)
        self.assertIn('min', stats)
        self.assertIn('max', stats)
        self.assertIn('count', stats)
        self.assertEqual(stats['count'], 10)
        
    def test_error_handling(self):
        """Test error handling and recovery"""
        scope = self.mock_scope
        
        # Test invalid channel
        with self.assertRaises(ValueError):
            scope.configure_channel(5, scale=1.0, offset=0.0)  # Invalid channel
            
        # Test invalid timebase
        with self.assertRaises(ValueError):
            scope.set_timebase(-1e-6)  # Negative timebase
            
    def test_optimization_validation(self):
        """Test that measurement settling optimizations work correctly"""
        scope = self.mock_scope
        
        # Test optimized measurement timing
        start_time = time.time()
        
        # Perform operations that would have used longer delays
        for _ in range(5):
            scope.configure_channel(1, scale=1.0, offset=0.0)
            scope.wait_for_settling()  # Optimized from 0.3s to 0.1s
            scope.acquire_waveform(channel=1)
            
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 5 operations × 0.3s = 1.5s minimum
        # Optimized: 5 operations × 0.1s = 0.5s minimum
        self.assertLess(end_time - start_time, 1.2)  # Allow some margin


if __name__ == '__main__':
    unittest.main()
