{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:29:23.510001", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0, "success_rate": 18.181818181818183, "total_execution_time": 2.235006093978882, "start_time": "2025-06-05T14:29:21.274243", "end_time": "2025-06-05T14:29:23.509249"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.15812134742736816, "start_time": "2025-06-05T14:29:21.275234", "end_time": "2025-06-05T14:29:21.433357", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17524218559265137, "start_time": "2025-06-05T14:29:21.434377", "end_time": "2025-06-05T14:29:21.609622", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 222, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17106246948242188, "start_time": "2025-06-05T14:29:21.611366", "end_time": "2025-06-05T14:29:21.782428", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 53, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.3288731575012207, "start_time": "2025-06-05T14:29:21.784519", "end_time": "2025-06-05T14:29:22.113394", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 227, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17812752723693848, "start_time": "2025-06-05T14:29:22.115432", "end_time": "2025-06-05T14:29:22.293561", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 292, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17340731620788574, "start_time": "2025-06-05T14:29:22.295288", "end_time": "2025-06-05T14:29:22.468695", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 232, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.11799025535583496, "start_time": "2025-06-05T14:29:22.470034", "end_time": "2025-06-05T14:29:22.588026", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22051501274108887, "start_time": "2025-06-05T14:29:22.588949", "end_time": "2025-06-05T14:29:22.809467", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 186, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.25560760498046875, "start_time": "2025-06-05T14:29:22.810872", "end_time": "2025-06-05T14:29:23.066480", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 229, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20777511596679688, "start_time": "2025-06-05T14:29:23.067385", "end_time": "2025-06-05T14:29:23.275160", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 223, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.23126530647277832, "start_time": "2025-06-05T14:29:23.277481", "end_time": "2025-06-05T14:29:23.508746", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 173, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 2.235006093978882, "average_sequence_time": 0.20318237217989835, "sequences_per_hour": 17718.072495051627, "optimization_effectiveness": {"optimization_success_rate": 18.181818181818183, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 9, "failure_by_procedure": {"DO385": 9}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 9 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}