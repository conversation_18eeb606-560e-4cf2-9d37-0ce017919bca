{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:16:49.311543", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0, "success_rate": 37.5, "total_execution_time": 1.4563543796539307, "start_time": "2025-06-05T14:16:47.854380", "end_time": "2025-06-05T14:16:49.310735"}, "sequence_results": [{"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20539045333862305, "start_time": "2025-06-05T14:16:47.857373", "end_time": "2025-06-05T14:16:48.062763", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18760967254638672, "start_time": "2025-06-05T14:16:48.063979", "end_time": "2025-06-05T14:16:48.251589", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1898512840270996, "start_time": "2025-06-05T14:16:48.253697", "end_time": "2025-06-05T14:16:48.443550", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 44, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19714951515197754, "start_time": "2025-06-05T14:16:48.445376", "end_time": "2025-06-05T14:16:48.642527", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 41, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18829059600830078, "start_time": "2025-06-05T14:16:48.643765", "end_time": "2025-06-05T14:16:48.832055", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 36, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14229369163513184, "start_time": "2025-06-05T14:16:48.833483", "end_time": "2025-06-05T14:16:48.975777", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.18596768379211426, "start_time": "2025-06-05T14:16:48.976691", "end_time": "2025-06-05T14:16:49.162660", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14671945571899414, "start_time": "2025-06-05T14:16:49.163816", "end_time": "2025-06-05T14:16:49.310536", "stdout": "", "stderr": ""}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1.4563543796539307, "average_sequence_time": 0.18204429745674133, "sequences_per_hour": 19775.40659220846, "optimization_effectiveness": {"optimization_success_rate": 37.5, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 5, "failure_by_procedure": {"DO282": 5}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 5 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}