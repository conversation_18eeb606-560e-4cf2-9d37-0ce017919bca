#!/usr/bin/env python3
"""
Mock CLR (Common Language Runtime) module for .NET compatibility
Provides mock implementations for .NET assemblies used in legacy sequences
"""

import sys


class MockObserverClientWrapper:
    """Mock implementation of Honeywell.Interface.ObserverClientWrapper"""
    
    def __init__(self):
        """Initialize mock observer client"""
        print("Mock ObserverClientWrapper initialized")
        
    def SendMessageToObserver(self, channel, level, source, message):
        """Mock message sending to observer"""
        print(f"[OBSERVER] {source}: {message}")
        return True


class MockCLR:
    """Mock CLR module that provides .NET assembly loading functionality"""
    
    @staticmethod
    def AddReference(assembly_path):
        """Mock assembly reference addition"""
        print(f"Mock CLR: AddReference({assembly_path})")
        return True


# Create mock clr module
clr = MockCLR()

# Create mock Honeywell.Interface module structure
class MockHoneywellInterface:
    ObserverClientWrapper = MockObserverClientWrapper

class MockHoneywell:
    Interface = MockHoneywellInterface()

# Add to sys.modules to make imports work
sys.modules['clr'] = clr
sys.modules['Honeywell'] = MockHoneywell()
sys.modules['Honeywell.Interface'] = MockHoneywellInterface()
