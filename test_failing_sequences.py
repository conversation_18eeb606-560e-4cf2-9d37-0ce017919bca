#!/usr/bin/env python3
"""
Test script to validate the two failing DO385 sequences
"""

import subprocess
import sys
import os
from pathlib import Path

# Set up environment like sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

# Test the two failing sequences
failing_sequences = [
    "TXDLib/Procedures/DO385/DO385_2_2_4_6_4_2.py",
    "TXDLib/Procedures/DO385/DO385_2_3_3_1.py"
]

for sequence in failing_sequences:
    print(f"\n{'='*60}")
    print(f"Testing: {sequence}")
    print('='*60)
    
    result = subprocess.run(
        [sys.executable, sequence],
        capture_output=True,
        text=True,
        timeout=120,  # 2 minute timeout for quick test
        env=env
    )
    
    print(f"Return code: {result.returncode}")
    if result.returncode == 0:
        print("✅ PASSED")
    else:
        print("❌ FAILED")
        print("STDERR:")
        print(result.stderr[-500:])  # Last 500 chars of error
