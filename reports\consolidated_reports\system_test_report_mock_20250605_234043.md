# TXD Qualification Test System - System Test Report

**Generated**: 2025-06-05 23:40:43
**Execution Mode**: MOCK
**Report Version**: 1.0.0

## Executive Summary: ❌ FAILED

| Metric | Value |
|--------|-------|
| Total Sequences | 35 |
| Passed | 33 |
| Failed | 2 |
| Errors | 0 |
| Timeouts | 0 |
| Success Rate | 94.3% |
| Total Execution Time | 3434.3 seconds |

## Procedure Breakdown

### ❌ DO181
- **Total Sequences**: 35
- **Passed**: 33
- **Failed**: 2
- **Success Rate**: 94.3%

## Performance Metrics

- **Total Execution Time**: 3434.3 seconds
- **Average Sequence Time**: 98.1 seconds
- **Sequences Per Hour**: 36.7
- **Optimization Status**: ACTIVE

## Recommendations

1. Review failed sequences and address root causes before production use
2. Investigate 2 failed sequences
3. Verify hardware connections before live mode execution
4. Run mock mode tests regularly to validate sequence logic
5. Monitor execution times for performance regression
6. Keep system test reports for compliance documentation

## Individual Sequence Results

### ✅ DO181/DO_181E_2_3_2_10_Step1a.py
- **Status**: PASSED
- **Execution Time**: 115.3 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step1a_11-14-23.py
- **Status**: PASSED
- **Execution Time**: 87.0 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step1b.py
- **Status**: PASSED
- **Execution Time**: 80.0 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step1c.py
- **Status**: PASSED
- **Execution Time**: 84.1 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step2a.py
- **Status**: PASSED
- **Execution Time**: 97.0 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step2a_11-14-23.py
- **Status**: PASSED
- **Execution Time**: 67.0 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step2b.py
- **Status**: PASSED
- **Execution Time**: 69.6 seconds

### ✅ DO181/DO_181E_2_3_2_10_Step3.py
- **Status**: PASSED
- **Execution Time**: 67.1 seconds

### ❌ DO181/DO_181E_2_3_2_12.py
- **Status**: FAILED
- **Execution Time**: 0.4 seconds
- **Error**: C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedures\DO181\DO_181E_2_3_2_12.py:53: SyntaxWarn...

### ✅ DO181/DO_181E_2_3_2_1_step1.py
- **Status**: PASSED
- **Execution Time**: 152.6 seconds

### ✅ DO181/DO_181E_2_3_2_1_step2.py
- **Status**: PASSED
- **Execution Time**: 246.1 seconds

### ✅ DO181/DO_181E_2_3_2_1_step3.py
- **Status**: PASSED
- **Execution Time**: 137.7 seconds

### ✅ DO181/DO_181E_2_3_2_1_step4.py
- **Status**: PASSED
- **Execution Time**: 42.7 seconds

### ✅ DO181/DO_181E_2_3_2_1_step5.py
- **Status**: PASSED
- **Execution Time**: 72.7 seconds

### ✅ DO181/DO_181E_2_3_2_1_step6.py
- **Status**: PASSED
- **Execution Time**: 108.0 seconds

### ✅ DO181/DO_181E_2_3_2_1_step7.py
- **Status**: PASSED
- **Execution Time**: 32.5 seconds

### ✅ DO181/DO_181E_2_3_2_2_1.py
- **Status**: PASSED
- **Execution Time**: 36.9 seconds

### ✅ DO181/DO_181E_2_3_2_2_2.py
- **Status**: PASSED
- **Execution Time**: 215.8 seconds

### ✅ DO181/DO_181E_2_3_2_2_2_11-14-23.py
- **Status**: PASSED
- **Execution Time**: 161.4 seconds

### ✅ DO181/DO_181E_2_3_2_3_1.py
- **Status**: PASSED
- **Execution Time**: 39.0 seconds

### ✅ DO181/DO_181E_2_3_2_3_1_old.py
- **Status**: PASSED
- **Execution Time**: 60.9 seconds

### ✅ DO181/DO_181E_2_3_2_3_2a.py
- **Status**: PASSED
- **Execution Time**: 97.2 seconds

### ✅ DO181/DO_181E_2_3_2_3_2a_old.py
- **Status**: PASSED
- **Execution Time**: 30.3 seconds

### ✅ DO181/DO_181E_2_3_2_3_2b.py
- **Status**: PASSED
- **Execution Time**: 106.3 seconds

### ✅ DO181/DO_181E_2_3_2_3_2b_old.py
- **Status**: PASSED
- **Execution Time**: 30.0 seconds

### ✅ DO181/DO_181E_2_3_2_4.py
- **Status**: PASSED
- **Execution Time**: 88.4 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step1.py
- **Status**: PASSED
- **Execution Time**: 27.8 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step2.py
- **Status**: PASSED
- **Execution Time**: 68.7 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step3.py
- **Status**: PASSED
- **Execution Time**: 46.5 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step4.py
- **Status**: PASSED
- **Execution Time**: 62.9 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step5.py
- **Status**: PASSED
- **Execution Time**: 231.9 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step6.py
- **Status**: PASSED
- **Execution Time**: 217.7 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step7.py
- **Status**: PASSED
- **Execution Time**: 287.0 seconds

### ✅ DO181/DO_181E_2_3_2_5_Step8.py
- **Status**: PASSED
- **Execution Time**: 148.5 seconds

### ❌ DO181/DO_181E_2_3_2_8.py
- **Status**: FAILED
- **Execution Time**: 17.6 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

