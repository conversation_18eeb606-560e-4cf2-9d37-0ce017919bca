"""
Mock UAT_LOGGING module for DO282 procedures
Provides logging functionality for UAT test procedures
"""

import os
import time
from pathlib import Path


def mkdir(path: str):
    """Create directory if it doesn't exist"""
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        print(f"[MOCK UAT_LOGGING] Created directory: {path}")
    except Exception as e:
        print(f"[MOCK UAT_LOGGING] Error creating directory {path}: {e}")


def log_message(level: int, message: str):
    """Mock logging function"""
    levels = {0: "INFO", 1: "DEBUG", 2: "WARNING", 3: "ERROR"}
    level_name = levels.get(level, "UNKNOWN")
    timestamp = time.strftime("%H:%M:%S")
    print(f"[MOCK UAT_LOGGING] {timestamp} [{level_name}] {message}")


def create_log_file(filename: str):
    """Create a mock log file"""
    try:
        with open(filename, 'w') as f:
            f.write(f"# Mock UAT Log File - Created at {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        print(f"[MOCK UAT_LOGGING] Created log file: {filename}")
        return True
    except Exception as e:
        print(f"[MOCK UAT_LOGGING] Error creating log file {filename}: {e}")
        return False


def write_log_entry(filename: str, entry: str):
    """Write entry to log file"""
    try:
        with open(filename, 'a') as f:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{timestamp}: {entry}\n")
        return True
    except Exception as e:
        print(f"[MOCK UAT_LOGGING] Error writing to log file {filename}: {e}")
        return False


# Additional mock functions that might be used
def setup_logging(log_dir: str = "logs"):
    """Setup logging directory and configuration"""
    mkdir(log_dir)
    print(f"[MOCK UAT_LOGGING] Logging setup complete for directory: {log_dir}")


def cleanup_logs(log_dir: str = "logs", days_old: int = 30):
    """Mock log cleanup function"""
    print(f"[MOCK UAT_LOGGING] Mock cleanup of logs older than {days_old} days in {log_dir}")


def get_log_level():
    """Get current log level"""
    return 1  # DEBUG level


def set_log_level(level: int):
    """Set log level"""
    print(f"[MOCK UAT_LOGGING] Log level set to {level}")
