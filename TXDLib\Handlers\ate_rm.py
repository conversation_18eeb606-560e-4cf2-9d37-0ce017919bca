import pyvisa, clr, sys

clr.AddReference("C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface")
from Honeywell.Interface import ObserverClientWrapper

# ATE object to manage the visa resource manager and trace log handle
class ate_rm():
    def __init__(self):
        self.rm = pyvisa.ResourceManager()
        self.tvl = ObserverClientWrapper()
        self.logMessage(1, "Resource Manager Initialized.")
        self.instruments = {}
            
    # Send message to tracelog
    def logMessage(self, severity, msg):
        # grab the filename and function of where this is called from (one below this in call stack)
        facility = "TXD Python Lib: " + sys._getframe(1).f_code.co_filename.split("\\")[-1] + "->" + sys._getframe(1).f_code.co_name

        self.tvl.SendMessageToObserver("0", severity, facility, msg)
        print(facility + ": " + msg)
        return facility + " -- " + msg

    # Cleanup the visa resource manager and trace log handle
    def cleanup(self):
        self.rm.close()
        self.logMessage(1, "Resource Manager Closed.")
        self.tvl.Dispose()

    def addInstrument(self, name, inst):
        self.instruments[name] = inst
