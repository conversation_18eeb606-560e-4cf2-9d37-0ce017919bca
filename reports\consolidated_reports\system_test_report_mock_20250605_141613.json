{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:16:13.000589", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0, "success_rate": 37.5, "total_execution_time": 1.5705554485321045, "start_time": "2025-06-05T14:16:11.428716", "end_time": "2025-06-05T14:16:12.999271"}, "sequence_results": [{"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2172861099243164, "start_time": "2025-06-05T14:16:11.430016", "end_time": "2025-06-05T14:16:11.647303", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19597792625427246, "start_time": "2025-06-05T14:16:11.649180", "end_time": "2025-06-05T14:16:11.845160", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1997241973876953, "start_time": "2025-06-05T14:16:11.846295", "end_time": "2025-06-05T14:16:12.046020", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 44, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20084428787231445, "start_time": "2025-06-05T14:16:12.047014", "end_time": "2025-06-05T14:16:12.247859", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 41, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19881820678710938, "start_time": "2025-06-05T14:16:12.249193", "end_time": "2025-06-05T14:16:12.448012", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 36, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14166641235351562, "start_time": "2025-06-05T14:16:12.449564", "end_time": "2025-06-05T14:16:12.591230", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.23072266578674316, "start_time": "2025-06-05T14:16:12.592244", "end_time": "2025-06-05T14:16:12.822967", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.17552709579467773, "start_time": "2025-06-05T14:16:12.823552", "end_time": "2025-06-05T14:16:12.999081", "stdout": "", "stderr": ""}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1.5705554485321045, "average_sequence_time": 0.19631943106651306, "sequences_per_hour": 18337.461454746775, "optimization_effectiveness": {"optimization_success_rate": 37.5, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 5, "failure_by_procedure": {"DO282": 5}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 5 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}