#!/usr/bin/env python3
"""
TXD Qualification Test System - Mock Mode Implementation
Provides mock interfaces for all hardware components
"""

import time
import random
from typing import Dict, Any, Optional


class MockMode:
    """Mock mode implementation for hardware-free testing"""
    
    def __init__(self):
        """Initialize mock mode"""
        self.mock_interfaces = {}
        self.setup_mock_interfaces()
    
    def setup_mock_interfaces(self):
        """Setup all mock hardware interfaces"""
        self.mock_interfaces = {
            'atc5000ng': MockATC5000NG(),
            'rgs2000ng': MockRGS2000NG(),
            'spectrum_analyzer': MockSpectrumAnalyzer(),
            'signal_generator': MockSignalGenerator(),
            'power_meter': MockPowerMeter(),
            'oscilloscope': MockOscilloscope(),
            'dc_power_supply': MockDCPowerSupply(),
            'ni_discretes': MockNIDiscretes(),
            'ni_multiio': MockNIMultiIO(),
            'pickering': <PERSON>ck<PERSON>ickering(),
            'arinc429': MockARINC429(),
            'uat_connection': MockUATConnection()
        }
    
    def get_interface(self, interface_name: str):
        """Get mock interface by name"""
        return self.mock_interfaces.get(interface_name)
    
    def is_hardware_available(self) -> bool:
        """Always return True for mock mode"""
        return True


class MockATC5000NG:
    """Mock ATC5000NG Aviation Test Controller"""
    
    def __init__(self):
        self.connected = False
        self.scenario_loaded = False
        self.rf_enabled = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)  # Simulate connection time
        self.connected = True
        return True
    
    def disconnect(self):
        """Mock disconnection"""
        self.connected = False
        return True
    
    def load_scenario(self, scenario_file: str):
        """Mock scenario loading"""
        time.sleep(2.0)  # Simulate loading time (optimized from 50s)
        self.scenario_loaded = True
        return True
    
    def start_scenario(self):
        """Mock scenario start"""
        time.sleep(1.0)  # Simulate start time (optimized from 30s)
        return True
    
    def enable_rf(self):
        """Mock RF enable"""
        time.sleep(1.5)  # Simulate RF stabilization (optimized from 15s)
        self.rf_enabled = True
        return True
    
    def get_status(self):
        """Mock status check"""
        return {
            'connected': self.connected,
            'scenario_loaded': self.scenario_loaded,
            'rf_enabled': self.rf_enabled,
            'ready': self.connected and self.scenario_loaded
        }


class MockRGS2000NG:
    """Mock RGS2000NG Radar Generator System"""
    
    def __init__(self):
        self.connected = False
        self.transmitting = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)
        self.connected = True
        return True
    
    def start_transmission(self):
        """Mock transmission start"""
        time.sleep(0.5)
        self.transmitting = True
        return True
    
    def stop_transmission(self):
        """Mock transmission stop"""
        self.transmitting = False
        return True


class MockSpectrumAnalyzer:
    """Mock Spectrum Analyzer (N9010B)"""
    
    def __init__(self):
        self.connected = False
        self.frequency = 1090e6  # Default frequency
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def set_frequency(self, freq: float):
        """Mock frequency setting"""
        time.sleep(0.1)
        self.frequency = freq
        return True
    
    def measure_power(self) -> float:
        """Mock power measurement"""
        time.sleep(0.5)  # Simulate measurement time
        # Return realistic power reading with some variation
        return -20.0 + random.uniform(-2.0, 2.0)


class MockSignalGenerator:
    """Mock Signal Generator (N5172B)"""
    
    def __init__(self):
        self.connected = False
        self.frequency = 1090e6
        self.power = -10.0
        self.output_enabled = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def set_frequency(self, freq: float):
        """Mock frequency setting"""
        time.sleep(0.1)
        self.frequency = freq
        return True
    
    def set_power(self, power: float):
        """Mock power setting"""
        time.sleep(0.1)
        self.power = power
        return True
    
    def enable_output(self):
        """Mock output enable"""
        time.sleep(0.2)
        self.output_enabled = True
        return True


class MockPowerMeter:
    """Mock Power Meter (B4500C)"""
    
    def __init__(self):
        self.connected = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def measure_power(self) -> float:
        """Mock power measurement"""
        time.sleep(0.3)
        return 25.5 + random.uniform(-1.0, 1.0)  # Mock power reading


class MockOscilloscope:
    """Mock Oscilloscope"""
    
    def __init__(self):
        self.connected = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def capture_waveform(self):
        """Mock waveform capture"""
        time.sleep(1.0)
        return [random.uniform(-1.0, 1.0) for _ in range(1000)]  # Mock waveform data


class MockDCPowerSupply:
    """Mock DC Power Supply (N6700)"""
    
    def __init__(self):
        self.connected = False
        self.voltage = 0.0
        self.current = 0.0
        self.output_enabled = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def set_voltage(self, voltage: float):
        """Mock voltage setting"""
        time.sleep(0.1)
        self.voltage = voltage
        return True
    
    def enable_output(self):
        """Mock output enable"""
        time.sleep(0.1)
        self.output_enabled = True
        return True


class MockNIDiscretes:
    """Mock NI Discrete I/O (NI6528)"""
    
    def __init__(self):
        self.connected = False
        self.digital_states = {}
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)
        self.connected = True
        return True
    
    def set_digital_output(self, channel: int, state: bool):
        """Mock digital output"""
        self.digital_states[channel] = state
        return True


class MockNIMultiIO:
    """Mock NI Multi-function I/O (NI6363)"""
    
    def __init__(self):
        self.connected = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)
        self.connected = True
        return True
    
    def read_analog_input(self, channel: int) -> float:
        """Mock analog input reading"""
        time.sleep(0.05)
        return random.uniform(0.0, 5.0)


class MockPickering:
    """Mock Pickering Switching System"""
    
    def __init__(self):
        self.connected = False
        self.switch_states = {}
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)
        self.connected = True
        return True
    
    def set_switch(self, switch_id: int, state: bool):
        """Mock switch setting"""
        self.switch_states[switch_id] = state
        return True


class MockARINC429:
    """Mock ARINC 429 Interface"""
    
    def __init__(self):
        self.connected = False
        self.transmitting = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.1)
        self.connected = True
        return True
    
    def send_message(self, message: bytes):
        """Mock message transmission"""
        time.sleep(0.01)
        return True


class MockUATConnection:
    """Mock UAT (Universal Access Transceiver) Connection"""
    
    def __init__(self):
        self.connected = False
    
    def connect(self):
        """Mock connection"""
        time.sleep(0.2)
        self.connected = True
        return True
    
    def send_uat_message(self, message: str):
        """Mock UAT message transmission"""
        time.sleep(0.05)
        return True
    
    def receive_uat_message(self) -> str:
        """Mock UAT message reception"""
        time.sleep(0.05)
        return "MOCK_UAT_MESSAGE"
