{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:28:38.127596", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0, "success_rate": 18.181818181818183, "total_execution_time": 2.140432357788086, "start_time": "2025-06-05T14:28:35.986082", "end_time": "2025-06-05T14:28:38.126515"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1888580322265625, "start_time": "2025-06-05T14:28:35.987025", "end_time": "2025-06-05T14:28:36.175885", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1931009292602539, "start_time": "2025-06-05T14:28:36.176976", "end_time": "2025-06-05T14:28:36.370062", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 222, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1718766689300537, "start_time": "2025-06-05T14:28:36.371379", "end_time": "2025-06-05T14:28:36.543258", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 53, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 89\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.35228919982910156, "start_time": "2025-06-05T14:28:36.544840", "end_time": "2025-06-05T14:28:36.897132", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 227, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16708755493164062, "start_time": "2025-06-05T14:28:36.899302", "end_time": "2025-06-05T14:28:37.066392", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 292, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16796302795410156, "start_time": "2025-06-05T14:28:37.068129", "end_time": "2025-06-05T14:28:37.236093", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 232, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14184308052062988, "start_time": "2025-06-05T14:28:37.237399", "end_time": "2025-06-05T14:28:37.379243", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18611764907836914, "start_time": "2025-06-05T14:28:37.380192", "end_time": "2025-06-05T14:28:37.566312", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 186, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18067383766174316, "start_time": "2025-06-05T14:28:37.568106", "end_time": "2025-06-05T14:28:37.748781", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 229, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19272899627685547, "start_time": "2025-06-05T14:28:37.750696", "end_time": "2025-06-05T14:28:37.943423", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 223, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17986559867858887, "start_time": "2025-06-05T14:28:37.945681", "end_time": "2025-06-05T14:28:38.125545", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 173, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 2.140432357788086, "average_sequence_time": 0.1945847597989169, "sequences_per_hour": 18500.93503581794, "optimization_effectiveness": {"optimization_success_rate": 18.181818181818183, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 9, "failure_by_procedure": {"DO385": 9}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 9 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}