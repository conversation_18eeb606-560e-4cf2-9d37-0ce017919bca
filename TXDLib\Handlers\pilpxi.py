from ctypes import *
import platform

#
# This Python class mimics the Pickering PILPXI driver
#
# Refer to PILPXI documentation for details of any function
#
# <PERSON>, Pickering Interfaces Ltd, 2010
#
# <PERSON><PERSON><PERSON>, bug fixed with the Status(self) function 18/06/2013 
# modified from Status(self, card): to Status(self):
#
def calc_dwords(bits):
    dwords = (bits)/32
    if ((bits)%32 > 0): dwords = dwords +1
    return dwords 

class pilpxi_base:
    def __init__(self):
        if platform.system() == "Windows":
            self.handle = windll.LoadLibrary("pilpxi")
        elif platform.system() == "Linux":
            arch = platform.architecture()
            if "64bit" in arch:
                self.handle = cdll.LoadLibrary("libpilpxi64.so")
            else:
                self.handle = cdll.LoadLibrary("libpilpxi32.so")

        self.bus = (c_uint32 * 40)()
        self.slot = (c_uint32 * 40)()

    def Version(self):
        ver = self.handle.PIL_Version()
        return ver

    def CountFreeCards(self):
        count = c_uint(0)
        err = self.handle.PIL_CountFreeCards(byref(count))
        return err, count.value

    def FindFreeCards(self, count):
        err = self.handle.PIL_FindFreeCards(count, byref(self.bus), byref(self.slot))
        return err, self.bus, self.slot


class pilpxi_card:
    
# Constructor

    def __init__(self, bus, slot):
        if platform.system() == "Windows":
            self.handle = windll.LoadLibrary("pilpxi")
        elif platform.system() == "Linux":
            arch = platform.architecture()
            if "64bit" in arch:
                self.handle = cdll.LoadLibrary("libpilpxi64.so")
            else:
                self.handle = cdll.LoadLibrary("libpilpxi32.so")

        self.card = c_uint(0)
        self.string = create_string_buffer(100)
        self.data = (c_uint32 * 40)()

        # Capabilities flags enum for ResInfo()

        self.RESCAP = { 
            "RES_CAP_NONE": 0x00, 
            "RES_CAP_PREC": 0x01, 
            "RES_CAP_ZERO": 0x02, 
            "RES_CAP_INF":  0x04, 
            "RES_CAP_REF":  0x08 
        }

        # Error Code Enum
        self.ERRORCODE = {
            "NO_ERR" : 0,                       # No error
            "ER_NO_CARD" : 1,                   # No card present with specified number
            "ER_NO_INFO" : 2,                   # Card information unobtainable - hardware problem
            "ER_CARD_DISABLED" : 3,             # Card disabled - hardware problem
            "ER_BAD_SUB" : 4,                   # Card has no sub-unit with specified number
            "ER_BAD_BIT" : 5,                   # Sub-unit has no bit with specified number
            "ER_NO_CAL_DATA" : 6,               # Sub-unit has no calibration data to write/read
            "ER_BAD_ARRAY" : 7,                 # Array type, size or shape is incorrect
            "ER_MUX_ILLEGAL" : 8,               # Non-zero write data is illegal for MUX sub-unit
            "ER_EXCESS_CLOSURE" : 9,            # Sub-unit closure limit exceeded
            "ER_ILLEGAL_MASK" : 10,             # One or more of the specified channels cannot be masked
            "ER_OUTPUT_MASKED" : 11,            # Cannot activate an output that is masked
            "ER_BAD_LOCATION" : 12,             # Cannot open a Pickering card at the specified location
            "ER_READ_FAIL" : 13,                # Failed read from hardware
            "ER_WRITE_FAIL" : 14,               # Failed write to hardware
            "ER_DRIVER_OP" : 15,                # Hardware driver failure
            "ER_DRIVER_VERSION" : 16,           # Incompatible hardware driver version
            "ER_SUB_TYPE" : 17,                 # Function call incompatible with sub-unit type or capabilities
            "ER_BAD_ROW" : 18,                  # Matrix row value out of range
            "ER_BAD_COLUMN" : 19,               # Matrix column value out of range
            "ER_BAD_ATTEN" : 20,                # Attenuation value out of range
            "ER_BAD_VOLTAGE" : 21,              # Voltage value out of range
            "ER_BAD_CAL_INDEX" : 22,            # Calibration reference out of range
            "ER_BAD_SEGMENT" : 23,              # Segment number out of range
            "ER_BAD_FUNC_CODE" : 24,            # Function code value out of range
            "ER_BAD_SUBSWITCH" : 25,            # Subswitch value out of range
            "ER_BAD_ACTION" : 26,               # Action code out of range
            "ER_STATE_CORRUPT" : 27,            # Cannot execute due to corrupt sub-unit state
            "ER_BAD_ATTR_CODE" : 28,            # Unrecognised attribute code
            "ER_EEPROM_WRITE_TMO" : 29,         # Timeout writing to EEPROM
            "ER_ILLEGAL_OP" : 30,               # Operation is illegal in the sub-unit's current state
            "ER_BAD_POT" : 31,                  # Unrecognised pot number requested
            "ER_MATRIXR_ILLEGAL" : 32,          # Invalid write pattern for MATRIXR sub-unit
            "ER_MISSING_CHANNEL" : 33,          # Attempted operation on non-existent channel
            "ER_CARD_INACCESSIBLE" : 34,        # Card cannot be accessed (failed/removed/unpowered)
            "ER_BAD_FP_FORMAT" : 35,            # Unsupported internal floating-point format (internal error)
            "ER_UNCALIBRATED" : 36,             # Sub-unit is not calibrated
            "ER_BAD_RESISTANCE" : 37,           # Unobtainable resistance value
            "ER_BAD_STORE" : 38,                # Invalid calibration store number
            "ER_BAD_MODE" : 39,                 # Invalid mode value
            "ER_SETTINGS_CONFLICT" : 40,        # Conflicting device settings
            "ER_CARD_TYPE" : 41,                # Function call incompatible with card type or capabilities
            "ER_BAD_POLE" : 42,                 # Switch pole value out of range
            "ER_MISSING_CAPABILITY" : 43,       # Attempted to activate a non-existent capability
            "ER_MISSING_HARDWARE" : 44,         # Action requires hardware that is not present
            "ER_HARDWARE_FAULT" : 45,           # Faulty hardware
            "ER_EXECUTION_FAIL" : 46,           # Failed to execute (e.g. blocked by a hardware condition)
            "ER_BAD_CURRENT" : 47,              # Current value out of range
            "ER_BAD_RANGE" : 48,                # Invalid range value
            "ER_ATTR_UNSUPPORTED" : 49,         # Attribute not supported
            "ER_BAD_REGISTER" : 50,             # Register number out of range
            "ER_MATRIXP_ILLEGAL" : 51,          # Invalid channel closure or write pattern for MATRIXP sub-unit
            "ER_BUFFER_UNDERSIZE" : 52,         # Data buffer too small
            "ER_ACCESS_MODE" : 53,              # Inconsistent shared access mode
            "ER_POOR_RESISTANCE" : 54,          # Resistance outside limits
            "ER_BAD_ATTR_VALUE" : 55,           # Bad attribute value
            "ER_INVALID_POINTER" : 56,          # Invalid pointer
            "ER_ATTR_READ_ONLY" : 57,           # Attribute is read only
            "ER_ATTR_DISABLED" : 58,            # Attribute is disabled
            "ER_PSU_MAIN_OUTPUT_DISABLED" : 59, # Main output is disabled, cannot enable the channel
            "ER_OUT_OF_MEMORY_HEAP" : 60,       # Unable to allocate memory on Hea
            "ER_INVALID_PROCESSID" : 61,        # Invalid ProcessID
            "ER_SHARED_MEMORY" : 62,            # Shared memory error
            "ER_CARD_OPENED_OTHER_PROCESS" : 63 # Card is opened by a process in exclusive mode
        }

        # Attribute Codes Enum
        self.ATTR = {
            "TYPE" : 0x400,  # Gets/Sets DWORD attribute value of Type of the Sub-unit (values: TYPE_MUXM, TYPE_MUXMS) 
            "MODE" : 0x401, # Gets/Sets DWORD attribute value of Mode of the Card 

            # Current monitoring attributes 
            "CNFGREG_VAL" : 0x402,	# Gets/Sets WORD value of config register 
            "SHVLREG_VAL" : 0x403,	# Gets WORD value of shuntvoltage register 
            "CURRENT_VAL" : 0x404,	# Gets double current value in Amps 

            # Read-only Power Supply attributes 
            "INTERLOCK_STATUS" : 0x405,	# Gets BOOL value of interlock status 
            "OVERCURRENT_STATUS_MAIN" : 0x406,	# Gets BOOL value of main overcurrent status 
            "OVERCURRENT_STATUS_CH" : 0x407,	# Gets BOOL value of overcurrent status on specific channel 

            # Read/Write Power Supply attributes 
            "OUTPUT_ENABLE_MAIN" : 0x408,	# Gets/Sets BOOL value. Enables/Disables main 
            "OUTPUT_ENABLE_CH" : 0x409,	# Gets/Sets BOOL value. Enables/Disables specific channel 

            # Read/Write Thermocouple Simulator functions 
            "TS_SET_RANGE" : 0x40A,		# Gets/Sets Auto range which toggles between based on the value 
            # Read-only function
            "TS_LOW_RANGE_MIN" : 0x40B,
            "TS_LOW_RANGE_MED" : 0x40C,
            "TS_LOW_RANGE_MAX" : 0x40D,
            "TS_LOW_RANGE_MAX_DEV" : 0x40E,
            "TS_LOW_RANGE_PREC_PC" : 0x40F,
            "TS_LOW_RANGE_PREC_DELTA" : 0x410,
            "TS_MED_RANGE_MIN" : 0x411,
            "TS_MED_RANGE_MED" : 0x412,
            "TS_MED_RANGE_MAX" : 0x413,
            "TS_MED_RANGE_MAX_DEV" : 0x414,
            "TS_MED_RANGE_PREC_PC" : 0x415,
            "TS_MED_RANGE_PREC_DELTA" : 0x416,
            "TS_HIGH_RANGE_MIN" : 0x417,
            "TS_HIGH_RANGE_MED" : 0x418,
            "TS_HIGH_RANGE_MAX" : 0x419,
            "TS_HIGH_RANGE_MAX_DEV" : 0x41A,
            "TS_HIGH_RANGE_PREC_PC" : 0x41B,
            "TS_HIGH_RANGE_PREC_DELTA" : 0x41C,
            "TS_POT_VAL" : 0x41D, # Read Pot Value from user store
            # Write-only function
            "TS_SET_POT" : 0x41E,
            "TS_SAVE_POT" : 0x41F,
            "TS_DATA_DUMP" : 0x420,
            "MUXM_MBB" : 0x421,

            "TS_TEMPERATURES_C" : 0x42E, # Read 7 sensors on 1192r0 41-760 I2C Compensation Block in degrees Celsius
            "TS_TEMPERATURES_F" : 0x42F, # Read 7 sensors on 1192r0 41-760 I2C Compensation Block in degrees Farenheit
            "TS_EEPROM" : 0x430, # Read/write 34LC02 eeprom
            "TS_EEPROM_OFFSET" : 0x431,  # Supply offset to eeprom

            "CURRENT_MA" : 0x440,   # Set/Get DOUBLE value of Current in mA
            "VOLTAGE_V" : 0x441,    # Get/Get DOUBLE value of Voltage in V

            # VDT attributes   
            "VDT_AUTO_INPUT_ATTEN"              : 0x450,    # Sets/Gets DWORD (0-100) for input gain (Default = 100)
            "VDT_ABS_POSITION"                  : 0x451,    # Sets/Gets DWORD (0-32767) for Both Outputs on LVDT_5_6 WIRE & OutputA on LVDT_4_WIRE
            "VDT_ABS_POSITION_B"                : 0x452,    # Sets/Gets DWORD (0-32767)  for OutputB on LVDT_4_WIRE
            "VDT_PERCENT_POSITION"              : 0x453,    # Sets/Gets DOUBLE (-100.00% to 100.00%) for Both Out on LVDT_5_6 WIRE & OutA on LVDT_4_WIRE
            "VDT_PERCENT_POSITION_B"            : 0x454,    # Sets/Gets DOUBLE (-100.00% to 100.00%) for OutB on LVDT_4_WIRE
            "VDT_VOLTAGE_SUM"                   : 0x455,    # Sets/Gets DOUBLE in Volts  for VSUM value
            "VDT_VOLTAGE_DIFF"                  : 0x456,    # Sets/Gets DOUBLE in Volts  for VDIFF value (the limit is +/- VSUM)
            "VDT_OUT_GAIN"                      : 0x457,    # Sets/Gets DWORD (1 or 2) for 1x or 2x output multiplier */ #CALIBRATION ONLY
            "VDT_MANUAL_INPUT_ATTEN"            : 0x458,    # Sets/Gets DWORD (0-255) Pot Value on LVDT
            "VDT_MODE"                          : 0x459,    # Sets/Gets DWORD to set mode 1 = LVDT_5_6_WIRE, mode 2=  LVDT_4_WIRE.
            "VDT_DELAY_A"                       : 0x45A,    # Sets/Gets DWORD (0-6499) delay for OutputA
            "VDT_DELAY_B"                       : 0x45B,    # Sets/Gets DWORD (0-6499) delay for OutputB
            "VDT_INPUT_LEVEL"                   : 0x45C,    # Sets/Gets DWORD (0-65520) for Input Value
            "VDT_INPUT_FREQ"                    : 0x45D,    # Sets/Gets DWORD (300-20000 Hz) for Input Frequency
            "VDT_OUT_LEVEL"                     : 0x45E,    # Sets/Gets DWORD (0-4096)  output level

            # LVDT Mk2 Get only
            "VDT_DSPIC_VERSION"                 : 0x45F,    # Gets DWORD value of for dsPIC firmware version 104 = v0.01.04

            # LVDT Mk2 Set/Get
            "VDT_INVERT_A"                      : 0x460,    # Sets/Gets DWORD (0 or 1)  for OutA
            "VDT_INVERT_B"                      : 0x461,    # Sets/Gets DWORD (0 or 1)  for OutB
            "VDT_PHASE_TRACKING"                : 0x462,    # 'TP' Phase tracking mode on or off  -CALIBRATION ONLY
            "VDT_SAMPLE_LOAD"                   : 0x463,    # Sets DWORD comprises of Top 16 bits is GAIN (0-100) and lower 16 frequency (300-20000 Hz)
            "VDT_INPUT_FREQ_HI_RES"             : 0x464,    # Gets DWORD value of frequency in Hz
            "VDT_LOS_THRESHOLD"                 : 0x465,    # Sets/Gets DWORD (0 to 32768) for LOS Threshold (Default = 32768)
            "VDT_SMPL_BUFFER_SIZE"              : 0x466,    # Sets/Gets DWORD (1 to 500) for Sample buffer size (Default = 500)
            "VDT_NULL_OFFSET"                   : 0x467,    # Sets/Gets WORD (0 to 100) for null offset (Default = 0)

            # LVDT Get Only
            "VDT_STATUS"                        : 0x468,    # Gets BYTE value (0x00 or 0x01) checking LOS status
            "VDT_MAX_OUT_VOLTAGE"               : 0x469,    # Gets DOUBLE value for maximum output voltage
            "VDT_MIN_OUT_VOLTAGE"               : 0x46A,    # Gets DOUBLE value for minimum output voltage
            "VDT_MAX_IN_VOLTAGE"                : 0x46B,    # Gets DOUBLE value for maximum input voltage
            "VDT_MIN_IN_VOLTAGE"                : 0x46C,    # Gets DOUBLE value for minimum input voltage

            # Resolver Get/Set
            "RESOLVER_START_STOP_ROTATE"        : 0x470,    # Sets/Gets BOOL TRUE for Start, FALSE of Stop
            "RESOLVER_NUM_OF_TURNS"             : 0x471,    # Sets/ Gets WORD Number of turns (1-65535)
            "RESOLVER_ROTATE_SPEED"             : 0x472,    # Sets/Gets DOUBLE rotating speed (RPM speed upto 655.35 RPM)
            "RESOLVER_POSITION"                 : 0x473,    # Sets/Gets DOUBLE rotation between -180.00 to 180.00 Degrees

            "SETTLE_DELAY_ZERO"                 : 0x480,    # Sets/Gets BOOL, settling time set to zero for the ouput subunits
                                                            # Use this attribute carefully. Settling delay wont be applied. 
                                                            # Handle the settling time needed for the relays appropriately, in the application.

            "CARD_PCB_NUM" : 0x43D, # Card PCB Number.
            "CARD_PCB_REV_NUM" : 0x43E, # Card PCB Revision Number.
            "CARD_FW_REV_NUM" : 0x43F  # Card FPGA Firmware Revision Number.
        }

        # Vsource Range Enum
        self.TS_RANGE = {
            "AUTO" : 0,
            "LOW" : 1,
            "MED" : 2,
            "HIGH" : 3
        }

        # Input modes for Displacement Modules
        self.DM_MODE = {
            "LVDT_5_6_WIRE": 1,
            "LVDT_4_WIRE": 2,
            "RESOLVER": 3
        }

        err = self.handle.PIL_OpenSpecifiedCard(bus, slot, byref(self.card))
        if err != 0:
            error, err_str = self.ErrorMessage(err)
            raise(ValueError(err_str))

# Non-card specific functions
        
    def Version(self):
        ver = self.handle.PIL_Version()
        return ver

# Card specific functions

# Open/Close

    def OpenSpecifiedCard(self, bus, slot):
        err = self.handle.PIL_OpenSpecifiedCard(bus, slot, byref(self.card))
        return err
    
    def CloseSpecifiedCard(self):
        err = self.handle.PIL_CloseSpecifiedCard(self.card)
        return err    

# Card identity

    def CardId(self):
        err = self.handle.PIL_CardId(self.card, byref(self.string))
        return err, str(self.string.value.decode())

    def CardLoc(self):
        bus = c_uint(0)
        slot = c_uint(0)
        err = self.handle.PIL_CardLoc(self.card, byref(bus), byref(slot))
        return err, bus.value, slot.value
  
    def ClearCard(self):
        err = self.handle.PIL_ClearCard(self.card)
        return err
        
    def ClearSub(self, sub):
        err = self.handle.PIL_ClearSub(self.card, sub)
        return err
        
    def ClosureLimit(self, sub):
        limit = c_uint(0)
        err = self.handle.PIL_ClosureLimit(self.card, sub, byref(limit))
        return err, limit.value

    def Diagnostic(self):
        strng = create_string_buffer(100)
        err = self.handle.PIL_Diagnostic(self.card, byref(strng))
        return err, str(strng.value.decode())
    
    def EnumerateSubs(self):
        ins = c_uint(0)
        outs = c_uint(0)
        err = self.handle.PIL_EnumerateSubs(self.card, byref(ins), byref(outs))
        return err, ins.value, outs.value
    
    def ErrorMessage(self, code):
        err = self.handle.PIL_ErrorMessage(code, byref(self.string))
        return err, str(self.string.value.decode())
    
    def MaskBit(self, sub, bit, action):
        err = self.handle.PIL_MaskBit(self.card, sub, bit, action)
        return err
   
    def MaskCrosspoint(self, sub, row, column, action):
        err = self.handle.PIL_MaskCrosspoint(self.card, sub, row, column, action)
        return err
        
    def OpBit(self, sub, bit, action):
        err = self.handle.PIL_OpBit(self.card, sub, bit, action)
        return err
        
    def OpCrosspoint(self, sub, row, column, action):
        err = self.handle.PIL_OpCrosspoint(self.card, sub, row, column, action)
        return err
        
    def OpSwitch(self, sub, func, seg, sw, act):
        state = c_uint(0)
        err = self.handle.PIL_OpSwitch(self.card, sub, func, seg, sw, act, byref(state))
        return err, state.value
    
    def ReadBit(self, sub, bit):
        state = c_uint(0)
        err = self.handle.PIL_ReadBit(self.card, sub, bit, byref(state))
        return err, state.value
    
    def ReadCal(self, sub, index):
        data = c_uint(0)
        err = self.handle.PIL_ReadCal(self.card, sub, index, byref(data))
        return err, data.value

    def ReadSub(self, sub, data):
        # get size of subunit and create an array to hold the data
        e,t,rows,cols = self.SubInfo(self.card, sub)
        dwords = calc_dwords(rows * cols)
        err = self.handle.PIL_ReadMask(self.card, sub, byref(self.data))
        return err, dwords, self.data

    def SetCrosspointRange(self, sub, row, start_col, end_col, state):
        """ Sets all outputs on a row within a given range """
        DWORD_LEN = 32

        err, typeNum, rows, columns = self.SubInfo(sub, True)
        err, array, pattern = self.ViewSub(sub)

        # Some sanity checks on the input
        if start_col < 1:
            start_col = 1
        if end_col > columns:
            end_col = columns
        if row < 1:
            row = 1
        if row > rows:
            row = rows

        start_bit_offset = ((row - 1) * columns) + (start_col - 1)
        dword_offset = start_bit_offset // DWORD_LEN
        dword_bit_offset = start_bit_offset % DWORD_LEN

        length = end_col - start_col + 1
        bit_index = 0
        current_dword = dword_offset
        current_dword_bit = dword_bit_offset

        while bit_index < length:

            if state:
                pattern[current_dword] = pattern[current_dword] | (1 << current_dword_bit)
            else:
                pattern[current_dword] = pattern[current_dword] & ~(1 << current_dword_bit)

            bit_index += 1
            current_dword_bit += 1

            if current_dword_bit == 32:
                current_dword_bit = 0
                current_dword += 1

        err = self.WriteSub(sub, pattern)
        return err

    def SetMode(self, mode):
        oldmode = self.handle.PIL_SetMode(mode)
        return oldmode
    
    def SettleTime(self, sub):
        time = c_uint(0)
        err = self.handle.PIL_SettleTime(self.card, sub, byref(time))
        return err, time.value
    
    def Status(self):
        status = self.handle.PIL_Status(self.card)
        return status
    
    def SubAttribute(self, sub, out_not_in, code):
        attr = c_uint(0)
        err = self.handle.PIL_SubAttribute(self.card, sub, out_not_in, code, byref(attr))
        return err, attr.value
    
    def SubInfo(self, sub, out_not_in):
        stype = c_uint(0)
        rows = c_uint(0)
        cols = c_uint(0)
        err = self.handle.PIL_SubInfo(self.card, sub, out_not_in, byref(stype), byref(rows), byref(cols))
        return err, stype.value, rows.value, cols.value
    
    def SubSize(self, sub, out_not_in):
        e, t, r, c = self.SubInfo(sub, out_not_in)
        bits = r*c
        dwords = calc_dwords(bits)
        return dwords, bits       
    
    def SubStatus(self, sub):
        status = c_uint(0)
        err = self.handle.PIL_SubStatus(self.card, sub, byref(status))
        return err, status.value
    
    def SubType(self, sub, out_not_in):
        err = self.handle.PIL_SubType(self.card, sub, out_not_in, byref(self.string))
        return err, str(self.string.value.decode())
    
    def ViewBit(self, sub, bit):
        state = c_uint(0)
        err = self.handle.PIL_ViewBit(self.card, sub, bit, byref(state))
        return err, state.value
        
    def ViewCrosspoint(self, sub, row, column):
        state = c_uint(0)
        err = self.handle.PIL_ViewCrosspoint(self.card, sub, row, column, byref(state))
        return err, state.value

    def ViewMask(self, sub):
        # get size of subunit and create an array to hold the data
        e,t,rows,cols = self.SubInfo(self.card, sub)
        dwords = calc_dwords(rows * cols)
        err = self.handle.PIL_ViewMask(self.card, sub, byref(self.data))
        return err, dwords, self.data

    def ViewMaskBit(self, sub, bit):
        state = c_uint(0)
        err = self.handle.PIL_ViewMaskBit(self.card, sub, bit, byref(state))
        return err, state.value
    
    def ViewMaskCrosspoint(self, sub, row, column):
        state = c_uint(0)
        err = self.handle.PIL_ViewMaskCrosspoint(self.card, sub, row, column, byref(state))
        return err, state.value
    
    def ViewSub(self, sub):
        # get size of subunit and create an array to hold the data
        e,t,rows,cols = self.SubInfo(sub, 1)
        dwords = calc_dwords(rows * cols)
        err = self.handle.PIL_ViewSub(self.card, sub, byref(self.data))
        return err, dwords, self.data
    
    def WriteCal(self, sub, index, data):
        err = self.handle.PIL_WriteCal(self.card, sub, index, data)
        return err
    
    def WriteMask(self, sub, data):
        err = self.handle.PIL_WriteMask(self.card, sub, data)
        return err

    def WriteSub(self, sub, data):
        err = self.handle.PIL_WriteSub(self.card, sub, data)
        return err

    
# Attenuator card functions

    def AttenType(self, sub):
        err = self.handle.PIL_AttenType(self.card, sub, byref(self.string))
        return err, str(self.string.value.decode())
       
    def AttenInfo(self, sub):
        size = c_float(0.0)
        steps = c_uint(0)
        stype = c_uint(0)
        err = self.handle.PIL_AttenInfo(self.card, sub, byref(stype), byref(steps), byref(size))
        return err, stype.value, steps.value, size.value

    def SetAttenuation(self, sub, atten):
        err = self.handle.PIL_AttenSetAttenuation(self.card, sub, atten)
        return err
    
    def GetAttenuation(self, sub):
        atten = c_float(0.0)
        err = self.handle.PIL_AttenGetAttenuation(self.card, sub, byref(atten))
        return err, atten.value

    def PadValue(self, sub, pad):
        atten = c_float(0.0)
        err = self.handle.PIL_AttenPadValue(self.card, sub, byref(atten))
        return err, atten.value

# PSU card functions

    def PsuType(self, sub):
        err = self.handle.PIL_PsuType(self.card, sub, byref(self.string),100)
        return err, str(self.string.value.decode())
        
    def PsuInfo(self, sub):
        stype = c_uint(0)
        volts = c_double(0.0)
        amps = c_double(0.0)
        precis = c_uint(0)
        capb = c_uint(0)
        err = self.handle.PIL_PsuInfo(self.card, sub, byref(stype), byref(volts), byref(amps), byref(precis), byref(capb))
        return err, stype.value, volts.value, amps.value, precis.value, capb.value
    
    def PsuGetVoltage(self, sub):
        volts = c_double(0.0)
        err = self.handle.PIL_PsuGetVoltage(self.card, sub, byref(volts))
        return err, volts.value

    def PsuSetVoltage(self, sub, v):
        volts = c_double(v)
        err = self.handle.PIL_PsuSetVoltage(self.card, sub, volts)
        return err

    def PsuEnable(self, sub, enable):
        err = self.handle.PIL_PsuEnable(self.card, sub, enable)
        return err

# Battery Simulator Functions

    def BattSetVoltage(self, sub, v):
        volts = c_double(v)
        err = self.handle.PIL_BattSetVoltage(self.card, sub, volts)
        return err

    def BattGetVoltage(self, sub):
        volts = c_double(0.0)
        err = self.handle.PIL_BattGetVoltage(self.card, sub, byref(volts))
        return err, volts.value

    def BattSetCurrent(self, sub, curr):
        current = c_double(curr)
        err = self.handle.PIL_BattSetCurrent(self.card, sub, current)
        return err

    def BattGetCurrent(self, sub):
        current = c_double(0.0)
        err = self.handle.PIL_BattGetCurrent(self.card, sub, byref(current))
        return err, current.value

    def BattSetEnable(self, sub, pattern):
        err = self.handle.PIL_BattSetEnable(self.card, sub, pattern)
        return err

    def BattGetEnable(self, sub):
        pattern = c_uint(0)
        err = self.handle.PIL_BattGetEnable(self.card, sub, byref(pattern))
        return err, pattern.value

    def BattReadInterlockState(self,sub):
        state = c_uint(0)
        err = self.handle.PIL_BattReadInterlockState(self.card, sub, byref(state))
        return err, state.value

#Resistor Functions

    def ResSetResistance(self, sub, mode, resistance):
        md = c_uint(mode)
        res = c_double(resistance)
        err = self.handle.PIL_ResSetResistance(self.card, sub, md, res)
        return err

    def ResGetResistance(self, sub):
        resistance = c_double(0.0)
        err = self.handle.PIL_ResGetResistance(self.card, sub, byref(resistance))
        return err, resistance.value

    def ResInfo(self, sub):
        minres = c_double(0.0)
        maxres = c_double(0.0)
        refres = c_double(0.0)
        precpc = c_double(0.0)
        precdelta = c_double(0.0)
        int1 = c_double(0.0)
        intdelta = c_double(0.0)
        caps = c_uint(0)

        err = self.handle.PIL_ResInfo(self.card, sub,
                                      byref(minres),
                                      byref(maxres),
                                      byref(refres),
                                      byref(precpc),
                                      byref(precdelta),
                                      byref(int1),
                                      byref(intdelta),
                                      byref(caps))
        return err, minres.value, maxres.value, refres.value, precpc.value, precdelta.value, int1.value, intdelta.value,caps.value

# Thermocouple functions

    def VsourceSetVoltage(self, sub, voltage):
        mvolt = c_double(voltage)
        err = self.handle.PIL_VsourceSetVoltage(self.card, sub, mvolt)
        return err

    def VsourceGetVoltage(self, sub):
        mvolt = c_double(0.0)
        err = self.handle.PIL_VsourceGetVoltage(self.card, sub, byref(mvolt))
        return err, mvolt.value
    
    def VsourceSetRange(self, sub, ts_range):
        err = self.ERRORCODE["ER_BAD_RANGE"]
        isoutsub = 1
        if ts_range in self.TS_RANGE.values():
            tsrng = c_uint(ts_range)
            err = self.handle.PIL_SetAttribute(self.card, sub, isoutsub,
                self.ATTR["TS_SET_RANGE"], byref(tsrng))
        return err

    def VsourceGetRange(self, sub):
        err = self.ERRORCODE["ER_BAD_RANGE"]
        isoutsub = 1
        ts_range = c_uint(0)
        err = self.handle.PIL_GetAttribute(self.card, sub, isoutsub,
            self.ATTR["TS_SET_RANGE"], byref(ts_range))
        return err, ts_range.value

    def VsourceInfo(self, sub):
        is_output = c_uint32(1)

        low_range_min = c_double(0.0)
        low_range_med = c_double(0.0)
        low_range_max = c_double(0.0)
        low_range_max_dev = c_double(0.0)
        low_range_prec_pc = c_double(0.0)
        low_range_prec_delta = c_double(0.0)

        med_range_min = c_double(0.0)
        med_range_med = c_double(0.0)
        med_range_max = c_double(0.0)
        med_range_max_dev = c_double(0.0)
        med_range_prec_pc = c_double(0.0)
        med_range_prec_delta = c_double(0.0)

        high_range_min = c_double(0.0)
        high_range_med = c_double(0.0)
        high_range_max = c_double(0.0)
        high_range_max_dev = c_double(0.0)
        high_range_prec_pc = c_double(0.0)
        high_range_prec_delta = c_double(0.0)

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_MIN"],
                                            byref(low_range_min)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_MED"],
                                            byref(low_range_med)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_MAX"],
                                            byref(low_range_max)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_MAX_DEV"],
                                            byref(low_range_max_dev)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_PREC_PC"],
                                            byref(low_range_prec_pc)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_LOW_RANGE_PREC_DELTA"],
                                            byref(low_range_prec_delta)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_MIN"],
                                            byref(med_range_min)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_MED"],
                                            byref(med_range_med)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_MAX"],
                                            byref(med_range_max)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_MAX_DEV"],
                                            byref(med_range_max_dev)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_PREC_PC"],
                                            byref(med_range_prec_pc)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_MED_RANGE_PREC_DELTA"],
                                            byref(med_range_prec_delta)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_MIN"],
                                            byref(high_range_min)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_MED"],
                                            byref(high_range_med)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_MAX"],
                                            byref(high_range_max)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_MAX_DEV"],
                                            byref(high_range_max_dev)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_PREC_PC"],
                                            byref(high_range_prec_pc)
        )

        err = self.handle.PIL_GetAttribute(self.card, sub,
                                            is_output,
                                            self.ATTR["TS_HIGH_RANGE_PREC_DELTA"],
                                            byref(high_range_prec_delta)
        )

        return err, low_range_min.value, \
            low_range_med.value, \
            low_range_max.value, \
            low_range_max_dev.value, \
            low_range_prec_pc.value, \
            low_range_prec_delta.value, \
            med_range_min.value, \
            med_range_med.value, \
            med_range_max.value, \
            med_range_max_dev.value, \
            med_range_prec_pc.value, \
            med_range_prec_delta.value, \
            high_range_min.value, \
            high_range_med.value, \
            high_range_max.value, \
            high_range_max_dev.value, \
            high_range_prec_pc.value, \
            high_range_prec_delta.value

    def VsourceGetTemperature(self, unit):
        err = self.ERRORCODE["ER_BAD_ATTR_CODE"]
        is_output = 1
        sub = 1
        temperatures = (c_double * 4)(0.0, 0.0, 0.0, 0.0)
        if unit == self.ATTR["TS_TEMPERATURES_C"] or unit == self.ATTR["TS_TEMPERATURES_F"]:
            err = self.handle.PIL_GetAttribute(self.card, sub,
                                                is_output,
                                                unit,
                                                byref(temperatures)
            )
        return err, [temp for temp in temperatures]

    ### Deprecated Thermocouple Functions ###

    # def VsourceSetRange(self, sub, ts_range):
    #     rng = c_double(ts_range)
    #     err = self.handle.PIL_PIL_VsourceSetRange(self.card, sub, rng)
    #     return err

    # def VsourceGetRange(self, sub):
    #     rng = c_double(0.0)
    #     err = self.handle.PIL_VsourceGetRange(self.card, sub, byref(rng))
    #     return err, rng.value

    # def VsourceSetEnable(self, sub, pattern):
    #     err = self.handle.PIL_VsourceSetEnable(self.card, sub, pattern)
    #     return err
    
    # def VsourceGetEnable(self, sub):
    #     pattern = c_uint(0)
    #     err = self.handle.VsourceGetEnable(self.card, sub, byref(pattern))
    #     return err, pattern

    def SetCurrentmA(self, subunit, current):
        current = c_uint32(current)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["CURRENT_MA"],
                                                byref(current))
        return err

    def GetCurrentmA(self, subunit):
        current = c_uint32()
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["CURRENT_MA"],
                                                byref(current))
        return err, float(current.value)

    def SetVoltageV(self, subunit, voltage):
        voltage = c_uint32(voltage)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VOLTAGE_V"],
                                                byref(voltage))
        return err

    def GetVoltageV(self, subunit):
        voltage = c_uint32()
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VOLTAGE_V"],
                                                byref(voltage))
        return err, float(voltage.value)

    # VDT Functions 
    def VDTSetInputAtten(self, subunit, attenuation):
        attenuation = c_uint32(attenuation)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_AUTO_INPUT_ATTEN"],
                                                byref(attenuation))

        return err

    def VDTGetInputAtten(self, subunit):
        attenuation = c_uint32()
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_AUTO_INPUT_ATTEN"],
                                                byref(attenuation))
        return err, int(attenuation.value)

    def VDTSetABSPosition(self, subunit, position):
        position = c_uint32(position)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_ABS_POSITION"],
                                                byref(position))

        return err

    def VDTGetABSPosition(self, subunit):
        position = c_uint32()
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_ABS_POSITION"],
                                                byref(position))

        return err, int(position.value)

    def VDTSetABSPositionB(self, subunit, position):
        position = c_uint32(position)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_ABS_POSITION_B"],
                                                byref(position))

        return err

    def VDTGetABSPositionB(self, subunit):
        position = c_uint32()
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_ABS_POSITION_B"],
                                                byref(position))

        return err, int(position.value)

    def VDTSetPercentPosition(self, subunit, position):
        position = c_double(position)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PERCENT_POSITION"],
                                                byref(position))

        return err

    def VDTGetPercentPosition(self, subunit):
        subunit = c_uint32(subunit)
        position = c_double()
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PERCENT_POSITION"],
                                                byref(position))

        return err, int(position.value)

    def VDTSetPercentPositionB(self, subunit, position):
        position = c_double(position)
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PERCENT_POSITION_B"],
                                                byref(position))

        return err

    def VDTGetPercentPositionB(self, subunit):
        subunit = c_uint32(subunit)
        position = c_double()
        is_output = c_uint32(1)

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PERCENT_POSITION_B"],
                                                byref(position))

        return err, int(position.value)

    def VDTSetVsum(self, subunit, vsum):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        vsum = c_double(vsum)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_VOLTAGE_SUM"],
                                                byref(vsum))
        return err

    def VDTGetVsum(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        vsum = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_VOLTAGE_SUM"],
                                                byref(vsum))
        return err, float(vsum.value)

    def VDTSetVdiff(self, subunit, vdiff):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        vdiff = c_double(vdiff)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_VOLTAGE_DIFF"],
                                                byref(vdiff))
        return err

    def VDTGetVdiff(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        vdiff = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_VOLTAGE_DIFF"],
                                                byref(vdiff))
        return err, float(vdiff.value)

    def VDTSetOutGain(self, subunit, outgain):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        outgain = c_uint32(outgain)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_OUT_GAIN"],
                                                byref(outgain))
        return err

    def VDTGetOutGain(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        outgain = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_OUT_GAIN"],
                                                byref(outgain))
        return err, int(outgain.value)

    def VDTSetManualInputAtten(self, subunit, attenuation):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        attenuation = c_uint32(attenuation)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MANUAL_INPUT_ATTEN"],
                                                byref(attenuation))
        return err

    def VDTGetManualInputAtten(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        attenuation = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MANUAL_INPUT_ATTEN"],
                                                byref(attenuation))
        return err, int(attenuation.value)

    def VDTSetMode(self, subunit, mode):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        mode = c_uint32(mode)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MODE"],
                                                byref(mode))
        return err

    def VDTGetMode(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        mode = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MODE"],
                                                byref(mode))
        return err, int(mode.value)

    def VDTSetDelayA(self, subunit, delay):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        delay = c_uint32(delay)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_DELAY_A"],
                                                byref(delay))
        return err

    def VDTGetDelayA(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        delay = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_DELAY_A"],
                                                byref(delay))
        return err, int(delay.value)

    def VDTSetDelayB(self, subunit, delay):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        delay = c_uint32(delay)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_DELAY_B"],
                                                byref(delay))
        return err

    def VDTGetDelayB(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        delay = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_DELAY_B"],
                                                byref(delay))
        return err, int(delay.value)

    def VDTSetInputLevel(self, subunit, level):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        level = c_uint32(level)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INPUT_LEVEL"],
                                                byref(level))
        return err

    def VDTGetInputLevel(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        level = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INPUT_LEVEL"],
                                                byref(level))
        return err, int(level.value)

    def VDTSetInputFreq(self, subunit, freq):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        freq = c_uint32(freq)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INPUT_FREQ"],
                                                byref(freq))
        return err

    def VDTGetInputFreq(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        freq = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INPUT_FREQ"],
                                                byref(freq))
        return err, int(freq.value)

    def VDTSetOutLevel(self, subunit, level):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        level = c_uint32(level)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_OUT_LEVEL"],
                                                byref(level))
        return err

    def VDTGetOutLevel(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        level = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_OUT_LEVEL"],
                                                byref(level))
        return err, int(level.value)

    # LVDT mk2 Get only
    def VDTGetDSPICVersion(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        version = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_DSPIC_VERSION"],
                                                byref(version))
        return err, int(version.value)

    # LVDT mk2 Set/Get
    def VDTSetInvertA(self, subunit, state):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32(state)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INVERT_A"],
                                                byref(state))
        return err

    def VDTGetInvertA(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INVERT_A"],
                                                byref(state))
        return err, int(state.value)

    def VDTSetInvertB(self, subunit, state):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32(state)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INVERT_B"],
                                                byref(state))
        return err

    def VDTGetInvertB(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INVERT_B"],
                                                byref(state))
        return err, int(state.value)

    def VDTSetPhaseTracking(self, subunit, state):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32(state)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PHASE_TRACKING"],
                                                byref(state))
        return err

    def VDTGetPhaseTracking(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        state = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_PHASE_TRACKING"],
                                                byref(state))
        return err, int(state.value)

    def VDTSetSampleLoad(self, subunit, dword):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        dword = c_uint32(dword)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_SAMPLE_LOAD"],
                                                byref(dword))
        return err

    def VDTGetInputFreqHiRes(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        freq = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_INPUT_FREQ_HI_RES"],
                                                byref(freq))
        return err, int(freq.value)

    def VDTSetLOSThreshold(self, subunit, threshold):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        threshold = c_uint32(threshold)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_LOS_THRESHOLD"],
                                                byref(threshold))
        return err

    def VDTGetLOSThreshold(self, subunit, threshold):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        threshold = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_LOS_THRESHOLD"],
                                                byref(threshold))
        return err, int(threshold.value)

    def VDTSetSampleBufferSize(self, subunit, size):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        size = c_uint32(size)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_SMPL_BUFFER_SIZE"],
                                                byref(size))
        return err

    def VDTGetSampleBufferSize(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        size = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_SMPL_BUFFER_SIZE"],
                                                byref(size))
        return err, int(size.value)

    def VDTSetNullOffset(self, subunit, offset):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        offset = c_uint16(offset)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_NULL_OFFSET"],
                                                byref(offset))
        return err

    def VDTGetNullOffset(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        offset = c_uint16()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_NULL_OFFSET"],
                                                byref(offset))
        return err, int(offset.value)

    # LVDT Get only
    def VDTGetStatus(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        status = c_uint8()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_STATUS"],
                                                byref(status))
        return err, int(status.value)

    def VDTGetMaxOutputVoltage(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        voltage = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MAX_OUT_VOLTAGE"],
                                                byref(voltage))
        return err, float(voltage.value)

    def VDTGetMinOutputVoltage(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        voltage = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MIN_OUT_VOLTAGE"],
                                                byref(voltage))
        return err, float(voltage.value)

    def VDTGetMaxInputVoltage(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        voltage = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MAX_IN_VOLTAGE"],
                                                byref(voltage))
        return err, float(voltage.value)

    def VDTGetMinInputVoltage(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        voltage = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["VDT_MIN_IN_VOLTAGE"],
                                                byref(voltage))
        return err, float(voltage.value)

    # Resolver Get/Set
    def ResolverSetStartStopRotate(self, subunit, state):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        if state == True:
            state = c_uint32(1)
        else:
            state = c_uint32(0)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_START_STOP_ROTATE"],
                                                byref(state))
        return err

    def ResolverGetStartStopRotate(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        result = c_uint32()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_START_STOP_ROTATE"],
                                                byref(result))
        return err, bool(result.value)

    def ResolverSetNumOfTurns(self, subunit, turns):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        turns = c_uint16(turns)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_NUM_OF_TURNS"],
                                                byref(turns))
        return err

    def ResolverGetNumOfTurns(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        turns = c_uint16()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_NUM_OF_TURNS"],
                                                byref(turns))
        return err, float(turns.value)

    def ResolverSetRotateSpeed(self, subunit, speed):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        speed = c_double(speed)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_ROTATE_SPEED"],
                                                byref(speed))
        return err

    def ResolverGetRotateSpeed(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        speed = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_ROTATE_SPEED"],
                                                byref(speed))
        return err, float(speed.value)

    def ResolverSetPosition(self, subunit, position):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        position = c_double(position)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_POSITION"],
                                                byref(position))
        return err

    def ResolverGetPosition(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        position = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_POSITION"],
                                                byref(position))
        return err, float(position.value)

    def ResolverSetPosition0To360(self, subunit, position):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        position = c_double(position)

        err = self.handle.PIL_SetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_POSITION_0_360"],
                                                byref(position))
        return err
    
    def ResolverGetPosition0To360(self, subunit):
        subunit = c_uint32(subunit)
        is_output = c_uint32(1)
        position = c_double()

        err = self.handle.PIL_GetAttribute(
                                                self.card,
                                                subunit,
                                                is_output,
                                                self.ATTR["RESOLVER_POSITION_0_360"],
                                                byref(position))
        return err, position