{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T23:40:43.545696", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 35, "passed": 33, "failed": 2, "errors": 0, "timeouts": 0, "success_rate": 94.28571428571428, "total_execution_time": 3434.343784093857, "start_time": "2025-06-05T22:43:29.201066", "end_time": "2025-06-05T23:40:43.544851"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "PASSED", "return_code": 0, "execution_time": 115.33022332191467, "start_time": "2025-06-05T22:43:29.202202", "end_time": "2025-06-05T22:45:24.532427", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1a - MODE A ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10517799710226196\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -23.38,-24.22,-16.26,-22.38\n['-23.38', '-24.22', '-16.26', '-22.38']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1a - Top Ant: -22.38\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0179676298581475\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply Delay Initial: 3.0179676298581475\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply Delay Final: 3.0179676298581475\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1b Bot Ant\nTimeBase-Pulse:  0.09967838192946735\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -15.26,-21.89,-21.07,-24.89\n['-15.26', '-21.89', '-21.07', '-24.89']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step1b - Bot Ant: -24.89\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1222657286413806\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Initial: 3.1222657286413806\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 3.1222657286413806\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09414221109152737\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.00,-26.44,-18.51,-18.24\n['-20.00', '-26.44', '-18.51', '-18.24']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2a - Top Ant: -18.24\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0265512592069084\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Initial: 3.0265512592069084\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Final: 3.0265512592069084\nTimeBase-Pulse:  0.09978131413518186\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.36,-22.60,-15.92,-16.46\n['-20.36', '-22.60', '-15.92', '-16.46']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Step2b - Bot Ant: -16.46\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.9521261464309414\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay 2 Initial: 2.9521261464309414\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 2.9521261464309414\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Power's: -22.380000,-24.890000,-18.240000,-16.460000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: ReplyDelay's: 3.017968,3.122266,3.026551,2.952126\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a.py->Test_2_3_2_10_Step1a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "PASSED", "return_code": 0, "execution_time": 87.0049786567688, "start_time": "2025-06-05T22:45:24.533002", "end_time": "2025-06-05T22:46:51.537982", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1a - MODE A ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10009377104223703\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -17.74,-18.87,-14.07,-21.21\n['-17.74', '-18.87', '-14.07', '-21.21']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1a - Top Ant: -21.21\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1070814504537405\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply Delay Initial: 3.1070814504537405\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply Delay Final: 3.1070814504537405\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1b Bot Ant\nTimeBase-Pulse:  0.10924655985342131\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.82,-18.70,-20.78,-18.97\n['-22.82', '-18.70', '-20.78', '-18.97']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step1b - Bot Ant: -18.97\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0725065299094094\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Initial: 3.0725065299094094\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 3.0725065299094094\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09942781408679804\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -24.79,-22.68,-19.16,-17.96\n['-24.79', '-22.68', '-19.16', '-17.96']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2a - Top Ant: -17.96\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.026450625146918\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Initial: 3.026450625146918\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Top Reply 2 Delay Final: 3.026450625146918\nTimeBase-Pulse:  0.09735826450140053\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.43,-22.68,-17.16,-20.91\n['-18.43', '-22.68', '-17.16', '-20.91']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Step2b - Bot Ant: -20.91\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.8131441941680992\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay 2 Initial: 2.8131441941680992\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Bottom Reply Delay Final: 2.8131441941680992\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Power's: -21.210000,-18.970000,-17.960000,-20.910000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: ReplyDelay's: 3.107081,3.072507,3.026451,2.813144\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1a_11-14-23.py->Test_2_3_2_10_Step1a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "PASSED", "return_code": 0, "execution_time": 79.98638892173767, "start_time": "2025-06-05T22:46:51.538763", "end_time": "2025-06-05T22:48:11.525153", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1b - MODE CS ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10587158011625766\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -15.86,-26.66,-19.52,-24.62\n['-15.86', '-26.66', '-19.52', '-24.62']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step1a - Top Ant: -24.62\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.037811155468221\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step1b Bot Ant\nTimeBase-Pulse:  0.09903738768982527\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.28,-23.90,-16.02,-16.28\n['-20.28', '-23.90', '-16.02', '-16.28']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step1b - Bot Ant: -16.28\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.9288608438849826\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10856293131438273\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.87,-22.28,-20.53,-25.70\n['-20.87', '-22.28', '-20.53', '-25.70']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step2a - Top Ant: -25.7\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.905821093632652\nTimeBase-Pulse:  0.09420674262607412\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.86,-24.01,-13.52,-20.20\n['-20.86', '-24.01', '-13.52', '-20.20']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Step2b - Bot Ant: -20.2\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1696745048968795\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Power's: -24.620000,-16.280000,-25.700000,-20.200000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: ReplyDelay's: 3.037811,2.928861,2.905821,3.169675\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1b.py->Test_2_3_2_10_Step1b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "PASSED", "return_code": 0, "execution_time": 84.05433249473572, "start_time": "2025-06-05T22:48:11.525868", "end_time": "2025-06-05T22:49:35.580202", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RFBOB.py->__init__: Mock RFBOB initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step1b - MODE S ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1 SetUp for Top Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09171880004715077\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -19.83,-23.42,-19.45,-24.02\n['-19.83', '-23.42', '-19.45', '-24.02']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1a - Top Ant: -24.02\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.093539478640764\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1b Bot Ant\nTimeBase-Pulse:  0.10345157946347888\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.17,-24.30,-20.34,-16.28\n['-18.17', '-24.30', '-20.34', '-16.28']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step1b - Bot Ant: -16.28\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0894586135644033\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT BOTTOM\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2 SetUp for Bot Ant\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2a Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10017482800416784\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -19.65,-18.82,-20.66,-21.07\n['-19.65', '-18.82', '-20.66', '-21.07']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2a - Top Ant: -21.07\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.87115717032113\nTimeBase-Pulse:  0.09720741202026706\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.86,-22.77,-17.58,-24.49\n['-22.86', '-22.77', '-17.58', '-24.49']\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Step2b - Bot Ant: -24.49\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.151770083061001\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:SIG ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:TRIG:ANT TOP\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Power's: -24.020000,-16.280000,-21.070000,-24.490000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: ReplyDelay's: 3.093539,3.089459,2.871157,3.151770\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step1c.py->Test_2_3_2_10_Step1c: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "PASSED", "return_code": 0, "execution_time": 96.99033188819885, "start_time": "2025-06-05T22:49:35.580887", "end_time": "2025-06-05T22:51:12.571222", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEC ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeC: Mock Transponder Mode C Set\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: TestA SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.7878538092023, 96.28238427374747, 97.39304594527889, 97.26137821002796]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: TestB SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.19147643798568, 95.66930230970392, 97.3376574675126, 97.21783462595285]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: TestC SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.31581269261203, 96.08581561772654, 95.18793530751765, 94.0688689596704]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: TestD SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.08076079935469, 94.52718817714918, 97.20045667974689, 93.80964833559626]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: Test A: Reply Rates: 94.787854,96.282384\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: Test B: Reply Rates: 95.669302,97.191476\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: Test C: Reply Rates: 94.315813,96.085816\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: Test D: Reply Rates: 96.080761,94.527188\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "PASSED", "return_code": 0, "execution_time": 66.98638916015625, "start_time": "2025-06-05T22:51:12.571866", "end_time": "2025-06-05T22:52:19.558256", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEC ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeC: Mock Transponder Mode C Set\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: TestA SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.56871860751978, 94.52117420114034, 97.74968683941724, 93.72785489326543]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: TestB SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.16717163167489, 93.91943352488282, 97.91899970110676, 96.27655170141642]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: TestC SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.48206635270382, 93.14242403913777, 95.57254668002314, 95.45488168121918]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: TestD SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.74278390611666, 96.29196256901649, 94.48337324493447, 95.76519983244403]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: Test A: Reply Rates: 95.568719,94.521174\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: Test B: Reply Rates: 93.919434,97.167172\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: Test C: Reply Rates: 96.482066,93.142424\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: Test D: Reply Rates: 95.742784,96.291963\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "PASSED", "return_code": 0, "execution_time": 69.59597611427307, "start_time": "2025-06-05T22:52:19.559057", "end_time": "2025-06-05T22:53:29.155033", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2b ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestA SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.64672978222139, 95.15400324697396, 97.21825675400507, 96.15499043979834]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestB SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.59570620170652, 94.09877207519777, 94.39722804282718, 95.56835845598292]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestC SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.57233966959848, 96.24711409670024, 94.86605242901761, 95.86482607008905]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: TestD SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.125\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW -6.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.84353851991749, 95.84334724364653, 97.77512019032423, 96.10896683405453]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test A: Reply Rates: 96.646730,95.154003\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test B: Reply Rates: 93.595706,94.098772\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test C: Reply Rates: 96.572340,96.247114\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Test D: Reply Rates: 95.843539,95.843347\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "PASSED", "return_code": 0, "execution_time": 67.09833884239197, "start_time": "2025-06-05T22:53:29.155757", "end_time": "2025-06-05T22:54:36.254096", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step3 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEC ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeC: Mock Transponder Mode C Set\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: TestA SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.59139624511947, 94.19398202970943, 95.67270115936552, 94.2522421672646]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: TestB SetUp for Bot Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.16967583771607, 94.25172823316888, 94.76788744380207, 93.89991538610583]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 20000000000004\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: TestC SetUp for Top Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM 0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM 0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:POWER -54.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:POWER -37.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.72412983015738, 95.93990646926248, 97.0239436085527, 94.9676957223561]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: TestD SetUp for Bot Ant\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:TIM -0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:TIM -0.375\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENA:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENA:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENB:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENB:POWER -37.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GENC:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GENC:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:GEND:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:GEND:POWER -54.31\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.67802367677123, 93.72048711562903, 97.84670833051861, 96.38733855107104]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: Test A: Reply Rates: 94.591396,94.193982\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: Test B: Reply Rates: 95.169676,94.251728\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: Test C: Reply Rates: 96.724130,95.939906\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: Test D: Reply Rates: 93.678024,93.720487\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.4321415424346924, "start_time": "2025-06-05T22:54:36.255042", "end_time": "2025-06-05T22:54:36.687167", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "PASSED", "return_code": 0, "execution_time": 152.57321071624756, "start_time": "2025-06-05T22:54:36.688650", "end_time": "2025-06-05T22:57:09.261863", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1 Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Cableloss passed to the script: -12.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.61520374120538, 95.24798761817287, 97.02442290951232, 94.01788403937428]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.78387105753907, 94.60770090594231, 97.92133904392733, 94.3306547811177]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.46521553878308, 95.84826449400572, 95.96243625845759, 93.70894393556662]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.39394437422575, 96.20650296740405, 94.32892242271639, 95.67427116237761]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.61267464916853, 93.71231410491403, 94.70988175204951, 93.5342446700337]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.68330014491796, 93.98912713277028, 96.57687723973872, 94.76325630410953]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.28034381502138, 94.3830249923805, 96.45417404536973, 93.5728165046886]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.12778713579378, 96.74479541184122, 94.17227125638314, 97.44730633940974]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.85288936862057, 93.92668511712313, 96.17501488012012, 96.27839419112527]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.1351257835647, 95.92815614472529, 95.00156702101779, 95.21745880327123]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.83788212461823, 95.97011629062831, 95.25550824379842, 96.00865258307665]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.59432823863014, 93.50884651362622, 97.61412312712041, 97.12782738306481]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.87088087739733, 93.67701585498668, 97.85701546382231, 94.7609382677259]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.57680871210262, 94.02878619306065, 97.73692708226298, 96.6028767749495]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.55762739933517, 96.70862670351865, 95.29655851096868, 94.41339444890293]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 0, Frequency: 1029.80, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1029.80 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.5738293921422, 93.93339057006139, 95.85665778559186, 95.2349810318415]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.44905982326603, 95.08920547796194, 95.34465157853498, 97.052038444466]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.15717979980714, 94.97148655794673, 94.6340746012867, 93.62519537744816]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.389047395551, 95.14345637682565, 96.60552125366361, 95.15776268170154]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.50195491984634, 96.17045762411185, 95.83198085445679, 94.14607678845479]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.43852052888678, 95.73866305135267, 97.77659671229401, 96.65598010427324]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.87283671412845, 93.90581486472611, 94.95624793122407, 97.15996704110091]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.2319485027524, 93.98878868218043, 97.92257608055107, 96.43927980471074]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.60585118151994, 93.52333344612944, 96.14645161666793, 94.6365792742514]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.35190822628924, 95.54844325750345, 97.78947437615003, 96.98913839887143]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.4840141462755, 95.61370161996662, 96.8952098529334, 94.36531174727841]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.62039375617863, 96.09401783554058, 98.0463703796917, 96.93183155600266]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.18284348803262, 92.89164008699798, 96.05520698867488, 97.2258727275777]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.29546522226659, 95.69106193040584, 94.35930196372247, 94.41320788506192]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.18982590053442, 95.21479475011503, 94.99059227189508, 95.95333686685224]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 1, Frequency: 1030.00, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1030.00 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.20322523485629, 96.27359445010926, 96.27108653461126, 94.05756145019517]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.7471159664594, 96.72090720934776, 96.95317809818829, 96.9294390137061]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.99349068713042, 93.97499601059283, 95.13527736185542, 94.33046011721264]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.40889628733801, 94.49426336519298, 96.18682744543682, 95.45879104401787]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.3026729366499, 93.27849483421384, 96.8630905226052, 95.78860763459882]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.67343697167499, 93.68583088675622, 95.73186456946851, 93.64629515002729]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.93414716066745, 95.8145125228436, 96.9765560037488, 94.4590750556713]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.23614987358278, 94.80612402019085, 96.18735638878859, 94.14633848973824]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.15761506731508, 95.69004863862992, 95.83735491793644, 96.25102570016033]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.63865040830001, 94.76832320115811, 97.80023927818391, 96.65373260980934]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.24703858644628, 92.98991939902213, 94.82974416487437, 95.58024088574675]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.58588478585808, 93.6180730578608, 97.89682669939907, 93.69830666378493]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.15331795009182, 94.66609673854055, 94.83644356964778, 93.81452275470654]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.51375475045545, 95.22249440863374, 97.4236490677545, 95.76274769545581]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.57887269999529, 93.4874358715273, 94.64636049086442, 96.98179764026165]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: RESULT: Idx: 2, Frequency: 1030.20, Power_Level: -75.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1Freq: 1030.20 PowerLevel: -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Test_2_3_2_1_Step1 - [-63.0, -63.0, -63.0]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n[MOCK] TXD Python Lib: ate_rm.py->cleanup: Mock Resource Manager Closed.\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "PASSED", "return_code": 0, "execution_time": 246.06057620048523, "start_time": "2025-06-05T22:57:09.262856", "end_time": "2025-06-05T23:01:15.323434", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEC ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeC: Mock Transponder Mode C Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Test_2_3_2_1_Step2 Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:FREQ 1029.80\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.4168986635143, 96.15267784929536, 94.27618578083154, 95.199137358557]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.23054451857438, 96.70010074695358, 97.24216812648388, 94.10198450371888]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.63529217025878, 94.95856955034546, 94.32621983446198, 93.86529055960821]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.53913852886419, 96.30722930538323, 96.97628722221407, 94.1154357111364]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.19239654635513, 93.16054231030486, 96.53398527047209, 94.20844568441623]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.42122286630212, 93.49257409177685, 97.15802666741644, 96.19906143542568]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.55170534390254, 96.15727689648605, 97.54320834252673, 95.86949905295187]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.2679415946701, 96.35974494448749, 95.2844117048455, 93.76393994684288]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.40026538773719, 93.99460585580061, 97.62194650413309, 94.8411748361405]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.19864655256686, 95.27420137501083, 97.3778925441215, 94.3809705016474]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.24961775174069, 95.21862377347914, 97.07938039951279, 96.89862218064431]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.41647169783572, 96.2013096971568, 95.01225589656569, 95.87596422609016]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.63955292241255, 93.24080989429504, 97.59576258128978, 96.76730017792887]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.0188774003546, 96.71686247805265, 94.91052563581167, 93.61320528236764]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.95864945140376, 95.73151534055278, 96.21158761581276, 95.47649855096273]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: RESULT: Idx: 0, Frequency: 1029.80, Power_Level: -80.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Test_2_3_2_1_Step1Freq: 1029.80 PowerLevel: -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:FREQ 1030.00\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.1169808170379, 95.67946660555866, 96.33768500855163, 96.88193188288932]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.5941043177532, 94.23291935395261, 94.75660033954902, 95.61125009353565]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.02747481781212, 96.0638458910768, 95.75199196495012, 97.39889642549826]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.56476173260411, 95.51904328751453, 97.71511732864646, 94.9983088071692]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.98785778743415, 94.01291932142607, 97.45554872194046, 96.8301027299348]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.15011007390916, 96.40397233398474, 94.83919790904329, 94.48951606534317]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.98529430978856, 95.23428198881605, 96.44976123227156, 97.11209329809806]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.96554594901326, 95.12408495081512, 95.58949646306498, 97.49635492004337]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.95782802333392, 95.25901174341072, 95.4673258013307, 94.84586317549793]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.80073818493376, 95.22624037263273, 94.20805549823615, 97.45019147123891]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.9716020585003, 95.01616931434143, 94.74779963228306, 97.30572204312446]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.80983571327857, 93.73507447324273, 96.75817915274271, 95.88971699679577]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.92325694935104, 96.11192079587636, 95.25271849467457, 94.10417237371439]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.87187294879543, 94.59151198717875, 98.03052468913968, 96.62367052875932]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.87446722032607, 96.55539416887929, 94.36034671611523, 93.8391171774407]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: RESULT: Idx: 1, Frequency: 1030.00, Power_Level: -80.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Test_2_3_2_1_Step1Freq: 1030.00 PowerLevel: -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:FREQ 1030.20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.00088683481616, 93.17517179073045, 97.16189100100328, 95.90204528555529]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.5024791178644, 95.51847703047734, 94.29915258796274, 95.64751616393906]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.04773353229912, 94.73270904520297, 95.72016770133153, 97.37725904868985]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.24532058025405, 93.73528867523913, 95.28106802285441, 93.54492064659756]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.68904661048838, 94.98359849245237, 95.40886297141128, 95.54190837481583]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.85363607184406, 94.98879978486816, 95.09861831518384, 96.00900212043187]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.23876565322976, 93.76121614173574, 95.9738427804517, 94.44590309301348]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.44381622757294, 92.82126323190221, 94.6946880685439, 97.38151883016161]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.61508156246464, 95.48133957623882, 94.797963029584, 94.01647063639942]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.20191557167718, 94.46813372167668, 94.1800356799856, 95.7728027371468]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.81601900546714, 94.28151938439478, 96.17109896296223, 94.57123429123567]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -76.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.65610507082181, 94.57769901603288, 96.93498827127446, 94.77308680797317]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -77.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.53837884081521, 95.15847532760193, 98.02606605495386, 97.49551879179135]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -78.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.94098739440172, 93.16465552667145, 96.72425802908681, 93.62767607017784]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -79.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.44117734720017, 96.70884454735283, 95.60505286150557, 93.87725955141582]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -80.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: RESULT: Idx: 2, Frequency: 1030.20, Power_Level: -80.000000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Test_2_3_2_1_Step1Freq: 1030.20 PowerLevel: -80.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Test_2_3_2_1_Step2 - [-68.0, -68.0, -68.0]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "PASSED", "return_code": 0, "execution_time": 137.67493200302124, "start_time": "2025-06-05T23:01:15.323949", "end_time": "2025-06-05T23:03:32.998884", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 3 ***\n\n\n['-85.0', '-75.0', '-65.0', '-55.0', '-45.0', '-33.0']\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Test_2_3_2_1_Step3 ModeA - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.80762823009375, 94.19443538287153, 96.72293844317142, 95.96794922108427]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -85.0, ReplyRate 94.194435\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.52736181758098, 94.37624533033626, 96.18920241350867, 94.07865155333468]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -75.0, ReplyRate 94.376245\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.39855193465381, 94.37515568328286, 94.87773767039943, 95.1057106687085]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -65.0, ReplyRate 94.375156\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -55.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -55.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.30342976534097, 96.18151855312837, 97.02107138055297, 95.30119069743618]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -55.0, ReplyRate 96.181519\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -45.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -45.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.69171116600415, 94.34607222560253, 94.11499140621942, 97.4968971563136]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -45.0, ReplyRate 94.346072\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.09619337772168, 94.68254307809208, 97.75671789990028, 96.37287819108141]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: RESULT: Power -33.0, ReplyRate 94.682543\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Test_2_3_2_1_Step3 End: [94.19443538287153, 94.37624533033626, 94.37515568328286, 96.18151855312837, 94.34607222560253, 94.68254307809208, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "PASSED", "return_code": 0, "execution_time": 42.65022397041321, "start_time": "2025-06-05T23:03:32.999801", "end_time": "2025-06-05T23:04:15.650026", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 4 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 100\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: Test_2_3_2_1_Step4 - Power at -81dBm, Get Reply Rate\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.75700441769239, 94.36803965544111, 97.65766501093718, 94.61470955052351]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: Test_2_3_2_1_Step4 - Done: 94.61470955052351\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "PASSED", "return_code": 0, "execution_time": 72.69070625305176, "start_time": "2025-06-05T23:04:15.651032", "end_time": "2025-06-05T23:05:28.341739", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 5 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Test_2_3_2_1_Step5 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -60.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.71556717423458, 92.83443084482292, 96.59605368486439, 96.61219352726907]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.51631079596514, 95.88224802690925, 94.97789134559449, 93.6008367267332]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.62028102975256, 93.55173038896228, 96.66317597717983, 94.81067184698901]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -63.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.90943034087853, 94.74327614708861, 97.95906512490876, 95.3610736066973]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -64.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.48647376202632, 95.37305434107975, 98.03972799041212, 95.08302025190076]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -65.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.14119517172315, 95.62572293625594, 95.60809616740947, 94.92697375876114]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -66.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.91895418873412, 94.51090888108773, 94.45508803406938, 95.79899812655194]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -67.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.81916790767266, 95.03257714597493, 97.95325965383363, 96.73050253239106]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -68.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.95606414570068, 93.21904810023106, 95.23179404832966, 93.68332846878889]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -69.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.60571123533936, 96.29901738616755, 94.73327441904627, 95.69494938322228]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -70.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.88942553461533, 93.65978180977409, 97.38989906329343, 94.29963780601534]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -71.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.17485902329389, 93.93541827379542, 97.03541105651016, 96.1880381528592]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -72.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.0514947834673, 95.69710240092331, 95.2295131929733, 95.55459909002487]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -73.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.0866745499031, 94.28934280150023, 97.1987881322068, 95.28553693356379]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -74.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.0754990630671, 94.95086908068124, 97.44277338928889, 95.43673961870729]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -75.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Test_2_3_2_1_Step5 - Done: -63.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "PASSED", "return_code": 0, "execution_time": 108.02493739128113, "start_time": "2025-06-05T23:05:28.342382", "end_time": "2025-06-05T23:07:16.367321", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 6 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Test_2_3_2_1_Step6 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -85.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.67326143725472, 96.72387920386798, 94.15638022529099, 95.69433895882713]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -62.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.59449855920548, 93.40403796328762, 95.22191613260372, 95.92088170604087]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.96479260977279, 94.84901500771905, 96.81181323224472, 94.25988089821587]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Test_2_3_2_1_Step6 - Done: [96.72387920386798, 93.40403796328762, 94.84901500771905]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "PASSED", "return_code": 0, "execution_time": 32.45052194595337, "start_time": "2025-06-05T23:07:16.367811", "end_time": "2025-06-05T23:07:48.818334", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: ***DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 7 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Test_2_3_2_1_Step7 - Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: ATC Power Level: -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Pwr: :ATC:XPDR:POW -93.0\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.45128680556562, 93.51925271185465, 96.57838195266964, 93.92890855919443]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Test_2_3_2_1_Step7 - Done 93.51925271185465\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 36.869831800460815, "start_time": "2025-06-05T23:07:48.819237", "end_time": "2025-06-05T23:08:25.689059", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: *** DO-181E, Reply Transmission Frequency,Sect 2.3.2.2.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseFrequency: Mock Frequency: 1029.667183216106\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: RESULT1: Frequency 1029.667183\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 50\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseFrequency: Mock Frequency: 1030.2006135051563\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: RESULT2: Frequency 1030.200614\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: Test_2_3_2_2_1 - Done: [1029.667183216106, 1030.2006135051563]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 215.79734420776367, "start_time": "2025-06-05T23:08:25.689926", "end_time": "2025-06-05T23:12:01.487273", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10312256771229876\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.22,-18.26,-19.51,-20.38\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09928119710088985\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -17.46,-23.20,-17.24,-25.32\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10137237824443282\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.75,-20.94,-20.52,-22.58\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09074063214327922\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.44,-23.91,-19.02,-17.54\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2.py->Test_2_3_2_2_2: Test_2_3_2_2_2 - Done: [-20.38, -25.32, -22.58, -17.54]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2.py->Test_2_3_2_2_2: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "PASSED", "return_code": 0, "execution_time": 161.4132945537567, "start_time": "2025-06-05T23:12:01.488081", "end_time": "2025-06-05T23:14:42.901379", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2_11-14-23.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.10682289906304539\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -21.46,-17.99,-21.09,-17.35\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09286682778681216\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -22.08,-25.68,-21.64,-25.57\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09152431103686166\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -17.04,-25.47,-18.42,-21.97\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nTimeBase-Pulse:  0.09867445545288527\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -15.72,-24.08,-19.74,-25.36\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2_11-14-23.py->Test_2_3_2_2_2: Test_2_3_2_2_2 - Done: [-17.35, -25.57, -21.97, -25.36]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2_11-14-23.py->Test_2_3_2_2_2: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "PASSED", "return_code": 0, "execution_time": 38.98877429962158, "start_time": "2025-06-05T23:14:42.901930", "end_time": "2025-06-05T23:15:21.890707", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4677102206649875\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PulseWidth: 0.4677102206649875\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PulseWidth Final: 0.0004677102206649875\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 13\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 13\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9932537366403618\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Message Width: 1.9932537366403618\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Message Width Final: 0.001993253736640362\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Test_2_3_2_3_1 - Begin Power Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0812239325445088\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.0812239325445088\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0231288590363055\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.0231288590363055\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: PowerLevel: -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.025671710065435\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: VAL: 3.025671710065435\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Test_2_3_2_3_1 : Done, MsgWidth: 0.001993253736640362, Widths: 0.0004677102206649875, Delays: [3.0812239325445088, 3.0231288590363055, 3.025671710065435]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "PASSED", "return_code": 0, "execution_time": 60.8806791305542, "start_time": "2025-06-05T23:15:21.891405", "end_time": "2025-06-05T23:16:22.772084", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 6 pos edges, 6 neg edges\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PEdges: 6,[0.6338732152064457, 0.7156761277455272, 0.6463710146519345, 0.9256611628367479, 0.4090556730577085, 0.9857804585778646] \n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: NEdges: 6,[0.8096831246556274, 0.5480100521274395, 0.24030679664811339, 0.23133039625361929, 0.08186487200863057, 0.5293264825763622]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Pulse duration: True\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Test_2_3_2_3_1 - Begin Power Loop\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 2.852787831683504\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 2.852787831683504\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.1602223057730687\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 3.1602223057730687\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: PowerLevel: -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getReplyDelay: Mock Reply Delay: 3.0230698915442393\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: VAL: 3.0230698915442393\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Test_2_3_2_3_1 : Done, MsgWidth: 0.0, Widths: 0.0, Delays: [2.852787831683504, 3.1602223057730687, 3.0230698915442393]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "PASSED", "return_code": 0, "execution_time": 97.20666027069092, "start_time": "2025-06-05T23:16:22.772963", "end_time": "2025-06-05T23:17:59.979623", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PRF 1\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PRF 1\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.42861320317942647\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth1: 0.42861320317942647\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth1 Final: 0.00042861320317942646\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getRiseTime: Mock Rise Time: 0.10687147773272528\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: RiseTime: 0.10687147773272528\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: RiseTime Final: 0.00010687147773272527\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getFallTime: Mock Fall Time: 0.1006243586605452\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: FallTime: 0.1006243586605452\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: FallTime Final: 0.0001006243586605452\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.025226239737935\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P1: 2.025226239737935\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P1 Final: 0.002025226239737935\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4949161896974855\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth2: 0.4949161896974855\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth2 Final: 0.0004949161896974856\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.03051603466807\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P2: 2.03051603466807\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P2 Final: 0.00203051603466807\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4190161663751666\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth3: 0.4190161663751666\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth3 Final: 0.0004190161663751666\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.900832870103496\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P3: 1.900832870103496\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P3 Final: 0.001900832870103496\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.48002483098656906\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth4: 0.48002483098656906\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PulseWidth4 Final: 0.00048002483098656906\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.9110506601995083\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P4: 1.9110506601995083\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P4 Final: 0.0019110506601995084\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:DFORMAT 2\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:DFORMAT 2\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.0847862013813594\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P5: 2.0847862013813594\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Pulse Position for P5 Final: 0.0020847862013813594\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.10071928409001601\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -21.62,-22.44,-16.71,-22.36\n\n*NumPULSEs:  True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: NumPULSEs: 1\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.71,-20.29,-16.37,-23.31\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Final Power Max: -23.310000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Final Power Min: -23.310000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Test_2_3_2_3_2a - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "PASSED", "return_code": 0, "execution_time": 30.336642265319824, "start_time": "2025-06-05T23:17:59.980501", "end_time": "2025-06-05T23:18:30.317145", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 7 pos edges, 7 neg edges\nPEdges:  7 [0.10803444914739135, 0.8667372930358185, 0.7040130751157294, 0.09843590704662264, 0.6605497693902862, 0.8306271943539647, 0.09814233085818513]\nNEdges:  7 [0.3776968908626962, 0.02970974106541513, 0.7876354612839153, 0.8077806333197906, 0.19191883199616477, 0.46073280614846723, 0.3939805723698514]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Pulse fall: True\n*** MESSAGE SPAN *** 285946.12322246004\n*** PreAmble Span *** 552515.3202428948\n*** Data Span *** -266569.1970204348\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.09898913600776828\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -21.84,-20.08,-13.20,-16.29\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -18.96,-19.86,-15.69,-21.72\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Test_2_3_2_3_2a - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "PASSED", "return_code": 0, "execution_time": 106.25730395317078, "start_time": "2025-06-05T23:18:30.317891", "end_time": "2025-06-05T23:20:16.575197", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000A00001\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000A00001\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.40555312111120806\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth1: 0.40555312111120806\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth1 Final: 0.00040555312111120804\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getRiseTime: Mock Rise Time: 0.09166970515275374\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: RiseTime: 0.09166970515275374\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: RiseTime Final: 9.166970515275374e-05\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getFallTime: Mock Fall Time: 0.09593956925925591\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: FallTime: 0.09593956925925591\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: FallTime Final: 9.593956925925591e-05\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.000056063150824\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P1: 2.000056063150824\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P1 Final: 0.002000056063150824\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 15\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.4533004639962857\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth2: 0.4533004639962857\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth2 Final: 0.00045330046399628574\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.942204742734152\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P2: 1.942204742734152\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P2 Final: 0.001942204742734152\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 16\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.48273661085216363\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth3: 0.48273661085216363\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth3 Final: 0.00048273661085216366\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.914209724200188\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P3: 1.914209724200188\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P3 Final: 0.0019142097242001881\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 17\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulseWidth: Mock Pulse Width: 0.45165292148257025\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth4: 0.45165292148257025\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PulseWidth4 Final: 0.00045165292148257024\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 1.996505880930809\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P4: 1.996505880930809\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P4 Final: 0.001996505880930809\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:MEA:SET:PUL 30\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->getPulsePosition: Mock Pulse Position: 2.0912460561016775\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P5: 2.0912460561016775\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Pulse Position for P5 Final: 0.0020912460561016777\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.10313425764232456\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -20.45,-22.49,-19.14,-16.99\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -23.55,-23.83,-20.76,-23.73\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Final Power Max: -23.730000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Final Power Min: -23.730000\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Test_2_3_2_3_2b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "PASSED", "return_code": 0, "execution_time": 30.004162311553955, "start_time": "2025-06-05T23:20:16.576121", "end_time": "2025-06-05T23:20:46.580285", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 28800000000004\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step1_Step2_Step4 - Start\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 6 pos edges, 6 neg edges\nPEdges:  6 [0.8896421315139627, 0.06975565402995776, 0.7809180680798076, 0.6339203763649361, 0.9255518437799952, 0.03725273910490989]\nNEdges:  6 [0.45166462208438396, 0.3034706614282542, 0.05970295530746661, 0.536322020667963, 0.940986065775631, 0.28447374515781343]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse duration: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse rise: True\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Pulse fall: True\n*** MESSAGE SPAN *** -605168.3863561492\n*** PreAmble Span *** 35909.71226603257\n*** Data Span *** -641078.0986221818\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *Test_2_3_2_3_2a_Step3 - Start\nTimeBase-Pulse:  0.09347720050120482\nTSPAN:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -16.64,-24.52,-15.47,-17.07\n\n*NumPULSEs:  True\n\n\nPULSE :  0\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -21.90,-20.46,-20.96,-23.77\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: PowerDiff: 0.000000\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Test_2_3_2_3_2b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "PASSED", "return_code": 0, "execution_time": 88.38662362098694, "start_time": "2025-06-05T23:20:46.581060", "end_time": "2025-06-05T23:22:14.967685", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: *** DO-181E, Side Lobe Supression: Sect 2.3.2.4 ***\n\n\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.59368360895432, 96.39984881091665, 95.9868796294524, 97.21004157841551]\nRESULT: Power -61.0, ReplyRate 96.399849\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -61.0 ReplyRate: 96.39984881091665\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -48.0\n:ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.3044926007473, 93.04560701127986, 95.32554603910766, 95.75461475407104]\nRESULT: Power -48.0, ReplyRate 93.045607\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -48.0 ReplyRate: 93.04560701127986\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -28.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -28.0\n:ATC:XPDR:POW -28.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.22748053166704, 94.50751666473423, 94.76900282872934, 93.9863581251261]\nRESULT: Power -28.0, ReplyRate 94.507517\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -28.0 ReplyRate: 94.50751666473423\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.07164225260915, 94.34696364697199, 97.65140770236691, 94.10760090265185]\nRESULT: Power -9.0, ReplyRate 94.346964\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step1_2Pwr: -9.0 ReplyRate: 94.34696364697199\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:SLS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P2POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P2POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3 - Start\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.86862541629642, 93.01747182266243, 96.95414814360272, 95.30662066790907]\nRESULT: Power -61.0, ReplyRate 93.017472\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -61.0 ReplyRate: 93.01747182266243\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.8919765965072, 95.39224427833496, 95.96830671688248, 97.13549581437651]\nRESULT: Power -38.0, ReplyRate 95.392244\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -38.0 ReplyRate: 95.39224427833496\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.85618557088303, 94.67294477126168, 94.79570007254891, 96.70927425913547]\nRESULT: Power -9.0, ReplyRate 94.672945\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4_Step3Pwr: -9.0 ReplyRate: 94.67294477126168\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Test_2_3_2_4 - Done: [96.39984881091665, 93.04560701127986, 94.50751666473423, 94.34696364697199][93.01747182266243, 95.39224427833496, 94.67294477126168]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "PASSED", "return_code": 0, "execution_time": 27.761391639709473, "start_time": "2025-06-05T23:22:14.968450", "end_time": "2025-06-05T23:22:42.729842", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step1 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nCMD:  :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.82287795605092, 94.18488558235842, 97.84337285959565, 95.2841547056083]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: Test_2_3_2_5_Step1Pwr: -10 Reply Rate: [93.82287795605092, 94.18488558235842, 97.84337285959565, 95.2841547056083]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: Results: -10[93.82287795605092, 94.18488558235842, 97.84337285959565, 95.2841547056083]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: Test_2_3_2_5_Step1 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "PASSED", "return_code": 0, "execution_time": 68.68156743049622, "start_time": "2025-06-05T23:22:42.730714", "end_time": "2025-06-05T23:23:51.412282", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step2 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA_ONLY ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA_Only: Mock Transponder Mode A Only Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nCMD:  :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -10\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.83393511494278, 94.60947695053, 98.04568895362603, 94.38279988603338]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -10 Reply Rate: [96.83393511494278, 94.60947695053, 98.04568895362603, 94.38279988603338]\nCMD:  :ATC:XPDR:PUL:P4POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.46565438688162, 93.16426996088107, 94.89293537707994, 95.31780294563626]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -9 Reply Rate: [94.46565438688162, 93.16426996088107, 94.89293537707994, 95.31780294563626]\nCMD:  :ATC:XPDR:PUL:P4POWER -8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.64584728015541, 96.4832299504081, 94.42323514508762, 93.9894316943426]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -8 Reply Rate: [94.64584728015541, 96.4832299504081, 94.42323514508762, 93.9894316943426]\nCMD:  :ATC:XPDR:PUL:P4POWER -7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.79612054394748, 92.92912956506197, 96.26839433108557, 93.93095233819882]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -7 Reply Rate: [95.79612054394748, 92.92912956506197, 96.26839433108557, 93.93095233819882]\nCMD:  :ATC:XPDR:PUL:P4POWER -6\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -6\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -6\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.11651875813202, 94.24937129228088, 94.50183599822705, 96.11196687399672]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -6 Reply Rate: [97.11651875813202, 94.24937129228088, 94.50183599822705, 96.11196687399672]\nCMD:  :ATC:XPDR:PUL:P4POWER -5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.99778479791115, 95.80039254898209, 97.28757209691955, 97.24714102858277]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -5 Reply Rate: [95.99778479791115, 95.80039254898209, 97.28757209691955, 97.24714102858277]\nCMD:  :ATC:XPDR:PUL:P4POWER -4\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -4\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -4\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.35975995257954, 92.98477912773066, 96.71457157210877, 96.67673998744702]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -4 Reply Rate: [93.35975995257954, 92.98477912773066, 96.71457157210877, 96.67673998744702]\nCMD:  :ATC:XPDR:PUL:P4POWER -3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -3\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.7707588213046, 94.78766742308032, 95.24800792384593, 93.52050367886054]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -3 Reply Rate: [94.7707588213046, 94.78766742308032, 95.24800792384593, 93.52050367886054]\nCMD:  :ATC:XPDR:PUL:P4POWER -2\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -2\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -2\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.34737912380048, 95.0251514309023, 95.09792225666753, 95.90808631204234]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -2 Reply Rate: [93.34737912380048, 95.0251514309023, 95.09792225666753, 95.90808631204234]\nCMD:  :ATC:XPDR:PUL:P4POWER -1\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4POWER -1\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4POWER -1\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.31176230024576, 95.24426783750107, 97.75731369983934, 96.48377542957789]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2Pwr: -1 Reply Rate: [96.31176230024576, 95.24426783750107, 97.75731369983934, 96.48377542957789]\nResults:  0 [96.31176230024576, 95.24426783750107, 97.75731369983934, 96.48377542957789]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Test_2_3_2_5_Step2 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "PASSED", "return_code": 0, "execution_time": 46.47957515716553, "start_time": "2025-06-05T23:23:51.413006", "end_time": "2025-06-05T23:24:37.892582", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step3 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 7.8usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.89589430526249, 94.49349432438044, 98.00682013462747, 95.01875112831449]\nReply Rate:  [95.89589430526249, 94.49349432438044, 98.00682013462747, 95.01875112831449]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 8.2usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 7.8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.48402788890074, 94.2910735359975, 94.98978049004184, 95.85416844210381]\nReply Rate:  [94.48402788890074, 94.2910735359975, 94.98978049004184, 95.85416844210381]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3a - ModeA P1P3 9.0usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.06080801718865, 95.7919161305771, 96.00163413166541, 97.44359386677428]\nReply Rate:  [95.06080801718865, 95.7919161305771, 96.00163413166541, 97.44359386677428]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3b - ModeCS P1P3 21.2usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 21.2\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 21.2\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.25433746368891, 92.82048735753436, 94.93217897150514, 97.19871968790824]\nReply Rate:  [93.25433746368891, 92.82048735753436, 94.93217897150514, 97.19871968790824]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3b - ModeCS P1P3 20.8usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 20.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 20.8\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.32012944244748, 95.91924157131143, 95.45443958330735, 94.13755706403171]\nReply Rate:  [94.32012944244748, 95.91924157131143, 95.45443958330735, 94.13755706403171]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3b - ModeCS P1P3 18.0usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P13SPACING 18.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P13SPACING 18.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.43681930156743, 95.51550040340265, 96.05453529295468, 94.87424127688365]\nReply Rate:  [93.43681930156743, 95.51550040340265, 96.05453529295468, 94.87424127688365]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Test_2_3_2_5_Step3 - Done: [94.49349432438044, 94.2910735359975, 95.7919161305771][97.19871968790824, 94.13755706403171, 94.87424127688365]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "PASSED", "return_code": 0, "execution_time": 62.8605318069458, "start_time": "2025-06-05T23:24:37.893466", "end_time": "2025-06-05T23:25:40.753999", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step4 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: Test_2_3_2_5_Step4a - ModeA/S P3P4 2.2usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P34SPACING 2.1\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P34SPACING 2.1\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.8788154230223, 96.09721487080839, 95.96251451860041, 95.80161868298167]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: Test_2_3_2_5_Step4b - ModeA/s P3P4 1.8usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P34SPACING 1.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P34SPACING 1.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.68075500523, 95.71688576832918, 96.49800177001886, 96.98129325404105]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: Test_2_3_2_5_Step4c - ModeA/S P3P4 3.0usec\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P34SPACING 3.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P34SPACING 3.0\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.32692817480694, 96.33588955703645, 96.29878497554442, 97.47243517304244]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: Test_2_3_2_5_Step4 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "PASSED", "return_code": 0, "execution_time": 231.9465250968933, "start_time": "2025-06-05T23:25:40.754734", "end_time": "2025-06-05T23:29:32.701260", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step5 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5a - Mode A\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.23457334472684, 96.76563368299247, 96.88751838120513, 94.00735912602077]\nReply Rate:  [96.23457334472684, 96.76563368299247, 96.88751838120513, 94.00735912602077]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.86205626619197, 96.55848424070712, 94.12590299493036, 96.53260315518074]\nReply Rate:  [96.86205626619197, 96.55848424070712, 94.12590299493036, 96.53260315518074]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.79876131629322, 93.35134065266749, 96.83469036445611, 95.0836296881439]\nReply Rate:  [96.79876131629322, 93.35134065266749, 96.83469036445611, 95.0836296881439]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.93169213439852, 93.13888890263917, 94.28373093316702, 96.82480930515057]\nReply Rate:  [94.93169213439852, 93.13888890263917, 94.28373093316702, 96.82480930515057]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.72914491828863, 94.66782347234462, 95.97740793024246, 93.54368110228563]\nReply Rate:  [95.72914491828863, 94.66782347234462, 95.97740793024246, 93.54368110228563]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.70503672008472, 95.49654892260415, 94.8457808768736, 94.24545035195615]\nReply Rate:  [93.70503672008472, 95.49654892260415, 94.8457808768736, 94.24545035195615]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5a - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEC ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeC: Mock Transponder Mode C Set\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5b - Mode C\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.18423980061463, 93.32754634584597, 96.97994679921287, 96.14084518999888]\nReply Rate:  [96.18423980061463, 93.32754634584597, 96.97994679921287, 96.14084518999888]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.89778805298323, 95.20859305704278, 96.98680850866997, 96.52599539940822]\nReply Rate:  [94.89778805298323, 95.20859305704278, 96.98680850866997, 96.52599539940822]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.23565048717018, 94.48543099087784, 96.51990805283882, 94.39816787893643]\nReply Rate:  [93.23565048717018, 94.48543099087784, 96.51990805283882, 94.39816787893643]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.93417692753123, 96.25766836436566, 94.80538672724951, 94.93339476219232]\nReply Rate:  [93.93417692753123, 96.25766836436566, 94.80538672724951, 94.93339476219232]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.61905137131744, 94.39308640644052, 95.51655979683453, 95.86056968176321]\nReply Rate:  [96.61905137131744, 94.39308640644052, 95.51655979683453, 95.86056968176321]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.7765441719772, 96.04576819950623, 98.09717957459755, 96.92734736295414]\nReply Rate:  [93.7765441719772, 96.04576819950623, 98.09717957459755, 96.92734736295414]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5b - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5c - Mode AS\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.56669410075723, 93.68517725544326, 97.71215405779786, 96.1095033067768]\nReply Rate:  [96.56669410075723, 93.68517725544326, 97.71215405779786, 96.1095033067768]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.6497764390254, 93.00438029736986, 97.37317383290585, 97.1462911097056]\nReply Rate:  [93.6497764390254, 93.00438029736986, 97.37317383290585, 97.1462911097056]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P1: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.84654364552698, 95.59983928018856, 97.41029586373449, 94.79821580800717]\nReply Rate:  [94.84654364552698, 95.59983928018856, 97.41029586373449, 94.79821580800717]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.64650346653968, 95.66032769268003, 96.69822321834678, 95.90034006204851]\nReply Rate:  [93.64650346653968, 95.66032769268003, 96.69822321834678, 95.90034006204851]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.84311549485763, 95.38532543077815, 97.53853833994475, 93.79463328734693]\nReply Rate:  [94.84311549485763, 95.38532543077815, 97.53853833994475, 93.79463328734693]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->vary_P3: Test_2_3_2_5_Step5:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.06131180480145, 94.95256793722977, 94.33875101398264, 94.49399846784976]\nReply Rate:  [95.06131180480145, 94.95256793722977, 94.33875101398264, 94.49399846784976]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5c - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Test_2_3_2_5_Step5 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "PASSED", "return_code": 0, "execution_time": 217.71638464927673, "start_time": "2025-06-05T23:29:32.702027", "end_time": "2025-06-05T23:33:10.418413", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step6 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Test_2_3_2_5_Step6a - ModeA/S\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.49233835441657, 95.8261702793861, 96.5463633584225, 94.31807841335511]\nReply Rate:  [96.49233835441657, 95.8261702793861, 96.5463633584225, 94.31807841335511]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.75758010856511, 96.70140608376971, 95.63142045440696, 94.85297258949439]\nReply Rate:  [95.75758010856511, 96.70140608376971, 95.63142045440696, 94.85297258949439]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.69420174677127, 95.01868094775672, 95.78144088092071, 97.02933701954791]\nReply Rate:  [94.69420174677127, 95.01868094775672, 95.78144088092071, 97.02933701954791]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.11101248416674, 96.39382992503788, 96.25813251916415, 94.09187085669437]\nReply Rate:  [96.11101248416674, 96.39382992503788, 96.25813251916415, 94.09187085669437]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.52716965306621, 96.33956358918316, 97.91685568640933, 94.33792653998472]\nReply Rate:  [94.52716965306621, 96.33956358918316, 97.91685568640933, 94.33792653998472]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.7857326237115, 93.33045864693277, 96.30324221026306, 96.43354682322044]\nReply Rate:  [94.7857326237115, 93.33045864693277, 96.30324221026306, 96.43354682322044]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.90772354695967, 93.61652439732114, 94.83274054253197, 95.34654745849603]\nReply Rate:  [93.90772354695967, 93.61652439732114, 94.83274054253197, 95.34654745849603]\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.98922183706418, 96.37262445311111, 97.2520710668926, 94.34290269553055]\nReply Rate:  [95.98922183706418, 96.37262445311111, 97.2520710668926, 94.34290269553055]\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.95\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.95\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.95\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.95\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.00432687636976, 96.06361681557951, 94.28956598370421, 97.32160282189409]\nReply Rate:  [95.00432687636976, 96.06361681557951, 94.28956598370421, 97.32160282189409]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.6\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.6\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Test_2_3_2_5_Step6a - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Test_2_3_2_5_Step6b - ModeC/S\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.29738337210946, 96.66891278078204, 97.0498501021141, 97.39146707126909]\nReply Rate:  [93.29738337210946, 96.66891278078204, 97.0498501021141, 97.39146707126909]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.9406641794649, 92.95817155649353, 97.13563101626544, 95.92762791811275]\nReply Rate:  [96.9406641794649, 92.95817155649353, 97.13563101626544, 95.92762791811275]\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P1: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.09678946602816, 94.71275926517744, 95.32248049757865, 93.84702901449121]\nReply Rate:  [97.09678946602816, 94.71275926517744, 95.32248049757865, 93.84702901449121]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.7\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.76829980977753, 96.0608087176538, 97.68244184465253, 96.93378647687203]\nReply Rate:  [93.76829980977753, 96.0608087176538, 97.68244184465253, 96.93378647687203]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.84040799765927, 95.3911582368453, 96.82691064796676, 97.07811990578018]\nReply Rate:  [93.84040799765927, 95.3911582368453, 96.82691064796676, 97.07811990578018]\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P3: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 1.5\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.15174482383777, 95.64505792524638, 95.58383131734087, 93.88046290127754]\nReply Rate:  [97.15174482383777, 95.64505792524638, 95.58383131734087, 93.88046290127754]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.58\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.81299175398136, 96.29931325056255, 94.2495668561241, 97.02757946439844]\nReply Rate:  [96.81299175398136, 96.29931325056255, 94.2495668561241, 97.02757946439844]\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.62\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.10370461705122, 95.8167871984677, 96.50690148571509, 94.48203906339549]\nReply Rate:  [95.10370461705122, 95.8167871984677, 96.50690148571509, 94.48203906339549]\nP4Width Cmd:  :ATC:XPDR:PUL:P4WIDTH 1.9\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->vary_P4: Test_2_3_2_5_Step6:ATC:XPDR:PUL:P4WIDTH 1.9\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.9\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.9\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.63287851934342, 93.37183289600252, 97.30559644211225, 94.42538894109292]\nReply Rate:  [96.63287851934342, 93.37183289600252, 97.30559644211225, 94.42538894109292]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P4WIDTH 1.6\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P4WIDTH 1.6\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Test_2_3_2_5_Step6b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Test_2_3_2_5_Step6 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "PASSED", "return_code": 0, "execution_time": 286.99307227134705, "start_time": "2025-06-05T23:33:10.419331", "end_time": "2025-06-05T23:37:57.412404", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step7 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a ModeA:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.90368783462351, 93.95761561511411, 95.17840740868556, 96.68507346741777]\nReply Rate:  [94.90368783462351, 93.95761561511411, 95.17840740868556, 96.68507346741777]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.55209897495578, 93.5520918877254, 97.79626502409478, 94.62777517481119]\nReply Rate:  [96.55209897495578, 93.5520918877254, 97.79626502409478, 94.62777517481119]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b ModeC/S:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.94506710950873, 93.10103456472878, 94.61152476517886, 93.79821852432083]\nReply Rate:  [93.94506710950873, 93.10103456472878, 94.61152476517886, 93.79821852432083]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.05057994520303, 95.5694464127321, 95.78364754711663, 95.41346963893422]\nReply Rate:  [96.05057994520303, 95.5694464127321, 95.78364754711663, 95.41346963893422]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a ModeA:ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.5604703743816, 96.05346164947039, 95.39279005674607, 96.5659562970506]\nReply Rate:  [95.5604703743816, 96.05346164947039, 95.39279005674607, 96.5659562970506]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [97.10433435522357, 96.62764223339245, 96.47193697534651, 97.38796169035804]\nReply Rate:  [97.10433435522357, 96.62764223339245, 96.47193697534651, 97.38796169035804]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -48.0\n:ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b ModeC/S:ATC:XPDR:POW -48.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.78528521364785, 94.89124616482447, 96.40906668917597, 94.47931594906649]\nReply Rate:  [93.78528521364785, 94.89124616482447, 96.40906668917597, 94.47931594906649]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.46680166336729, 96.08551888178279, 95.63615737319608, 96.95806917437231]\nReply Rate:  [93.46680166336729, 96.08551888178279, 95.63615737319608, 96.95806917437231]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEA ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeA: Mock Transponder Mode A Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a ModeA:ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.86353579406422, 93.90547358118415, 96.96955623227431, 93.67310198386681]\nReply Rate:  [93.86353579406422, 93.90547358118415, 96.96955623227431, 93.67310198386681]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.63274688133623, 92.87575174123539, 96.75548089193558, 93.5839796899769]\nReply Rate:  [96.63274688133623, 92.87575174123539, 96.75548089193558, 93.5839796899769]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7a - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODECS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeCS: Mock Transponder Mode C/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -33.0\n:ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b ModeC/S:ATC:XPDR:POW -33.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\nP1Width Cmd:  :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P1: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.90124029633884, 93.4170206969476, 97.99260830657877, 95.56487240261312]\nReply Rate:  [94.90124029633884, 93.4170206969476, 97.99260830657877, 95.56487240261312]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P1WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P1WIDTH 0.8\nP3Width Cmd:  :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->vary_P3: Test_2_3_2_5_Step7:ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.25\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.85731098141152, 94.83512721061288, 96.59467871902224, 94.22532264350369]\nReply Rate:  [95.85731098141152, 94.83512721061288, 96.59467871902224, 94.22532264350369]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P3WIDTH 0.8\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7b - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Test_2_3_2_5_Step7 - Done\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "PASSED", "return_code": 0, "execution_time": 148.499662399292, "start_time": "2025-06-05T23:37:57.413365", "end_time": "2025-06-05T23:40:25.913028", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step8 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODES ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeS: Mock Transponder Mode S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:UF 58000000FFFFFF\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -61.0\n:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -61.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.61429005316266, 96.32441105836452, 98.01668239168289, 93.64844598497274]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.53329030100663, 95.4879720296534, 94.9067424398459, 95.29807207408575]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -38.0\n:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -38.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [95.10437720496137, 95.28674581286145, 97.20032177984888, 94.21277315072915]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [96.11058963204971, 94.77121160857148, 94.38100902859196, 96.54988071615088]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -9.0\n:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 ModeS:ATC:XPDR:POW -9.0\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.52\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [93.33121955853146, 92.9896643123459, 96.83246535747821, 93.50647108409207]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->vary_P16: Test_2_3_2_5_Step8:ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:PUL:P16SPACING 3.48\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n[MOCK] TXD Python Lib: ATC5000NG.py->getPercentReply: Mock Reply Rate: [94.45925645196253, 93.27839400330618, 96.44423622643097, 94.24953142870218]\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF OFF\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Test_2_3_2_5_Step8 - Done: [98.01668239168289, 96.53329030100663, 97.20032177984888, 96.54988071615088, 96.83246535747821, 96.44423622643097]\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: Done, closing session\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:DME:STOP\n", "stderr": ""}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 17.630593299865723, "start_time": "2025-06-05T23:40:25.913546", "end_time": "2025-06-05T23:40:43.544141", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_8.py->Test_2_3_2_8: *** DO-181E, Undesired Replies: Sect 2.3.2.8 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODE ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderMode: Mock Transponder Mode Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:HEAD 0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:ALT 12000\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:OWN:MSADDR 4\n[MOCK] TXD Python Lib: ATC5000NG.py->init_own_aircraft_pos: Mock ATC Own Aircraft Set\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:MODEAS ON\n[MOCK] TXD Python Lib: ATC5000NG.py->transponderModeAS: Mock Transponder Mode A/S Set\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:ANT:POW 3\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:POW -100.0\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:POW -100.0\n[MOCK] TXD Python Lib: DO_181E_2_3_2_8.py->Test_2_3_2_8: Test_2_3_2_8 ModeA/S - Begin Timing Loop\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:XPDR:RF ON\n[MOCK] TXD Python Lib: ATC5000NG.py->waitforstatus: Mock STATUS: 20\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 124, in <module>\n    res = Test_2_3_2_8(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 69, in Test_2_3_2_8\n    atc.data_log_start()\n    ^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'data_log_start'\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 33, "failed": 2, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 3434.343784093857, "average_sequence_time": 98.12410811696734, "sequences_per_hour": 36.688231557821396, "optimization_effectiveness": {"optimization_success_rate": 94.28571428571428, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 2, "failure_by_procedure": {"DO181": 2}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 2 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}