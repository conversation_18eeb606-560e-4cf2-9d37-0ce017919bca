{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T22:02:38.691885", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 74, "passed": 14, "failed": 60, "errors": 0, "timeouts": 0, "success_rate": 18.91891891891892, "total_execution_time": 1976.2539639472961, "start_time": "2025-06-05T21:29:42.436931", "end_time": "2025-06-05T22:02:38.690895"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16202402114868164, "start_time": "2025-06-05T21:29:42.439120", "end_time": "2025-06-05T21:29:42.601144", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 407, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15445232391357422, "start_time": "2025-06-05T21:29:42.602125", "end_time": "2025-06-05T21:29:42.756577", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 383, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15306639671325684, "start_time": "2025-06-05T21:29:42.757303", "end_time": "2025-06-05T21:29:42.910369", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 366, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1519603729248047, "start_time": "2025-06-05T21:29:42.911415", "end_time": "2025-06-05T21:29:43.063375", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 383, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 2.6597635746002197, "start_time": "2025-06-05T21:29:43.063959", "end_time": "2025-06-05T21:29:45.723723", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 258, in <module>\n    res = Test_2_3_2_10_Step2a(rm,atc_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 88, in Test_2_3_2_10_Step2a\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 2.164341688156128, "start_time": "2025-06-05T21:29:45.724881", "end_time": "2025-06-05T21:29:47.889223", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2a_11-14-23.py->Test_2_3_2_10_Step2a: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2a ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 239, in <module>\n    res = Test_2_3_2_10_Step2a(rm,atc_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 87, in Test_2_3_2_10_Step2a\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1601321697235107, "start_time": "2025-06-05T21:29:47.890476", "end_time": "2025-06-05T21:29:50.050609", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step2b.py->Test_2_3_2_10_Step2b: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step2b ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 244, in <module>\n    res = Test_2_3_2_10_Step2b(rm,atc_obj,52.75,52.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 87, in Test_2_3_2_10_Step2b\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 2.163654327392578, "start_time": "2025-06-05T21:29:50.051769", "end_time": "2025-06-05T21:29:52.215423", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_10_Step3.py->Test_2_3_2_10_Step3: *** DO-181E, Diversity Operation: Sect 2.3.2.10_Step3 ***\n[MOCK] TXD Python Lib: ATC5000NG.py->gwrite: Cmd: :ATC:SET:SCO:CH1 24\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:SET:SCO:CH1 24\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 254, in <module>\n    res = Test_2_3_2_10_Step3(rm,atc_obj,12.75,12.69)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 83, in Test_2_3_2_10_Step3\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.45558619499206543, "start_time": "2025-06-05T21:29:52.216340", "end_time": "2025-06-05T21:29:52.671926", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 2.155153751373291, "start_time": "2025-06-05T21:29:52.672935", "end_time": "2025-06-05T21:29:54.828089", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step1.py->Test_2_3_2_1_Step1: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 1 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 138, in <module>\n    res = Test_2_3_2_1_Step1(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 52, in Test_2_3_2_1_Step1\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1629714965820312, "start_time": "2025-06-05T21:29:54.828966", "end_time": "2025-06-05T21:29:56.991938", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step2.py->Test_2_3_2_1_Step2: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 2 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 134, in <module>\n    res = Test_2_3_2_1_Step2(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 50, in Test_2_3_2_1_Step2\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1628196239471436, "start_time": "2025-06-05T21:29:56.992896", "end_time": "2025-06-05T21:29:59.155716", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step3.py->Test_2_3_2_1_Step3: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 3 ***\n\n\n['-85.0', '-75.0', '-65.0', '-55.0', '-45.0', '-33.0']\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 159, in <module>\n    res = Test_2_3_2_1_Step3(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 60, in Test_2_3_2_1_Step3\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.154776096343994, "start_time": "2025-06-05T21:29:59.156624", "end_time": "2025-06-05T21:30:01.311401", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step4.py->Test_2_3_2_1_Step4: DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 4 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 107, in <module>\n    res = Test_2_3_2_1_Step4(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 48, in Test_2_3_2_1_Step4\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1593830585479736, "start_time": "2025-06-05T21:30:01.312231", "end_time": "2025-06-05T21:30:03.471614", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step5.py->Test_2_3_2_1_Step5: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 5 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 119, in <module>\n    res = Test_2_3_2_1_Step5(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 48, in Test_2_3_2_1_Step5\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 2.166311025619507, "start_time": "2025-06-05T21:30:03.472473", "end_time": "2025-06-05T21:30:05.638784", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step6.py->Test_2_3_2_1_Step6: *** DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 6 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 115, in <module>\n    res = Test_2_3_2_1_Step6(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 53, in Test_2_3_2_1_Step6\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1651387214660645, "start_time": "2025-06-05T21:30:05.639965", "end_time": "2025-06-05T21:30:07.805104", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_1_step7.py->Test_2_3_2_1_Step7: ***DO-181E, Receiver Characteristics, Sect 2.3.2.1, Step 7 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 101, in <module>\n    res = Test_2_3_2_1_Step7(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 48, in Test_2_3_2_1_Step7\n    atc.transponderModeS()\n    ^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderModeS'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1637587547302246, "start_time": "2025-06-05T21:30:07.806506", "end_time": "2025-06-05T21:30:09.970265", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_1.py->Test_2_3_2_2_1: *** DO-181E, Reply Transmission Frequency,Sect 2.3.2.2.1 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 134, in <module>\n    res = Test_2_3_2_2_1(rm,atc_obj,-12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 48, in Test_2_3_2_2_1\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 2.665116786956787, "start_time": "2025-06-05T21:30:09.971683", "end_time": "2025-06-05T21:30:12.636801", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 309, in <module>\n    res = Test_2_3_2_2_2(rm,atc_obj,pwr_obj,-52.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 148, in Test_2_3_2_2_2\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 2.668361186981201, "start_time": "2025-06-05T21:30:12.638427", "end_time": "2025-06-05T21:30:15.306788", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_2_2_11-14-23.py->Test_2_3_2_2_2: *** DO-181E, RF Peak Output Power: Sect 2.3.2.2.2 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 289, in <module>\n    res = Test_2_3_2_2_2(rm,atc_obj,pwr_obj,-52.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 131, in Test_2_3_2_2_2\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 2.6709182262420654, "start_time": "2025-06-05T21:30:15.308213", "end_time": "2025-06-05T21:30:17.979131", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 293, in <module>\n    res = Test_2_3_2_3_1(rm,atc_obj,scope_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 144, in Test_2_3_2_3_1\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 2.6634762287139893, "start_time": "2025-06-05T21:30:17.980614", "end_time": "2025-06-05T21:30:20.644090", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_1_old.py->Test_2_3_2_3_1: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3.1 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 248, in <module>\n    res = Test_2_3_2_3_1(rm,atc_obj,scope_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 141, in Test_2_3_2_3_1\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 3.169248342514038, "start_time": "2025-06-05T21:30:20.644959", "end_time": "2025-06-05T21:30:23.814208", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 579, in <module>\n    res = Test_2_3_2_3_2a(rm,atc_obj,scope_obj,pwr_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 215, in Test_2_3_2_3_2a\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 3.162975549697876, "start_time": "2025-06-05T21:30:23.815627", "end_time": "2025-06-05T21:30:26.978604", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2a_old.py->Test_2_3_2_3_2a: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2a ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 321, in <module>\n    res = Test_2_3_2_3_2a(rm,atc_obj,scope_obj,pwr_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 193, in Test_2_3_2_3_2a\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 3.171623945236206, "start_time": "2025-06-05T21:30:26.979611", "end_time": "2025-06-05T21:30:30.151235", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 547, in <module>\n    res = Test_2_3_2_3_2b(rm,atc_obj,scope_obj,pwr_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 191, in Test_2_3_2_3_2b\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 3.1700687408447266, "start_time": "2025-06-05T21:30:30.152076", "end_time": "2025-06-05T21:30:33.322145", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO_181E_2_3_2_3_2b_old.py->Test_2_3_2_3_2b: *** DO-181E, Reply Pulse Characteristics: Sect 2.3.2.3_2b ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 304, in <module>\n    res = Test_2_3_2_3_2b(rm,atc_obj,scope_obj,pwr_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 177, in Test_2_3_2_3_2b\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.163637399673462, "start_time": "2025-06-05T21:30:33.322992", "end_time": "2025-06-05T21:30:35.486630", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_4.py->Test_2_3_2_4: *** DO-181E, Side Lobe Supression: Sect 2.3.2.4 ***\n\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 194, in <module>\n    res = Test_2_3_2_4(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 78, in Test_2_3_2_4\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1680948734283447, "start_time": "2025-06-05T21:30:35.487400", "end_time": "2025-06-05T21:30:37.655495", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step1.py->Test_2_3_2_5_Step1: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step1 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 121, in <module>\n    res = Test_2_3_2_5_Step1(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 54, in Test_2_3_2_5_Step1\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1632957458496094, "start_time": "2025-06-05T21:30:37.656600", "end_time": "2025-06-05T21:30:39.819896", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step2.py->Test_2_3_2_5_Step2: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step2 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 121, in <module>\n    res = Test_2_3_2_5_Step2(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 51, in Test_2_3_2_5_Step2\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1620514392852783, "start_time": "2025-06-05T21:30:39.820901", "end_time": "2025-06-05T21:30:41.982953", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step3.py->Test_2_3_2_5_Step3: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step3 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 198, in <module>\n    res = Test_2_3_2_5_Step3(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 54, in Test_2_3_2_5_Step3\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1699862480163574, "start_time": "2025-06-05T21:30:41.983676", "end_time": "2025-06-05T21:30:44.153663", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step4.py->Test_2_3_2_5_Step4: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step4 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 129, in <module>\n    res = Test_2_3_2_5_Step4(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 49, in Test_2_3_2_5_Step4\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 2.164775848388672, "start_time": "2025-06-05T21:30:44.154664", "end_time": "2025-06-05T21:30:46.319440", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step5.py->Test_2_3_2_5_Step5: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step5 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 199, in <module>\n    res = Test_2_3_2_5_Step5(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 100, in Test_2_3_2_5_Step5\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 2.165034055709839, "start_time": "2025-06-05T21:30:46.320187", "end_time": "2025-06-05T21:30:48.485222", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step6.py->Test_2_3_2_5_Step6: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step6 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 216, in <module>\n    res = Test_2_3_2_5_Step6(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 124, in Test_2_3_2_5_Step6\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 2.163649559020996, "start_time": "2025-06-05T21:30:48.486017", "end_time": "2025-06-05T21:30:50.649667", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step7.py->Test_2_3_2_5_Step7: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step7 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 189, in <module>\n    res = Test_2_3_2_5_Step7(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 107, in Test_2_3_2_5_Step7\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1632676124572754, "start_time": "2025-06-05T21:30:50.650420", "end_time": "2025-06-05T21:30:52.813687", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_5_Step8.py->Test_2_3_2_5_Step8: *** DO-181E, Pulse Decoder Characterics: Sect 2.3.2.5_Step8 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 142, in <module>\n    res = Test_2_3_2_5_Step8(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 82, in Test_2_3_2_5_Step8\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 2.158984899520874, "start_time": "2025-06-05T21:30:52.814479", "end_time": "2025-06-05T21:30:54.973464", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_181E_2_3_2_8.py->Test_2_3_2_8: *** DO-181E, Undesired Replies: Sect 2.3.2.8 ***\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 124, in <module>\n    res = Test_2_3_2_8(rm,atc_obj,12.0)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 49, in Test_2_3_2_8\n    atc.transponderMode()\n    ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'transponderMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1583538055419922, "start_time": "2025-06-05T21:30:54.974385", "end_time": "2025-06-05T21:30:55.132739", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 104, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.154083251953125, "start_time": "2025-06-05T21:30:55.133482", "end_time": "2025-06-05T21:30:55.287565", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 55, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 7.155519247055054, "start_time": "2025-06-05T21:30:55.288213", "end_time": "2025-06-05T21:31:02.443733", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_189_2_2_1_b.py->init_DME_Standard: *Test_189_2_2_1 - initializing DME\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 127, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 103, in main\n    init_DME_Standard(cable_loss, rm, atc, ARINC)\n    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 56, in init_DME_Standard\n    atc.DMEMode()\n    ^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'DMEMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 2.6628854274749756, "start_time": "2025-06-05T21:31:02.444576", "end_time": "2025-06-05T21:31:05.107461", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 449, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 415, in main\n    atc.DMEMode()\n    ^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'DMEMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 2.1614043712615967, "start_time": "2025-06-05T21:31:05.108216", "end_time": "2025-06-05T21:31:07.269620", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 270, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 186, in main\n    atc.DMEMode()\n    ^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'DMEMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 7.163553714752197, "start_time": "2025-06-05T21:31:07.270490", "end_time": "2025-06-05T21:31:14.434044", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 198, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 168, in main\n    atc.DMEMode()\n    ^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'DMEMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 17.67292881011963, "start_time": "2025-06-05T21:31:14.434864", "end_time": "2025-06-05T21:31:32.107793", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: ATC5000NG.py->__init__: Mock ATC5000 Connection Success\n[MOCK] TXD Python Lib: ATC5000NG.py->write: [MOCK ATC WRITE] :ATC:RESET\n[MOCK] TXD Python Lib: ATC5000NG.py->Reset: Mock Reset Complete\n\n[MOCK] TXD Python Lib: DO_189_2_2_7.py->Test_DO189_2_2_7: *** DO-189, Pulse Power Characteristics: Sect 2.2.7 ***\n[MOCK] TXD Python Lib: DO_189_2_2_7.py->init_DME_Standard: *Test_189_2_2_7 - Initializing ATC into DME mode \n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 366, in <module>\n    Test_DO189_2_2_7(rm,ARINC,pwr,atc,PathLoss)\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 262, in Test_DO189_2_2_7\n    init_DME_Standard(rm, cableLoss, atc, ARINC)\n    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 71, in init_DME_Standard\n    atc.DMEMode()\n    ^^^^^^^^^^^\nAttributeError: 'ATC5000NG' object has no attribute 'DMEMode'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1582012176513672, "start_time": "2025-06-05T21:31:32.108632", "end_time": "2025-06-05T21:31:32.266833", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 41, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15713787078857422, "start_time": "2025-06-05T21:31:32.267700", "end_time": "2025-06-05T21:31:32.424838", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 29, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16019701957702637, "start_time": "2025-06-05T21:31:32.425860", "end_time": "2025-06-05T21:31:32.586057", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15845251083374023, "start_time": "2025-06-05T21:31:32.586722", "end_time": "2025-06-05T21:31:32.745175", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15656614303588867, "start_time": "2025-06-05T21:31:32.745808", "end_time": "2025-06-05T21:31:32.902375", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 44, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15572261810302734, "start_time": "2025-06-05T21:31:32.902981", "end_time": "2025-06-05T21:31:33.058704", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 41, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16703152656555176, "start_time": "2025-06-05T21:31:33.059321", "end_time": "2025-06-05T21:31:33.226353", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 36, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.11179637908935547, "start_time": "2025-06-05T21:31:33.227000", "end_time": "2025-06-05T21:31:33.338797", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.15066051483154297, "start_time": "2025-06-05T21:31:33.339225", "end_time": "2025-06-05T21:31:33.489885", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_LOGGING.py", "status": "PASSED", "return_code": 0, "execution_time": 0.136793851852417, "start_time": "2025-06-05T21:31:33.490271", "end_time": "2025-06-05T21:31:33.627065", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.11680412292480469, "start_time": "2025-06-05T21:31:33.627648", "end_time": "2025-06-05T21:31:33.744453", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1348130702972412, "start_time": "2025-06-05T21:31:33.745264", "end_time": "2025-06-05T21:31:33.880077", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "PASSED", "return_code": 0, "execution_time": 100.17765426635742, "start_time": "2025-06-05T21:31:33.880514", "end_time": "2025-06-05T21:33:14.058170", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->__init__: Mock Spectrum Analyzer Connection Success\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Transmit Frequency\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: *Test_2.2.3..5: SetUp SpecAnz, Capture Data\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > *RST\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz Resetting...\n[MOCK] TXD Python Lib: N9010BSpecAn.py->Reset: Mock SpecAnz complete\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:CENTer 1030 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:RESolution 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :BANDwidth:VIDeo 1 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > SWE:TIME 1 s\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :FREQuency:SPAN 5 MHz\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :DISPlay:WINDow:TRACe:Y:RLEVel -10 dBm\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :TRAC:TYPE MAXHold\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :INITiate:CONTinuous ON\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:STATE ON\n\n[MOCK] TXD Python Lib: N9010BSpecAn.py->basicWrite: [MOCK SPEC] > :CALCULATE:MARKER1:MAX\n\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Frq: 1030007883.0615541 Lmt: 1.0\n[MOCK] TXD Python Lib: DO385_2_2_3_5.py->Test_2_2_3_5: Test_2.2.3..5: Transmit Frequency, DONE.\n[MOCK] TXD Python Lib: ate_rm.py->cleanup: Mock Resource Manager Closed.\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "PASSED", "return_code": 0, "execution_time": 34.73999738693237, "start_time": "2025-06-05T21:33:14.059139", "end_time": "2025-06-05T21:33:48.799137", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: D3054Scope.py->__init__: Mock D3054Scope initialized successfully\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *** DO-385, ModeS Transmit Pulse Characteristics: Sect 2.2.3.8***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2.2.3.8: Start Scenario\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: *Test_2_2_3_8 - Start Pulse Measurements\n[MOCK] TXD Python Lib: D3054Scope.py->digiEdgePos: Mock digiEdgePos: 5 pos edges, 5 neg edges\nPEdges:  5 [0.5718354363520142, 0.4209937288810781, 0.7701251101700299, 0.9034645542834894, 0.37715765759945163]\nNEdges:  5 [0.7015814882620008, 0.9434176895313274, 0.10779094866797856, 0.20169397870383476, 0.3295547296877923]\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse duration: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse rise: True\n[MOCK] TXD Python Lib: DO385_2_2_3_8.py->Test_2_2_3_8: Pulse fall: True\n*** MESSAGE SPAN *** -242280.70666422186\n*** PreAmble Span *** -194677.77875256253\n*** Data Span *** -47602.927911659324\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "PASSED", "return_code": 0, "execution_time": 552.6546928882599, "start_time": "2025-06-05T21:33:48.800017", "end_time": "2025-06-05T21:43:01.454712", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD qual_test_a350.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario qual_test_a350.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -64\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [93, 97, 88, 42, 18, 69]\nRange:  [1.4759207242676542, 18.823818465604298, 41.21135856377512, 46.84713250634503, 7.49219912381009, 8.404760287950651]\nAltitude:  [26196, 620, 7014, 509, 26330, 2567]\nBearing:  [44.74636281234147, 355.2098611408619, 75.13472402775707, 220.49729472512797, 185.4933834305715, 281.66939034439076]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [93, 97, 88, 42, 18, 69]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [1.4759207242676542, 18.823818465604298, 41.21135856377512, 46.84713250634503, 7.49219912381009, 8.404760287950651]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [26196, 620, 7014, 509, 26330, 2567]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [44.74636281234147, 355.2098611408619, 75.13472402775707, 220.49729472512797, 185.4933834305715, 281.66939034439076]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [1, 27, 20, 85, 79, 18, 16]\nRange:  [3.426797141773563, 4.615038627846424, 0.3257916757918311, 36.00903974183184, 25.70741551063362, 37.69776157172976, 38.096350491675786]\nAltitude:  [35871, 28650, 1075, 20385, 23169, 25399, 24095]\nBearing:  [10.014724942613995, 68.21874298370055, 104.89020416020692, 74.37357259348876, 8.684621593605005, 244.51269178212834, 146.7985434700231]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [1, 27, 20, 85, 79, 18, 16]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [3.426797141773563, 4.615038627846424, 0.3257916757918311, 36.00903974183184, 25.70741551063362, 37.69776157172976, 38.096350491675786]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [35871, 28650, 1075, 20385, 23169, 25399, 24095]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [10.014724942613995, 68.21874298370055, 104.89020416020692, 74.37357259348876, 8.684621593605005, 244.51269178212834, 146.7985434700231]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [92, 4, 84, 97, 87]\nRange:  [43.860599893580655, 29.66353426058555, 0.3116156233850309, 42.89948861982023, 36.68688266790778]\nAltitude:  [267, 17001, 38786, 12748, 34026]\nBearing:  [336.06662887185513, 355.8815291485717, 242.22976871340606, 76.67769206737165, 107.39698166795434]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [92, 4, 84, 97, 87]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [43.860599893580655, 29.66353426058555, 0.3116156233850309, 42.89948861982023, 36.68688266790778]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [267, 17001, 38786, 12748, 34026]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [336.06662887185513, 355.8815291485717, 242.22976871340606, 76.67769206737165, 107.39698166795434]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [97, 79, 81, 31, 53, 66]\nRange:  [37.00042100204186, 22.39209261349428, 19.92221126768701, 3.439138526394403, 8.525075284808759, 16.23211977481367]\nAltitude:  [8826, 37599, 37385, 28408, 24942, 6865]\nBearing:  [190.58772017108723, 309.19018396089325, 236.63918447130206, 31.48462484646831, 224.92154467881014, 359.5138508533062]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [97, 79, 81, 31, 53, 66]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [37.00042100204186, 22.39209261349428, 19.92221126768701, 3.439138526394403, 8.525075284808759, 16.23211977481367]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [8826, 37599, 37385, 28408, 24942, 6865]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [190.58772017108723, 309.19018396089325, 236.63918447130206, 31.48462484646831, 224.92154467881014, 359.5138508533062]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [88, 89, 8, 31, 83, 25, 9, 5]\nRange:  [8.992913801784836, 6.838432669084832, 38.231658111141286, 24.148906951293796, 48.879342966678834, 3.234493981202613, 19.195518496868292, 43.13624216189917]\nAltitude:  [39108, 7040, 17555, 9423, 3626, 20030, 11477, 16958]\nBearing:  [32.65582561799251, 330.87251567171666, 346.9306875389583, 292.1824408643639, 355.9227634693851, 324.7787793636069, 288.58531022820034, 154.31233301758965]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [88, 89, 8, 31, 83, 25, 9, 5]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [8.992913801784836, 6.838432669084832, 38.231658111141286, 24.148906951293796, 48.879342966678834, 3.234493981202613, 19.195518496868292, 43.13624216189917]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [39108, 7040, 17555, 9423, 3626, 20030, 11477, 16958]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [32.65582561799251, 330.87251567171666, 346.9306875389583, 292.1824408643639, 355.9227634693851, 324.7787793636069, 288.58531022820034, 154.31233301758965]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [48, 91, 98, 91, 40]\nRange:  [49.56178118617534, 41.92974583203867, 46.34571132407795, 33.16052243464797, 11.248423067365199]\nAltitude:  [25400, 27265, 16463, 16134, 10831]\nBearing:  [192.82914977932538, 291.79146588240906, 161.64083119524997, 355.9822069764729, 259.08779702468627]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [48, 91, 98, 91, 40]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [49.56178118617534, 41.92974583203867, 46.34571132407795, 33.16052243464797, 11.248423067365199]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [25400, 27265, 16463, 16134, 10831]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [192.82914977932538, 291.79146588240906, 161.64083119524997, 355.9822069764729, 259.08779702468627]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -40\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [22, 77, 48, 42, 15]\nRange:  [10.252394390376807, 2.7912875466096496, 23.416125702059844, 0.09363872423810427, 32.820505622566046]\nAltitude:  [29239, 34501, 39300, 5768, 37470]\nBearing:  [206.812518686004, 11.589309557636787, 305.67560032451667, 255.19962712849525, 94.97058089617643]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [22, 77, 48, 42, 15]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [10.252394390376807, 2.7912875466096496, 23.416125702059844, 0.09363872423810427, 32.820505622566046]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [29239, 34501, 39300, 5768, 37470]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [206.812518686004, 11.589309557636787, 305.67560032451667, 255.19962712849525, 94.97058089617643]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [73, 31, 84, 1, 91, 63]\nRange:  [49.1056959407992, 6.813059366884033, 37.81007593939554, 28.76392892844613, 26.72436315112129, 45.29234497875901]\nAltitude:  [37150, 26402, 24844, 25617, 17777, 16740]\nBearing:  [67.87837044249956, 193.4084328000118, 199.7477161511125, 75.33253621930282, 122.40010933261695, 180.97429435866852]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [73, 31, 84, 1, 91, 63]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [49.1056959407992, 6.813059366884033, 37.81007593939554, 28.76392892844613, 26.72436315112129, 45.29234497875901]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [37150, 26402, 24844, 25617, 17777, 16740]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [67.87837044249956, 193.4084328000118, 199.7477161511125, 75.33253621930282, 122.40010933261695, 180.97429435866852]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [81, 78, 77, 61, 51, 78]\nRange:  [38.122733092262465, 10.991804030615715, 31.187420207365125, 25.862685069123753, 49.330931476083165, 37.899007241066194]\nAltitude:  [7972, 10232, 21181, 8211, 37319, 27741]\nBearing:  [63.40494950659482, 327.53529959365017, 80.02111257290709, 135.4595575143913, 90.82743936414514, 93.30204908812358]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [81, 78, 77, 61, 51, 78]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [38.122733092262465, 10.991804030615715, 31.187420207365125, 25.862685069123753, 49.330931476083165, 37.899007241066194]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [7972, 10232, 21181, 8211, 37319, 27741]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [63.40494950659482, 327.53529959365017, 80.02111257290709, 135.4595575143913, 90.82743936414514, 93.30204908812358]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [21, 28, 18, 84, 7, 41, 70]\nRange:  [1.6640631165248332, 45.883889532176084, 40.16714742955211, 0.9272803625027082, 46.479827578757, 16.60326696190248, 37.935517627165666]\nAltitude:  [42, 32996, 30711, 4380, 36708, 12388, 15332]\nBearing:  [113.02133046043284, 50.316065182832745, 171.05874471332143, 268.1059367351517, 119.20068998394659, 251.09693490252624, 294.44402661178975]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [21, 28, 18, 84, 7, 41, 70]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [1.6640631165248332, 45.883889532176084, 40.16714742955211, 0.9272803625027082, 46.479827578757, 16.60326696190248, 37.935517627165666]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [42, 32996, 30711, 4380, 36708, 12388, 15332]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [113.02133046043284, 50.316065182832745, 171.05874471332143, 268.1059367351517, 119.20068998394659, 251.09693490252624, 294.44402661178975]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [23, 60, 31, 83, 42, 26, 38]\nRange:  [11.383912522261664, 40.10136699127013, 5.836282928102538, 3.384527241962193, 10.814118470255924, 11.091240881766579, 38.40555786793429]\nAltitude:  [796, 24635, 35462, 11111, 8917, 31094, 24152]\nBearing:  [268.19711976499264, 214.38723385940034, 75.33174210528573, 84.81040570369726, 192.33130194219362, 287.919930467076, 295.13096225929564]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [23, 60, 31, 83, 42, 26, 38]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [11.383912522261664, 40.10136699127013, 5.836282928102538, 3.384527241962193, 10.814118470255924, 11.091240881766579, 38.40555786793429]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [796, 24635, 35462, 11111, 8917, 31094, 24152]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [268.19711976499264, 214.38723385940034, 75.33174210528573, 84.81040570369726, 192.33130194219362, 287.919930467076, 295.13096225929564]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [92, 91, 16, 36, 63, 94, 7, 45]\nRange:  [42.636674374691594, 45.32001492446058, 22.664898926196486, 45.0434147933205, 13.905198692720205, 6.910117371037039, 44.932748279558744, 40.37196958574405]\nAltitude:  [15914, 7476, 6942, 4680, 18216, 35635, 20751, 36645]\nBearing:  [177.99550058193933, 302.6465199333436, 68.38569707634409, 238.72526236481022, 202.4857673793193, 0.19464910523847134, 272.90827648441945, 112.79425714180593]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [92, 91, 16, 36, 63, 94, 7, 45]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [42.636674374691594, 45.32001492446058, 22.664898926196486, 45.0434147933205, 13.905198692720205, 6.910117371037039, 44.932748279558744, 40.37196958574405]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [15914, 7476, 6942, 4680, 18216, 35635, 20751, 36645]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [177.99550058193933, 302.6465199333436, 68.38569707634409, 238.72526236481022, 202.4857673793193, 0.19464910523847134, 272.90827648441945, 112.79425714180593]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1090MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1090\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:1:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:2:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:3:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:4:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:5:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:6:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STATIC:7:RPLYPWR -21\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [98, 40, 91, 74, 49]\nRange:  [13.858024472326008, 31.051975046855134, 45.428415605242506, 30.89338817952093, 47.05968808717684]\nAltitude:  [6143, 12569, 35884, 17335, 19283]\nBearing:  [321.6219080837821, 67.15381422029532, 93.00860610363229, 107.00243241159713, 26.970462975268788]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [98, 40, 91, 74, 49]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [13.858024472326008, 31.051975046855134, 45.428415605242506, 30.89338817952093, 47.05968808717684]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [6143, 12569, 35884, 17335, 19283]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [321.6219080837821, 67.15381422029532, 93.00860610363229, 107.00243241159713, 26.970462975268788]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [35, 76, 74, 60, 73, 86]\nRange:  [44.273389080836594, 8.721825916814508, 31.468388393927448, 35.88701551163958, 6.389896548056928, 2.248532820355803]\nAltitude:  [1193, 31370, 19565, 35286, 29851, 33075]\nBearing:  [81.09802514493278, 178.67125081451607, 221.0754794284063, 270.09113653958724, 228.91832229996876, 249.069884118024]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [35, 76, 74, 60, 73, 86]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [44.273389080836594, 8.721825916814508, 31.468388393927448, 35.88701551163958, 6.389896548056928, 2.248532820355803]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [1193, 31370, 19565, 35286, 29851, 33075]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [81.09802514493278, 178.67125081451607, 221.0754794284063, 270.09113653958724, 228.91832229996876, 249.069884118024]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1087MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1087\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [65, 74, 2, 48, 74]\nRange:  [33.443645544203925, 39.3610177390228, 3.959729469215417, 46.5714550418264, 2.69450251772399]\nAltitude:  [1767, 16164, 28500, 14486, 26160]\nBearing:  [148.75170668765787, 72.91636300363642, 180.8617905392819, 129.257160597776, 139.05633012743743]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [65, 74, 2, 48, 74]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [33.443645544203925, 39.3610177390228, 3.959729469215417, 46.5714550418264, 2.69450251772399]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [1767, 16164, 28500, 14486, 26160]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [148.75170668765787, 72.91636300363642, 180.8617905392819, 129.257160597776, 139.05633012743743]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [39, 54, 26, 68, 94, 70, 17, 12]\nRange:  [39.99313494022185, 28.45989090585058, 27.383126324887957, 33.55726179846169, 34.49768635423877, 44.25410683087754, 17.514079230519176, 41.682857549873034]\nAltitude:  [26036, 37942, 10618, 13212, 29050, 19099, 25753, 31332]\nBearing:  [20.549354325539, 313.5612326470674, 314.0468064440559, 95.14638187384092, 203.14305428924814, 33.2336271830609, 24.315221792705582, 129.48031563497204]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [39, 54, 26, 68, 94, 70, 17, 12]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [39.99313494022185, 28.45989090585058, 27.383126324887957, 33.55726179846169, 34.49768635423877, 44.25410683087754, 17.514079230519176, 41.682857549873034]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [26036, 37942, 10618, 13212, 29050, 19099, 25753, 31332]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [20.549354325539, 313.5612326470674, 314.0468064440559, 95.14638187384092, 203.14305428924814, 33.2336271830609, 24.315221792705582, 129.48031563497204]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->InBandAcceptance: Setting Frequency to 1093MHz\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENA:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENB:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GEND:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENE:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SET:GENF:FREQ 1093\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Starting Test for Intruders\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [46, 13, 28, 18, 76]\nRange:  [27.218844180584924, 7.644813728691574, 1.1888993558721195, 15.668168401711663, 7.392636873071839]\nAltitude:  [20983, 16159, 2217, 39674, 13933]\nBearing:  [328.3316695066871, 53.202945903070365, 190.70395671533925, 166.75595488852406, 8.895405552420407]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Starting Data: [46, 13, 28, 18, 76]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Starting Data: [27.218844180584924, 7.644813728691574, 1.1888993558721195, 15.668168401711663, 7.392636873071839]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Starting Data: [20983, 16159, 2217, 39674, 13933]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Starting Data: [328.3316695066871, 53.202945903070365, 190.70395671533925, 166.75595488852406, 8.895405552420407]\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [87, 28, 15, 70, 88, 57, 20]\nRange:  [26.320821500146113, 30.240918517611842, 13.517369068630892, 37.010709533119154, 10.09316761964501, 38.07039153669423, 24.371145459721816]\nAltitude:  [22968, 33663, 21625, 24324, 35632, 24266, 23808]\nBearing:  [120.32507978550811, 338.45346046345946, 163.44501471332103, 322.2088864603451, 196.66219628392813, 204.65847618216753, 106.71277959657036]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Intruders Ending Data: [87, 28, 15, 70, 88, 57, 20]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Range Ending Data: [26.320821500146113, 30.240918517611842, 13.517369068630892, 37.010709533119154, 10.09316761964501, 38.07039153669423, 24.371145459721816]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Altitude Ending Data: [22968, 33663, 21625, 24324, 35632, 24266, 23808]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Bearing Ending Data: [120.32507978550811, 338.45346046345946, 163.44501471332103, 322.2088864603451, 196.66219628392813, 204.65847618216753, 106.71277959657036]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_1_1.py->testIntruders: Finished Test for Intruders\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nRESULTS:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "PASSED", "return_code": 0, "execution_time": 310.5737569332123, "start_time": "2025-06-05T21:43:01.455454", "end_time": "2025-06-05T21:48:12.029212", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode C Reply Reception, Sect 2.2.4.4.2.1 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: *Test_2.2.4.4.2.1 Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 9600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario A\nStep1:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [94, 73, 45, 86, 83, 28, 5]\nRange:  [9.562802370050989, 33.55063469046574, 0.6047917768926103, 20.126115505603774, 0.06544871589130308, 26.827253526590965, 40.999694486498235]\nAltitude:  [18831, 39219, 16103, 4831, 26464, 25768, 10465]\nBearing:  [25.914223465770313, 128.65527422099822, 132.01763277911047, 65.7611180440096, 7.662538620688606, 97.87003793574186, 268.3681052459031]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario B\nStep1:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [89, 36, 39, 3, 26, 47, 54]\nRange:  [23.01504586830739, 14.559141722332114, 8.263810847055058, 36.2401508962353, 28.59702333203839, 40.60131013620733, 30.020188076834454]\nAltitude:  [29601, 1301, 2298, 29119, 15782, 18796, 8624]\nBearing:  [136.2760095928046, 134.9305845139969, 114.46286856112785, 257.9635840141856, 267.76015052219395, 174.91368870711327, 351.7764099296955]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario C\nStep1:Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [64, 57, 3, 44, 17, 22, 53]\nRange:  [41.876551475523314, 28.69001805818929, 20.38758823578602, 12.679534703768496, 16.19864628068422, 34.49070028878063, 8.537662534976409]\nAltitude:  [12542, 14437, 27799, 37820, 25938, 27735, 5514]\nBearing:  [129.62727128465247, 116.04782735926719, 345.61277416863464, 73.6490529188985, 264.70453434792194, 338.61918738404484, 354.25512531384584]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario D\nStep1:Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [98, 75, 14, 63, 99]\nRange:  [26.589011586734724, 8.064148824911, 28.62538269631212, 49.15903912505194, 40.5279150778467]\nAltitude:  [28312, 18338, 14373, 32814, 30407]\nBearing:  [244.8197464811856, 152.9553807756883, 138.82003167401865, 203.247527747454, 319.5816032482158]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario E\nStep1:Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [97, 33, 82, 58, 49, 72, 20]\nRange:  [27.14328208250072, 34.067033291228846, 32.28334757126367, 14.856557085203848, 11.344905190151755, 34.533783061532816, 14.638149619538154]\nAltitude:  [179, 14709, 33915, 10133, 3564, 39964, 5393]\nBearing:  [301.28218857294837, 106.05563134489526, 81.79315535133007, 169.24174062234943, 89.05779222366613, 63.63330447315398, 286.59699417075177]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1:Test_385_Mode C Reply Reception - Scenario F\nStep1:Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_RPLY_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_RPLY_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [63, 95, 73, 60, 71, 20]\nRange:  [31.254070813070722, 48.916329222169495, 11.542411436653161, 49.32353930047847, 37.62962147708709, 10.73046983447139]\nAltitude:  [12595, 4589, 17374, 19283, 38323, 702]\nBearing:  [130.13343908773598, 95.04188011429997, 8.0117873544522, 49.729917267863954, 155.55493702197467, 158.70483868622173]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 33400\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario A\nStep2:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [62, 22, 73, 77, 6, 16, 1]\nRange:  [21.08295745908618, 31.203239140698162, 37.30984888710832, 45.09973982962773, 7.072976941694609, 45.445187087229534, 6.786989794338111]\nAltitude:  [28727, 3604, 14769, 19075, 18731, 35796, 23180]\nBearing:  [148.10246659879195, 257.0517165438617, 122.01572502723184, 25.266621217922037, 256.7326253445392, 242.3589243584371, 331.8060548692741]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2:Test_385_Mode C Reply Reception - Scenario B\nStep2:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Pulse_Det_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Pulse_Det_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [94, 79, 71, 54, 36, 76]\nRange:  [7.074854790995827, 30.573968896615916, 15.919227825191996, 28.34063148845733, 45.80941138927069, 48.98624095683236]\nAltitude:  [34772, 405, 20425, 1522, 33460, 23687]\nBearing:  [233.7224887721248, 198.45222441327985, 15.797708784772526, 341.18940873888056, 67.01832608668873, 286.8332126981389]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8600\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario A\nStep3:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [6, 14, 6, 28, 58]\nRange:  [10.580779486461628, 27.735662822027894, 9.77692945140592, 46.32576221975038, 32.499790774204094]\nAltitude:  [25667, 38531, 35592, 33860, 22289]\nBearing:  [123.58205952995546, 299.75986732526655, 279.55283912957424, 197.28388140919725, 266.2647112458516]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3:Test_385_Mode C Reply Reception - Scenario B\nStep3:Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Narw_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Narw_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [63, 36, 8, 95, 3, 71, 7]\nRange:  [48.952279811317545, 39.805627278853024, 11.324126011816016, 49.51634677049099, 17.357439189348035, 38.22780201546468, 46.51963064850621]\nAltitude:  [22506, 25991, 25723, 6371, 29834, 33342, 7731]\nBearing:  [341.7766388905211, 133.70993414868224, 124.61225263838934, 283.07084431014147, 171.8409779610856, 92.14332715888567, 239.94643184404111]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 6000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4:Test_385_Mode C Reply Reception - Scenario A\nStep4:Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD ModeC_Garb.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario ModeC_Garb.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [4, 20, 42, 59, 26, 13, 73]\nRange:  [40.344544780796404, 23.517939816997746, 42.902420553817755, 0.600004926809572, 3.9851131816551035, 12.96664365942966, 30.138410720638802]\nAltitude:  [31787, 17048, 12643, 1125, 19787, 33373, 29792]\nBearing:  [87.45705553101216, 1.4468211877489612, 250.99086771946816, 53.2625597017428, 6.649956553856358, 205.9652975471213, 170.94507842840915]\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step1_Results: [0, 0, 0, 0, 0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step2_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step3_Results: [0, 0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Step4_Results: [0]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_1.py->Test_2_4_2_1_4_1: Done,Closing Session\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.3364703655243, "start_time": "2025-06-05T21:48:12.030212", "end_time": "2025-06-05T21:52:44.366686", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n*** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n\n\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [12, 91, 16, 68, 17, 34, 61]\nRange:  [32.832842282687466, 36.97332681746366, 9.207454666189074, 9.475228131976587, 36.995256869346335, 1.9504730640291401, 44.04178118793054]\nAltitude:  [37322, 32966, 14271, 12195, 3389, 5589, 2357]\nBearing:  [87.74369156364448, 293.00984642527624, 184.99813381257113, 226.99031103077144, 94.95732657901885, 55.51971970266463, 194.97669735283955]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [70, 51, 32, 5, 73, 100, 33, 54]\nRange:  [18.80196918755443, 18.03934327675552, 36.930245743006886, 12.06968280981458, 14.483134028465965, 45.769683852949164, 10.053078606691518, 48.07459368513696]\nAltitude:  [5710, 39978, 18368, 5912, 26959, 8209, 36885, 22516]\nBearing:  [162.4899924336723, 203.3971898631693, 40.60933081624121, 149.83428025492415, 229.42921246087622, 84.84770614626855, 173.56105187006366, 297.51606993681537]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [3, 15, 59, 70, 77, 65, 59]\nRange:  [43.34564131243956, 18.89639454287873, 4.287905947945875, 48.803588276345224, 19.832476511536772, 1.1813982303949366, 1.6548445672168177]\nAltitude:  [11470, 26320, 29680, 36106, 308, 31813, 5974]\nBearing:  [4.369522040095597, 77.2609340143959, 66.19332628979126, 89.55276040330378, 170.7347821444008, 158.00482121228762, 22.60288018510459]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [95, 93, 7, 95, 7, 63, 71]\nRange:  [24.551007010950414, 4.859451596828457, 18.153490255086975, 15.53074200962632, 15.495466762443533, 17.72341105026687, 33.23315655431806]\nAltitude:  [7124, 3394, 39737, 30684, 14601, 26695, 39173]\nBearing:  [259.44106670782867, 123.16676623671066, 161.49450843559276, 191.47565469767332, 115.60242861448592, 311.83322494199217, 215.37101538195637]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [5, 10, 40, 14, 52, 93, 49]\nRange:  [5.649797284541008, 39.39843309744007, 46.69630225450865, 0.5850685202704875, 12.753464688962628, 18.698936660584568, 9.720052905489684]\nAltitude:  [12063, 28534, 9084, 16648, 30086, 13275, 24493]\nBearing:  [68.57494949132517, 162.61826827196268, 29.839234036897338, 132.83793413684606, 188.46809090488094, 34.46019907938187, 283.1108833320645]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [29, 51, 5, 80, 74, 61]\nRange:  [43.37611630141991, 12.255059527513545, 1.480410065452531, 32.09203269399757, 31.04763634103596, 31.607079845089498]\nAltitude:  [17627, 33930, 15883, 15483, 38046, 14356]\nBearing:  [0.6167539854148218, 314.3749519367658, 67.10957626273789, 241.6372950543215, 23.22188459346988, 50.08213130862365]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [25, 23, 41, 9, 47, 6, 79]\nRange:  [16.847253020381693, 34.671620832479995, 38.26747644747529, 43.9006841010222, 40.8680428113149, 16.63188513881451, 30.498399049069725]\nAltitude:  [5170, 9524, 35207, 27211, 24819, 10808, 1821]\nBearing:  [309.2725256290967, 202.54388584513302, 70.9579546834853, 322.2756430580151, 282.9146794649293, 66.08682521057804, 67.23038803591626]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [76, 47, 54, 87, 68, 29]\nRange:  [12.20204896968431, 48.17564658424682, 25.835405560310516, 6.95214476590067, 19.788795571726993, 42.75099220478633]\nAltitude:  [3936, 28355, 1643, 36447, 9446, 38333]\nBearing:  [311.0637220236195, 29.294482505034992, 47.57777247408244, 339.1233830123776, 358.01898864159074, 350.96905675749775]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [10, 82, 50, 46, 67, 15, 14]\nRange:  [10.082295653527208, 8.974916696069096, 33.332582481195494, 5.6861534485771745, 17.953748731149954, 38.35237143214835, 30.701645283513784]\nAltitude:  [38303, 15617, 13940, 3189, 13759, 29607, 28726]\nBearing:  [185.43676664033427, 132.13578494524953, 228.10830043573642, 294.6086394750489, 65.37571048041785, 31.935137363486007, 267.048230502618]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [17, 67, 44, 62, 41, 60, 45]\nRange:  [43.26430734405224, 10.66013975799327, 33.66042651792506, 7.589699465971977, 18.318811882186736, 40.19224060519212, 30.47405951503499]\nAltitude:  [16778, 28768, 31824, 19029, 24300, 23029, 8369]\nBearing:  [57.66871150165641, 62.19016574023109, 73.85951144793529, 146.68735812447727, 250.1654648170205, 159.74931771026624, 332.7939221440234]\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_4_2_2.py->Test_2_4_2_1_4_2: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\nStep1_Results:  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1191396713256836, "start_time": "2025-06-05T21:52:44.367795", "end_time": "2025-06-05T21:52:44.486935", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "PASSED", "return_code": 0, "execution_time": 123.24666237831116, "start_time": "2025-06-05T21:52:44.487659", "end_time": "2025-06-05T21:54:47.734323", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *** DO-185E/385, Mode C Surveillance Initiation, Sect  2.2.4.6.2.1.2***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: *Test_2.2.4.6.2.1.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 11000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step1:Test_385_Mode C Surveillance Initiation - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [76, 8, 57, 91, 29, 52]\nRange:  [44.1591248159417, 16.220120353729666, 28.12301652374764, 5.000812398487914, 46.82577588224292, 6.701739875896617]\nAltitude:  [27197, 39913, 262, 30035, 13600, 8973]\nBearing:  [337.7545385015927, 156.04657055013885, 289.1885959020738, 203.58507139227254, 99.04705428181528, 348.27712846040697]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step2:Test_385_Mode C Surveillance Initiation - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR ALTRPT,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR ALTRPT,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR REPLY,ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR REPLY,OFF\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [44, 63, 64, 37, 36, 89]\nRange:  [4.760740565115906, 6.833493030258858, 42.25021601870416, 42.85669813529476, 13.179036771333653, 33.359993437108784]\nAltitude:  [4760, 28744, 15997, 12315, 6520, 4263]\nBearing:  [121.11882390073082, 202.38312921392176, 299.74882245736137, 296.9973239225532, 142.27049802359866, 209.2858636644534]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Step3:Test_385_Mode C Surveillance Initiation - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODEC_SURV.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODEC_SURV.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:WAYPOINTS:MODE TIME\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYNAMIC:1:WAYPOINTS:NTIM 3\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:BEGIN 0.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:BEGIN 4.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:BEGIN 8.0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:PAR VERTICAL,0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:PAR VERTICAL,-32700\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:1:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:2:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:DYN:1:WAY:TIME:3:ENA ON\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [75, 33, 75, 54, 26, 85, 84]\nRange:  [29.85026455982146, 33.63185003943855, 46.50219541018354, 15.356776807009481, 41.04221043209522, 27.375423559710487, 8.012669214329593]\nAltitude:  [32547, 29953, 10251, 3856, 26666, 13768, 7082]\nBearing:  [326.6698575115352, 317.2437843312298, 350.756826786115, 23.32404368331337, 43.93711320122446, 173.78956889023843, 339.0712228140711]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_1_2.py->Test_2_4_2_1_6_1: Test_2.2.4.6.2.1.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\nStep1_Results:  [0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "PASSED", "return_code": 0, "execution_time": 272.38011360168457, "start_time": "2025-06-05T21:54:47.734860", "end_time": "2025-06-05T21:59:20.114976", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: *Test_2.2.4.4.2.2 - Start\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LAT 48.834340\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:LONG -122.214200\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:HEAD 0\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:ALT 8000\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:OWN:MSADDR 4\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario A\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_A.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_A.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [16, 91, 54, 95, 58]\nRange:  [8.464318560798889, 28.035368475355348, 20.371251889956092, 38.958834508541045, 25.57962306330699]\nAltitude:  [39085, 35421, 24338, 4162, 28142]\nBearing:  [325.44312675823323, 359.45484285330485, 139.96868070359457, 0.26320914332178535, 259.597953621713]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario B\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_B.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_B.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [58, 2, 46, 83, 18, 11]\nRange:  [4.266128435550792, 39.1022405748022, 42.68156894605642, 37.31946017079243, 13.864927338554732, 16.68490816782255]\nAltitude:  [3341, 29857, 37546, 2603, 24467, 36241]\nBearing:  [107.46251586318469, 191.93995630847328, 222.3281784023662, 154.24396309633585, 136.05975477221654, 105.27647087430815]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario C\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_C.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_C.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [39, 19, 40, 29, 92, 41, 63, 5]\nRange:  [39.23041022318505, 22.78134377373383, 36.150540200861286, 10.08121468922591, 42.7677601038364, 2.135238839785958, 4.146106348006745, 28.41692520025072]\nAltitude:  [15166, 23261, 21044, 8155, 13020, 39392, 29739, 15084]\nBearing:  [75.97306798064578, 317.9235363907013, 288.73178638693577, 95.95301810354994, 103.15719061485045, 248.42441754036537, 74.04004127247856, 18.344644336103464]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario D\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_D.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_D.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [66, 12, 4, 62, 74]\nRange:  [24.774219869164355, 7.320189001466687, 34.81147347791811, 3.688516289120758, 13.909804446462443]\nAltitude:  [17265, 16564, 4726, 16697, 903]\nBearing:  [127.7195235582961, 61.332101148873356, 155.78567858801645, 355.99121922915697, 237.25970488640905]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario E\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_E.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_E.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [1, 33, 1, 80, 60]\nRange:  [44.924532522850186, 17.58021749858834, 30.234555456911682, 45.74984607032043, 14.0936687328228]\nAltitude:  [16410, 14762, 30825, 36589, 27153]\nBearing:  [282.6616538689712, 71.81952696435467, 285.1805927681447, 348.44014399439976, 59.73607416873309]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario F\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_F.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_F.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\nIntruders:  [46, 94, 41, 28, 10, 82]\nRange:  [1.034790684603587, 34.68200728925787, 1.8568182519404064, 38.15242136325986, 27.563863328370065, 28.92276732242237]\nAltitude:  [22743, 33718, 32435, 31126, 30342, 33347]\nBearing:  [292.86936517525385, 255.43300167477986, 112.61591484150338, 354.5762014228666, 287.36399213847125, 240.82347905625988]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario G\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_G.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_G.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [37, 42, 23, 22, 57, 56, 76]\nRange:  [0.13468101870536264, 32.40261382282677, 38.64127567695842, 46.616078860835415, 13.129055847862798, 30.384646529606112, 4.477800637112795]\nAltitude:  [742, 8288, 21571, 19694, 5885, 11409, 28034]\nBearing:  [97.97206279889855, 141.75554091069236, 11.007441353860505, 283.40059490956145, 237.96259045750924, 294.0073587938992, 140.22787317363733]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario H\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_H.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_H.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\nIntruders:  [91, 81, 4, 71, 91]\nRange:  [38.44245636744284, 43.32855817969624, 33.284223949231304, 45.4078639018415, 9.024106933419063]\nAltitude:  [39272, 18866, 26305, 25297, 4654]\nBearing:  [11.475953343263967, 266.5878197160934, 166.65420945059645, 36.71273657358151, 128.31437607214164]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario I\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_I.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_I.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\nIntruders:  [73, 31, 92, 27, 94, 68, 64, 87]\nRange:  [47.51943647637308, 36.05153419383531, 49.51021709788511, 2.800861120343207, 16.40038798344783, 4.0196404341646454, 10.547138018676804, 15.68200857877779]\nAltitude:  [29045, 8782, 34037, 16419, 34851, 17933, 13374, 22797]\nBearing:  [307.01269477055865, 327.273564761842, 23.523432104515155, 239.8064790802063, 267.5069889335232, 151.68165067727003, 228.07171850900255, 304.3625676965281]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1:Test_385_Mode S Reply Reception - Scenario J\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD MODES_PRE_J.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario MODES_PRE_J.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\nNumber of Intruders True\nIntruder Return String:  True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\nIntruders:  [26, 34, 24, 34, 37, 35, 43]\nRange:  [37.87382610534712, 7.172739537921985, 39.15652529124189, 4.259570267965018, 35.59277557453591, 33.768607834865136, 11.821949326158638]\nAltitude:  [595, 36565, 31312, 810, 15043, 7135, 35012]\nBearing:  [210.88676999991392, 358.20674192764625, 84.1515225869086, 53.896433541826724, 27.055579474271113, 57.50035003991129, 227.9162456931987]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Test_2.2.4.4.2.2 - Done\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_6_2_2_2.py->Test_2_4_2_1_4_2: Step1_Results: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "PASSED", "return_code": 0, "execution_time": 154.82457041740417, "start_time": "2025-06-05T21:59:20.115868", "end_time": "2025-06-05T22:01:54.940441", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: ARINC_Client.py->__init__: Mock ARINC_Client initialized successfully\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: *** DO-185E/385, Bearing Accuracy, Sect 2.2.4.6.4.2***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg0err: 40.108360228507365\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg1err: 12.740266168577989\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg2err: -43.250902719147774\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg3err: -42.733977857564895\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max0err: -38.33295822807139\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max1err: -44.92483673683097\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max2err: -37.89540042395055\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max3err: -31.545167440983562\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: Bearing AVG and MAX: 40.108360228507365, -31.545167440983562\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brggt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brggt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 7 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 8 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 5 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->readTCASData: Number of Intruders: True\n[MOCK] TXD Python Lib: ARINC_Client.py->TCAS_Decode: Mock TCAS_Decode: 6 intruders\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg0err: 41.64440286671197\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg1err: -35.90827234695627\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg2err: 35.46479683768786\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Avg3err: 38.29960914912607\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max0err: -38.33153398733981\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max1err: -31.4232881107161\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max2err: -28.44752403333854\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->compute_BearingAccuracy: Max3err: -38.808419630477715\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: Bearing AVG and MAX: 41.64440286671197, -28.44752403333854\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: Bearing AVG Error [-1.0, -1.0, 40.108360228507365, 41.64440286671197]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: Bearing Max Error [-1.0, -1.0, -31.545167440983562, -28.44752403333854]\n[MOCK] TXD Python Lib: DO385_2_2_4_6_4_2.py->Test_2_2_4_6_4_2: Test_2.2.4.6.4.2 - Done\n", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 42.22392654418945, "start_time": "2025-06-05T22:01:54.941417", "end_time": "2025-06-05T22:02:37.165343", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n[MOCK] TXD Python Lib: RGS2000NG.py->__init__: Mock RGS Connection Success\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->__init__: Mock B4500CPwrMeter initialized successfully\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->Test_2_3_3_1: *** DO-185E/385, Radiated Output Power, Sect 2.3.3.1 ***\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STOP\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:LOAD Brglt10ModeS.csv\n[MOCK] TXD Python Lib: RGS2000NG.py->loadScen: Mock scenario Brglt10ModeS.csv loaded\n[MOCK] TXD Python Lib: RGS2000NG.py->basicWrite: [MOCK RGS] > :RGS:SCE:STA\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->setup: *Radiated Output Power -Start\nERROR?:  OK\nTimeBase-Pulse:  0.09201286417691937\nTSPAN:  True\n[MOCK] TXD Python Lib: DO385_2_3_3_1.py->trigger: *Radiated Output Power: Pulses: True\n[MOCK] TXD Python Lib: B4500CPwrMeter.py->getpwrmeasuremet: Mock power measurement: -16.62,-22.46,-16.94\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 181, in <module>\n    res = Test_2_3_3_1(rm, rgs, pwr_obj)\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 153, in Test_2_3_3_1\n    res[0] = measurePP(pw, 0)\n             ~~~~~~~~~^^^^^^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 109, in measurePP\n    return float(pwr[3])\n                 ~~~^^^\nIndexError: list index out of range\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15906524658203125, "start_time": "2025-06-05T22:02:37.167084", "end_time": "2025-06-05T22:02:37.326150", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_A_Frequency.py\", line 156, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1526658535003662, "start_time": "2025-06-05T22:02:37.327122", "end_time": "2025-06-05T22:02:37.479789", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_B_Supression.py\", line 157, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1552715301513672, "start_time": "2025-06-05T22:02:37.480607", "end_time": "2025-06-05T22:02:37.635878", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 220, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.14866352081298828, "start_time": "2025-06-05T22:02:37.637036", "end_time": "2025-06-05T22:02:37.785699", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_D_Power.py\", line 325, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.14804315567016602, "start_time": "2025-06-05T22:02:37.786880", "end_time": "2025-06-05T22:02:37.934923", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_E_Diversity.py\", line 304, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15256690979003906, "start_time": "2025-06-05T22:02:37.935626", "end_time": "2025-06-05T22:02:38.088194", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 168, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15126347541809082, "start_time": "2025-06-05T22:02:38.088805", "end_time": "2025-06-05T22:02:38.240068", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 536, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.15157222747802734, "start_time": "2025-06-05T22:02:38.240656", "end_time": "2025-06-05T22:02:38.392228", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 257, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.14807653427124023, "start_time": "2025-06-05T22:02:38.393145", "end_time": "2025-06-05T22:02:38.541222", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 133, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.14851617813110352, "start_time": "2025-06-05T22:02:38.541993", "end_time": "2025-06-05T22:02:38.690510", "stdout": "[MOCK] TXD Python Lib: ate_rm.py->__init__: Mock Resource Manager Initialized.\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_J_Squitter.py\", line 240, in <module>\n    rf_obj = RFBOB(rm)\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 9, "passed": 4, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 10, "failed": 1, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1976.2539639472961, "average_sequence_time": 26.706134647936434, "sequences_per_hour": 134.80048863148266, "optimization_effectiveness": {"optimization_success_rate": 18.91891891891892, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 60, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 1, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 60 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}