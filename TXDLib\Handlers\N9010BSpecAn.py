# -*- coding: utf-8 -*-
"""
Created on Fri Feb 28 3:02:30 2020

@author: E589493
         <PERSON><PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the instantiation of spectrum analyzer class. 


HISTORY:
02/26/2020  KF  Initial Release.
3/31/2020   MR  Added various functions related Amp and Limits
04/29/2020  MR  Added basic error handling and option flow
06/10/2020  MRS  Added Close SpecAnz function.
10/05/2020  NEZ  Updated for new Instrument Resource Manager.

"""

# import pyvisa
import time
import matplotlib.pyplot as plt


class N9010BSpecAn():
    def __init__(self, rm):
        # Add conneciton to Observer client, ignore linting errors
        self.resourceManager = rm
        
        # Attempt connection to resource
        self.specAnzIp = 'TCPIP0::************::INSTR'
        try:
            self.SpecAnz = self.resourceManager.rm.open_resource(self.specAnzIp)
            self.SpecAnz.write("")
            self.resourceManager.logMessage(0, "Connection Success")
        except:
            self.resourceManager.logMessage(3, "Failed to connect to resource")
            raise

##################################################
# Basic Commands
##################################################

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.SpecAnz.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=False):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.SpecAnz.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp

    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, tvlTxt)
    
    def Ident(self):
        return self.basicQuery('*IDN?')

    def Reset(self):
        """ Resets the spectrum analyzer. """
        self.basicWrite('*RST')
        self.resourceManager.logMessage(1, "SpecAnz Resetting...")
        time.sleep(5)
        self.resourceManager.logMessage(1, "SpecAnz complete\n")
        
    def close(self):
        """ Closes the spectrum analyzer """
        self.SpecAnz.close()
        

##################################################
# XTime and YScale Commands
##################################################

    def CenterFreqSet(self, freq, units):
        """ Sets center frequency, pass in frequency number, then uints example: 100, 'MHz'. """
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(':FREQuency:CENTer ' + str(freq) + ' ' + str(units))
        else:
            self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(units))

    def CenterFreqRead(self):
        """ Prints center frequency. """
        self.basicQuery(':FREQuency:CENTer?')

    def ResBandwidthSet(self, freq, units):
        """ Sets Resolution Bandwidth, pass in frequency number, then uints example: 10, 'kHz'. """
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(':BANDwidth:RESolution ' + str(freq) + ' ' + str(units))
        else:
            self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(units))

    def VidBandwidthSet(self, freq, units):
        """ Sets Video Bandwidth, pass in frequency number, then uints example: 10, 'kHz'. """
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(':BANDwidth:VIDeo ' + str(freq) + ' ' + str(units))
        else:
            self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(units))

    def SpanSet(self, freq, units):
        """ Sets Span, pass in frequency number, then uints example: 100, 'kHz'. """
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(':FREQuency:SPAN ' + str(freq) + ' ' + str(units))
        else:
            self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(units))

    def SweepTimeSet(self, sec, units):
        """ Sets sweep time, pass in number, then uints example: 2, 'ms'. """
        if units.lower() in ["", "s", "ms", "us", "ns"]:
            self.basicWrite('SWE:TIME ' + str(sec) + ' ' + str(units))
        else:
            self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(units))

    def SweepContSet(self, cont):
        """ Set Continious or Single Mode, Pass string 'On' for Continous mode,
        or 'Off' for single measurement. """
        self.basicWrite(':INITiate:CONTinuous ' + str(cont))

    def sweepNumPoints(self, points):
        """ Set number of points in sweep """
        self.basicWrite(":SENSe:SWEep:POINts " + str(points))

    def TraceTypeSet(self, traceType):
        """ Set Trace Type. Pass in String 'WRITe', 'AVERage', 'MAXHold', 'MINHold' """
        if traceType.lower() in ["write", "average", "maxhold", "minhold"]:
            self.basicWrite(':TRAC:TYPE ' + str(traceType))
        else:
            self.resourceManager.logMessage(3, "Invalid Trace Typeoption ({})".format(traceType))

    def setDetector(self, mode):
        if mode.upper() in ["AVER", "AVERAGE", "NEG", "NEGATIVE", "NORM", "NORMAL",
                          "POS", "POSITIVE", "SAMP", "SAMPLE", "QPE", "QPEAK", 
                          "EAV", "EAVERAGE", "RAV", "RAVERAGE"]:
            self.basicWrite(":DETector:TRACe " + str(mode))
        else:
            self.resourceManager.logMessage(3, "Invalid Detector Typeoption ({})".format(mode))


    def setRefLevel(self, ref_lvl, unit):
        """ Sets the specAnz trace reference level to <ref_lvl><unit>. For ex 5, dbm """
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:RLEVel {}".format(str(ref_lvl) + " " + unit))

    def setDisplayLine(self, lineLevel):
        """ Sets the horizontal visual reference line to <lineLevel> """
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:DLINe " + str(lineLevel))

    def enableDisplayLine(self):
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:DLINe:STATe ON")

    def disableDisplayLine(self):
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:DLINe:STATe OFF")

##################################################
# MARKER FUNCTIONS AND MEASUREMENT
##################################################

    def MarkerSet(self, num, state):
        """ Set Marker. Pass in integer value 1 through 12, and a string placing marker 
        'ON' or 'OFF'. """
        self.basicWrite(':CALCULATE:MARKER' + str(num) + ':STATE ' + state + '\n')

    def MarkerMode(self, mode):
        """ Set Marker mode. Can be Normal(POSition), Delta(DELTa), Fixed(FIXed), or Off(OFF) """
        if str(mode).upper() in ["POS", "POSITION", "DELT", "DELTA", "FIX", "FIXED", "OFF"]:
            self.basicWrite("CALCulate:MARKer1:MODE " + str(mode))
        else:
            self.resourceManager.logMessage(3, "Invalid Marker Mode Typeoption ({})".format(mode))

    def GetMaxPeakPower(self):
        """This function sets up Marker 1 and returns peak power measured"""
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(1)
        self.basicWrite(':CALCULATE:MARKER1:MAX\n')
        time.sleep(1)
        return self.basicQuery(':CALCULATE:MARKER1:Y?')
        
    def GetMaxPeakFreq(self):
        """This function sets up Marker 1 and returnsfrequency of peak"""
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(1)
        self.basicWrite(':CALCULATE:MARKER1:MAX\n')
        time.sleep(1)
        return self.basicQuery(':CALCULATE:MARKER1:X?')
        
    def BandwidthPowerCalc(self, centfreq, centunits, span, spanunits):
        """ Returns bandwidth power.  User must pass center frequency number, then uints
         example: 100, 'kHz'.  User must als pass bandwidth frequency number, then uints
         example: 10, 'kHz'. eg x = BandwidthPowerCalc(100, kHz, 10, kHz)  """
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(1)
        self.basicWrite(":CALCULATE:MARKER1:MODE POS\n")
        time.sleep(1)
        self.basicWrite('CALCULATE:MARKER1:X:CENTER ' + str(centfreq) + ' ' + str(centunits))
        time.sleep(1)
        self.basicWrite('CALCULATE:MARKER1:X:SPAN ' + str(span) + ' ' + str(spanunits))
        time.sleep(1)
        self.basicWrite(":CALCULATE:MARKER1:FUNCTION BPOWER\n")
        time.sleep(1)
        return self.basicQuery(':CALCULATE:MARKER1:Y?')

    def setMkrRefLevel(self, mkr):
        """ Sets the trace Y reference level to the power level at the marker <mkr 1-12>"""
        ref_pwr = float(self.basicQuery(":CALCULATE:MARKER{}:Y?".format(str(mkr))))
        self.setRefLevel(ref_pwr, "")
    
    def LimitResult(self):
        """ Returns True is pass, False if Fail """
        res = int(self.basicQuery(":CALCUlate:LLINE1:FAIL?"))
        if res == 0:
            return True
        else:
            return False

    def LimitSetLine(self, data_list):
        """ Programs in to Limit Line 1 given <data_list> per relative freqs and amplitudes """
        self.basicWrite("CALCulate:LLINE1:FREQuency:CMODe:RELative 1")
        self.basicWrite("CALCulate:LLINe1:AMPLitude:CMODe:RELative 1")
        format_str = ""
        for i in range(len(data_list)):
            format_str += data_list[i]
            if i < len(data_list) - 1:
                format_str += ", "
        self.basicWrite(":CALCULate:LLINE1:DATA {}".format(format_str))

    def LimitSetState(self, state):
        """ Turns on <1> or off <0> Limit Lines """
        self.basicWrite("CALCulate:LLINe:STATe {}".format(str(state)))

    def disableAverage(self):
        """ Turns off average state """
        self.basicWrite(":AVERage:STATe OFF")

    def enableAverage(self):
        """ Turns on average state """
        self.basicWrite(":AVERage:STATe ON")

    def setAttenuation(self, val):
        """ Sets attenuation value in decibels(dB) """
        self.basicWrite(":POWer:RF:ATTenuation " + str(val))

    def setExternalTrigger(self):
        self.basicWrite(":TRIGger:SEQuence:SOURCe EXTernal")
    
    def setExternalTriggerSource(self, slope):
        if slope.upper() in ["POS", "POSITIVE", "NEG", "NEGATIVE"]:
            self.basicWrite("TRIGger:SEQuence:SLOPe " + str(slope))
        else:
            self.resourceManager.logMessage(3, "Invalid Trigger Slope Typeoption ({})".format(slope))

    def setExternalTriggerDelay(self, time):
        self.basicWrite("TRIGger:SEQuence:DELay " + str(time))


##################################################
# Memory Loading and setting
##################################################

    def loadLimit(self, trace_csv):
        """ Loads given limit line CSV file from onboard Mass Storage """
        self.basicWrite(":MMEMory:LOAD:LIMit LLINE1, {}".format("\"" + str(trace_csv) + "\""))

##################################################
# Data Collection and Plotting
##################################################

    def dataSpecRead(self):
        """ Clears current measurements, reads and returns trace Spec An Data as <freq> and <pwr> """
        data = (self.basicQuery(":READ:SANalyzer?")).split(",")
        data = [float(element) for element in data]
        freq = []
        pwr = []
        for i in range(0, len(data)):
            if i%2 == 0:
                freq.append(data[i])
            else:
                pwr.append(data[i])
        return freq, pwr

    def dataSpecFetch(self):
        """ Fetches and returns current trace Spec An Data as <freq> and <pwr>. """
        data = (self.basicQuery(":FETCH:SANalyzer?")).split(",")
        data = [float(element) for element in data]
        freq = []
        pwr = []
        for i in range(0, len(data)):
            if i%2 == 0:
                freq.append(data[i])
            else:
                pwr.append(data[i])
        return freq, pwr
    
    def plotSpecAn(self, freq, pwr):
        """ User plotting function given <x> and <y> value. For example: <freq>, <pwr> """
        plt.plot(freq, pwr)
        plt.show()

    def plotSave(self, file_name, freq, pwr):
        plt.plot(freq, pwr)
        fname = file_name + ".png"
        plt.savefig(fname)