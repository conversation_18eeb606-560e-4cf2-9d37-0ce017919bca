# -*- coding: utf-8 -*-
"""
Created on Wed Feb 26 3:02:30 2020

@author: H403316
         <PERSON><PERSON> <PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-385 MOPs requirement for
             In Band Acceptance *******.1.1
             
             Given a valid transponder reply signal in the absence of interference or overloads, 
             the minimum trigger level (MTL) is defined as the minimum input power level that results in 
             a 90% ratio of decoded to received replies.
             
             a. The MTL for ATCRBS and Mode S signals over the frequency range of 1087 to 1093
             MHz shall be -74 dBm ±2 dB.
             
             The MTL for Mode S signals over the frequency range of 1089 to 1091 MHz shall
             (1210) be -74 dBm ±2 dB.
             
             Note: This provides adequate link margin for reliable detection of near-co-altitude
             aircraft in level flight at a range of 14 NM.
             Note: To accommodate a shared ACAS X/ADS-B receiver, a narrow band (1089 to
             1091 MHz) receiver for Mode S signals is acceptable.
             
             b. For an input signal power of level -78 dBm or less, no more than 10% of ATCRBS and
             Mode S signals shall (1211) be decoded.
             
             c. The decoding ratio shall (1212) be at least 99% for ATCRBS and Mode S signals
             between MTL +3 dB and -21 dBm.
             
             If the antenna gain is not as specified in §2.2.4.7, the MTL shall (1213) be adjusted to
             account for the antenna gain.
             Note: For example, if the antenna gain were 3 dB higher than specified, the nominal
             MTL would be raised by 3 dB to -71 dBm.
             
INPUTS:      Top_Cable_Loss, RGS, ARINC
OUTPUTS:     Results[36] : For each Power level and Frequency, the vailidty of the targets (number, range, altitude, bearing)
             is checked.  Four Booleans are passed back for each of the three Frequencies, and each of the of the 
             three Power Levels, for a total of 36 Booleans. 


HISTORY:

07/28/2020   AS     Initial Release
10/09/2020   MRS    Updated for new ATE_RM.
03/12/2021   MRS    Updates for Lobster                     
"""

#Required Libraries
import numpy as np
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers.ARINC_Client import ARINC_Client

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def setFrequency(freq, rgs):
    """ Set Generators to frequency passed in as a string. """
    
    rgs.setGeneratorFreq("GENA", freq)
    rgs.setGeneratorFreq("GENB", freq)
    rgs.setGeneratorFreq("GEND", freq)
    rgs.setGeneratorFreq("GENE", freq)
    rgs.setGeneratorFreq("GENF", freq)
    
def setintruderPower(rgs,Pwr):
    """ Set Reply Power Level for all 7 intruders. Pwr is passed in as a
    string. """
    rgs.basicWrite(":RGS:SCE:STATIC:1:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:2:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:3:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:4:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:5:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:6:RPLYPWR " + Pwr)
    rgs.basicWrite(":RGS:SCE:STATIC:7:RPLYPWR " + Pwr)


def readTCASData(ARINC):
    """ Reads data via ARINC client, returns 4 lists containing data
        in the order intruders, range, altitude, bearing. """
    #Read the Number of TCAS Intruders
    intr = ARINC.TCAS_Read(b"READ,TCAS_OUT,INT")
    print("Number of Intruders",intr)

    #Get a List of All TCAS Intruders
    data = ARINC.TCAS_Read(b"READ,TCAS_OUT,ALL")
    print("Intruder Return String: ",data)
    #Decode the Return string into separate lists
    itr,rng,alt,brg = ARINC.TCAS_Decode(data)
    return itr, rng, alt, brg

def testIntruders(rm,ARINC, timeInterval):
    """ Record intruder data at beginning and end of time interval and 
    tests that data matches. Returns 4 booleans in the order 
    intruders, range, altitude, bearing. """

    rm.logMessage(0,"Starting Test for Intruders") 

    # get intruder data at begining of run
    itr1, rng1, alt1, brg1 = readTCASData(ARINC)

    print ("Intruders: ",itr1)
    print ("Range: ",rng1)
    print ("Altitude: ",alt1)
    print ("Bearing: ",brg1)

    time.sleep(timeInterval)

    # get intruder data after one minute (timeInterval)
    itr2, rng2, alt2, brg2 = readTCASData(ARINC)

    print ("Intruders: ",itr2)
    print ("Range: ",rng2)
    print ("Altitude: ",alt2)
    print ("Bearing: ",brg2)

    # check if before/after results are the same
    itrMatch = (itr1 == itr2) and itr1 != []
    rngMatch = (rng1 == rng2) and rng1 != []
    altMatch = (alt1 == alt2) and alt1 != []
    
    # check if bearing results are the within 12 deg
    brgMatch = True
    for i in range(len(brg1)):
        if (np.abs(brg1[i]-brg2[i]) > 12.0):
            brgMatch = False
            break
    brgMatch = brgMatch and brg1 != []
    
    results = [int(itrMatch), int(rngMatch), int(altMatch), int(brgMatch)]
    
    rm.logMessage(0,"Finished Test for Intruders") 

    return results

       

##############################################################################
################# MAIN     ##################################################
##############################################################################


def InBandAcceptance(rm,rgs, ARINC):
    
    Power_Levels = [-64, -40, -21]
    results = []

    #Load Default Qual Scenario
    rgs.loadScen('qual_test_a350.csv')
    time.sleep(10)
    
    #Check each power level, at each frequency
    for pwr in Power_Levels:
        

        # set frequency 1090 MHz
        rgs.stopScen() #stop scenario if already running. 
        rm.logMessage(0,"Setting Frequency to 1090MHz") 
        setFrequency("1090", rgs)
        
        # set intruder power
        setintruderPower(rgs,str(pwr))

    
        # Load and Start Scenario
        rgs.stopScen() #stop scenario if already running.
        rgs.startScen()
        time.sleep(30)
    
        results = results + testIntruders(rm,ARINC, 30)
    
    
        rgs.stopScen()
    
    
        # set frequency 1087 MHz
        rm.logMessage(0,"Setting Frequency to 1087MHz") 
        setFrequency("1087", rgs)
    
        # Start Scenario
        rgs.startScen()
        time.sleep(30)
    
        results = results + testIntruders(rm,ARINC, 30)
    
        
        rgs.stopScen()
    
     
        # set frequency 1093 MHz
        rm.logMessage(0,"Setting Frequency to 1093MHz") 
        setFrequency("1093", rgs)
    
        # Start Scenario
        rgs.startScen()
        time.sleep(30)
    
        results = results + testIntruders(rm,ARINC, 30)
    
         
        rgs.stopScen()   
        
        print("RESULTS: ",results)
    
    return results
##############################################################################

if __name__ == "__main__":
    

    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
  

    InBandAcceptance(rm, rgs, ARINC)
    
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
    
    #Close RGS
    rgs.close()
