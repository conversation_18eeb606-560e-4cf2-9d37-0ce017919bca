# TXD Qualification Test System - Reports Directory

This directory contains all system test execution reports.

## Directory Structure

- `system_test_reports/` - Individual system test execution reports
- `consolidated_reports/` - Consolidated final reports

## Report Types

### Consolidated Reports
- **JSON Format**: Machine-readable detailed results
- **Markdown Format**: Human-readable formatted reports

## Report Naming Convention

Reports are named with the following pattern:
```
system_test_report_{mode}_{timestamp}.{format}
```

Where:
- `mode`: `mock` or `live`
- `timestamp`: `YYYYMMDD_HHMMSS`
- `format`: `json` or `md`

## Usage

Reports are automatically generated by the system test runner:

```bash
# Generate both JSON and Markdown reports
python run_system_test.py --mode mock --procedures all

# Generate only JSON reports
python run_system_test.py --mode live --procedures DO282 --report-format json
```

## Report Contents

Each consolidated report includes:
- Executive summary with pass/fail status
- Procedure breakdown by test category
- Performance metrics and timing data
- Individual sequence results
- Failure analysis and recommendations
- Optimization effectiveness validation
