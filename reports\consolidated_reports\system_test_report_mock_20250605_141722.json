{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:17:22.587004", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 9, "passed": 4, "failed": 5, "errors": 0, "timeouts": 0, "success_rate": 44.44444444444444, "total_execution_time": 1.6088881492614746, "start_time": "2025-06-05T14:17:20.977102", "end_time": "2025-06-05T14:17:22.585990"}, "sequence_results": [{"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19710683822631836, "start_time": "2025-06-05T14:17:20.978052", "end_time": "2025-06-05T14:17:21.175161", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18836379051208496, "start_time": "2025-06-05T14:17:21.176506", "end_time": "2025-06-05T14:17:21.364870", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18549251556396484, "start_time": "2025-06-05T14:17:21.366494", "end_time": "2025-06-05T14:17:21.551989", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 44, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20294618606567383, "start_time": "2025-06-05T14:17:21.554018", "end_time": "2025-06-05T14:17:21.756963", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 41, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18834400177001953, "start_time": "2025-06-05T14:17:21.758792", "end_time": "2025-06-05T14:17:21.947137", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 36, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1394946575164795, "start_time": "2025-06-05T14:17:21.948982", "end_time": "2025-06-05T14:17:22.088474", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1854097843170166, "start_time": "2025-06-05T14:17:22.089092", "end_time": "2025-06-05T14:17:22.274503", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_LOGGING.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1693744659423828, "start_time": "2025-06-05T14:17:22.275618", "end_time": "2025-06-05T14:17:22.444994", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14008855819702148, "start_time": "2025-06-05T14:17:22.445665", "end_time": "2025-06-05T14:17:22.585757", "stdout": "", "stderr": ""}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO282": {"total": 9, "passed": 4, "failed": 5, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 1.6088881492614746, "average_sequence_time": 0.17876534991794163, "sequences_per_hour": 20138.13080472531, "optimization_effectiveness": {"optimization_success_rate": 44.44444444444444, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 5, "failure_by_procedure": {"DO282": 5}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 5 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}