#!/usr/bin/env python3
"""
Test the legacy DO181 sequence fix with compatibility layer
"""

import subprocess
import sys
import os
from pathlib import Path

# Set up environment like sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

print("Testing legacy DO181 sequence with compatibility layer...")
print("=" * 60)

# Test the failing sequence
sequence = "TXDLib/Procedures/DO181/DO_181E_2_3_2_12.py"

print(f"Testing: {sequence}")

# First, let's test the imports directly
print("\n1. Testing legacy module imports...")
test_imports = '''
import sys
sys.path.insert(0, "MockHandlers")

# Import mock CLR first
import mock_clr

print("Testing legacy imports...")
try:
    sys.path.append("MockHandlers/common")
    import atc
    print("✅ atc module imported successfully")
    
    import powersupply  
    print("✅ powersupply module imported successfully")
    
    import clr
    print("✅ clr module imported successfully")
    
    # Test ATC class instantiation
    import pyvisa
    rm = pyvisa.ResourceManager()
    atc_obj = atc.ATC(rm)
    print("✅ atc.ATC instantiated successfully")
    
    # Test power supply instantiation
    pwr_obj = powersupply.Powersup(rm)
    print("✅ powersupply.Powersup instantiated successfully")
    
    print("\\n✅ All legacy imports working!")
    
except Exception as e:
    print(f"❌ Import test failed: {e}")
    import traceback
    traceback.print_exc()
'''

print("Running import test...")
result = subprocess.run(
    [sys.executable, '-c', test_imports],
    capture_output=True,
    text=True,
    env=env
)

print("Import test results:")
print(result.stdout)
if result.stderr:
    print("STDERR:")
    print(result.stderr)

# Now test the actual sequence
print(f"\n2. Testing actual sequence: {sequence}")
try:
    result = subprocess.run(
        [sys.executable, sequence],
        capture_output=True,
        text=True,
        timeout=300,  # 5 minute timeout
        env=env
    )
    
    print(f"Return code: {result.returncode}")
    if result.returncode == 0:
        print("✅ PASSED - Legacy sequence now working!")
        print("STDOUT:")
        print(result.stdout[-1000:])  # Last 1000 chars
    else:
        print("❌ FAILED")
        print("STDERR:")
        print(result.stderr[-1000:])  # Last 1000 chars of error
        
except subprocess.TimeoutExpired:
    print("⏱️ TIMEOUT - Likely working but slow")
except Exception as e:
    print(f"❌ EXCEPTION: {e}")

print("\n" + "=" * 60)
print("Legacy compatibility test complete!")
