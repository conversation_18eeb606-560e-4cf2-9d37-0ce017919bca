''' Collect data from RF FPGA BITE  '''

import json, time, os, glob, toml, socket, base64
from datetime import date, datetime

''' Retrieve latest list of raw signed hex xadc counts from FPGA BIT '''
def readBITMux():
    today = date.today()
    this_year = str(today)[:4]
    this_month = str(today)[5:7]
    this_day = str(today)[8:10]
    current_directory = os.getcwd()
    time.sleep(0.5)
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\bit-mux-response")
    return_values = []
    voltages = []
    output_file = (glob.glob("bit-mux-response_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
    if len(output_file) > 0:
        dateTimeObj = datetime.now()
        timestampStr = dateTimeObj.strftime("%H%M%S")
        currentFileTimeStamp = max(output_file)[-11:-5]
        if abs(int(timestampStr) - int(currentFileTimeStamp) <= 10):
            with open(max(output_file), "r") as read_file:
                json_response = json.load(read_file)
                return_values.append(json_response["bit-mux-response"]["bit_mux_data"])
            os.remove(max(output_file))
            os.chdir(current_directory)
            return_values[0] = return_values[0].strip()
            i = 0
            while i < len(return_values[0]):
                voltages.append(return_values[0][i:i+4].zfill(4))
                i = i + 4
    return(voltages)

def readBITMux_direct():
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                      socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    
    i=0
    while i<1:
        data = sock.recv(2048)
        data = data.decode('utf-8').split('\x00')[0]
        data = json.loads(data)
        try:
            data = (base64.b64decode(data['bit-mux-response']['bit_mux_data_base64']))
            i+=1
        except KeyError:
            pass
    sock.close()
    return_values = []
    voltages = []
    dataarr = bytearray(data)
    flippedData = bytearray(data)
    index = 0
    while (index<len(dataarr)):
        for j in range(0,8):
            flippedData[index+j]=dataarr[(7-j)+index]
        index+=8
    data=flippedData.hex()
    return_values.append(data)
    return_values[0] = return_values[0].strip()

    i=0
    while i < len(return_values[0]):
        voltages.append(return_values[0][i:i+4].zfill(4))
        i = i + 4

    return(voltages)

''' Read xadc BIT signals from FPGA, convert into analog readings based on coefficients,
    return list of signals '''
def updateBITMonitor(aterm, BITCoefficients=None):
    aterm.logMessage(1, "Procedure Started")
    if None==BITCoefficients:
        with open("C:\\Honeywell\\TXD LRU Test SW\\BITCoefficients.toml", "r") as f:
            coefficients = toml.load(f)

        BITCoefficients = []
        BITCoefficients.append(coefficients['+50V_E1'])
        BITCoefficients.append(coefficients['+50V_E2'])
        BITCoefficients.append(coefficients['+2.5V'])
        BITCoefficients.append(coefficients['E1_PA_TEMPERATURE'])
        BITCoefficients.append(coefficients['+30V'])
        BITCoefficients.append(coefficients['+5V_TX'])
        BITCoefficients.append(coefficients['+3.3V'])
        BITCoefficients.append(coefficients['+1.8V'])
        BITCoefficients.append(coefficients['+1.0V'])
        BITCoefficients.append(coefficients['E1_BOT_ANTENNA_BIT'])
        BITCoefficients.append(coefficients['E1_TOP_ANTENNA_BIT'])
        BITCoefficients.append(coefficients['E2_CAPACITOR_TEMPERATURE'])
        BITCoefficients.append(coefficients['-5V_RX'])
        BITCoefficients.append(coefficients['TX_LO_POWER'])
        BITCoefficients.append(coefficients['MEZALOK_TEMPERATURE'])
        BITCoefficients.append(coefficients['-280V'])
        BITCoefficients.append(coefficients['RX_DME_LO_POWER'])
        BITCoefficients.append(coefficients['+4VDC_FILTER'])
        BITCoefficients.append(coefficients['RX_LO_POWER'])
        BITCoefficients.append(coefficients['+1.8V_ADC'])
        BITCoefficients.append(coefficients['+1.2V'])
        BITCoefficients.append(coefficients['+58VDC_FILTER'])
        BITCoefficients.append(coefficients['FLAGS'])
        BITCoefficients.append(coefficients['Temp. Sensor'])
        BITCoefficients.append(coefficients['VCCINT'])
        BITCoefficients.append(coefficients['VCCAUX'])
        BITCoefficients.append(coefficients['VpVn'])
        BITCoefficients.append(coefficients['VREFP'])
        BITCoefficients.append(coefficients['VREFN'])
        BITCoefficients.append(coefficients['VCCBRAM'])

    # Read BIT Register
    # Parse and read
    # function to read BIT
    BITBufferRaw = readBITMux_direct() # list of 16-bit signed hex values
    # print(BITBufferRaw)
    # Change ordering of list
    adjList = []
    sizeOfList = len(BITBufferRaw)
    BITSize = sizeOfList // 4
    for i in range(BITSize):
        adjList += BITBufferRaw[i * 4:4 + (4 * i)][::-1]
    adjList = adjList[0:22] + adjList[32:40] # take only the signals we want
    # print(adjList)

    xadcCounts = []
    statusIndex = 23 # No two's complement for flag register
    sizeOfList = len(adjList)
    for i in range(sizeOfList):
        temp = int(adjList[i], 16)
        if (temp & 0x8000) == 0x8000 and i != statusIndex:
            temp = -((temp ^ 0xffff) + 1)
        xadcCounts.append(temp)
    
    #print(xadcCounts)
    BITBuffer = []
    sizeOfList = len(xadcCounts)
    for i in range(sizeOfList):
        x = xadcCounts[i]
        m4 = BITCoefficients[i]["m_4"]
        m3 = BITCoefficients[i]["m_3"]
        m2 = BITCoefficients[i]["m_2"]
        m1 = BITCoefficients[i]["m_1"]
        b  = BITCoefficients[i]["b"]
        recoveredVal = (m4*x**4) + (m3*x**3) + (m2*x**2) + (m1*x) + b
        BITBuffer.append(recoveredVal)
    aterm.logMessage(1, str(BITBuffer))
    aterm.logMessage(1, "Procedure Ended")
    return BITBuffer

def getBITNames(aterm):
    aterm.logMessage(1, "Procedure Started")
    BITNames = []
    BITNames.append("+50V_E1")
    BITNames.append("+50V_E2")
    BITNames.append("+2.5V")
    BITNames.append("E1_PA_TEMP")
    BITNames.append("+30V")
    BITNames.append("+5V_TX")
    BITNames.append("+3.3V")
    BITNames.append("+1.8V")
    BITNames.append("+1.0V")
    BITNames.append("E1_BOT_ANTENNA_BIT")
    BITNames.append("E1_TOP_ANTENNA_BIT")
    BITNames.append("E2_CAPACITOR_TEMP")
    BITNames.append("-5V_RX")
    BITNames.append("TX_LO_POWER")
    BITNames.append("MEZALOK_TEMP")
    BITNames.append("-280V")
    BITNames.append("RX_DME_LO_POWER")
    BITNames.append("+4VDC_FILTER")
    BITNames.append("RX_LO_POWER")
    BITNames.append("+1.8V_ADC")
    BITNames.append("+1.2V")
    BITNames.append("+58VDC_FILTER")
    BITNames.append("Temp. Sensor")
    BITNames.append("VCCINT")
    BITNames.append("VCCAUX")
    BITNames.append("Vp/Vn")
    BITNames.append("VREFP")
    BITNames.append("VREFN")
    BITNames.append("VCCBRAM")

    BITNames.append("JTGD")
    BITNames.append("JTGR")
    BITNames.append("REF")
    BITNames.append("VCCO_DDR")
    BITNames.append("VCCPAUX")
    BITNames.append("VCCPINT")
    BITNames.append("VCCBRAM")
    BITNames.append("OT")
    BITNames.append("VCCAUX")
    BITNames.append("VCCINT")
    BITNames.append("Temp.")
    aterm.logMessage(1, str(BITNames))
    aterm.logMessage(1, "Procedure Ended")
    return BITNames