#!/usr/bin/env python3
"""
Legacy Power Supply module compatibility layer for DO181 sequence DO_181E_2_3_2_12.py
Provides mock power supply functionality
"""

import time
import random


class Powersup:
    """Legacy Power Supply class mock implementation"""
    
    def __init__(self, resource_manager):
        """Initialize with legacy resource manager"""
        self.rm = resource_manager
        self.output_state = "OFF"
        self.voltage = 28.0
        self.current = 0.0
        
        print("Mock Power Supply initialized successfully")
        
    def Reset(self):
        """Legacy reset method"""
        self.output_state = "OFF"
        self.voltage = 28.0
        self.current = 0.0
        time.sleep(0.1)  # Reduced timing
        print("Mock Power Supply reset")
        return True
        
    def Ident(self):
        """Legacy identification method"""
        return "Mock Power Supply,Model PS-2000,SN123456,FW1.0"
        
    def setOuput(self, state):
        """Legacy output control method (note: typo 'Ouput' preserved for compatibility)"""
        self.output_state = state.upper()
        
        if self.output_state == "ON":
            self.voltage = 28.0 + random.uniform(-0.5, 0.5)
            self.current = 2.5 + random.uniform(-0.2, 0.2)
            print(f"Mock Power Supply: Output ON - {self.voltage:.1f}V, {self.current:.1f}A")
        else:
            self.voltage = 0.0
            self.current = 0.0
            print("Mock Power Supply: Output OFF")
            
        time.sleep(0.05)  # Reduced timing for power switching
        return True
        
    def setOutput(self, state):
        """Corrected spelling version of setOuput"""
        return self.setOuput(state)
        
    def getVoltage(self):
        """Get output voltage"""
        return self.voltage
        
    def getCurrent(self):
        """Get output current"""
        return self.current
        
    def getState(self):
        """Get output state"""
        return self.output_state
        
    def close(self):
        """Close power supply connection"""
        self.output_state = "OFF"
        self.voltage = 0.0
        self.current = 0.0
        print("Mock Power Supply closed")
        return True
