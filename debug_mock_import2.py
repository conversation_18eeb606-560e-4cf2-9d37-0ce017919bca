#!/usr/bin/env python3
"""
Debug script to test mock import behavior - test direct import
"""

import subprocess
import sys
import os
from pathlib import Path

# Simulate the exact environment from sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

# Test script that tries different import approaches
test_script = '''
import sys
print("Testing different import approaches...")

# Test 1: Direct import from MockHandlers
print("\\n1. Testing direct import from MockHandlers:")
try:
    sys.path.insert(0, "MockHandlers")
    from TXDLib.Handlers import ate_rm
    print(f"ate_rm type: {type(ate_rm)}")
    rm = ate_rm()
    print("SUCCESS: Direct MockHandlers import worked!")
except Exception as e:
    print(f"FAILED: {e}")

# Test 2: Check what TXDLib.Handlers resolves to
print("\\n2. Checking TXDLib.Handlers resolution:")
try:
    import TXDLib.Handlers
    print(f"TXDLib.Handlers location: {TXDLib.Handlers.__file__}")
    print(f"TXDLib.Handlers contents: {dir(TXDLib.Handlers)}")
except Exception as e:
    print(f"FAILED: {e}")

# Test 3: Check if MockHandlers/TXDLib exists
print("\\n3. Checking MockHandlers/TXDLib structure:")
import os
mock_path = "MockHandlers/TXDLib/Handlers"
if os.path.exists(mock_path):
    print(f"MockHandlers/TXDLib/Handlers exists")
    files = os.listdir(mock_path)
    print(f"Files: {files[:5]}...")  # Show first 5 files
else:
    print("MockHandlers/TXDLib/Handlers does not exist")
'''

print("\nRunning subprocess test...")
result = subprocess.run(
    [sys.executable, '-c', test_script],
    capture_output=True,
    text=True,
    env=env
)

print("STDOUT:")
print(result.stdout)
if result.stderr:
    print("STDERR:")
    print(result.stderr)
