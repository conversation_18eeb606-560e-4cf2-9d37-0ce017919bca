{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-04T22:25:56.601478", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 73, "passed": 4, "failed": 69, "errors": 0, "timeouts": 0, "success_rate": 5.47945205479452, "total_execution_time": 12.853426218032837, "start_time": "2025-06-04T22:25:43.748052", "end_time": "2025-06-04T22:25:56.601478"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18196654319763184, "start_time": "2025-06-04T22:25:43.750056", "end_time": "2025-06-04T22:25:43.932022", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16996550559997559, "start_time": "2025-06-04T22:25:43.932022", "end_time": "2025-06-04T22:25:44.101988", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16598296165466309, "start_time": "2025-06-04T22:25:44.102987", "end_time": "2025-06-04T22:25:44.268970", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16697168350219727, "start_time": "2025-06-04T22:25:44.269960", "end_time": "2025-06-04T22:25:44.436931", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17796921730041504, "start_time": "2025-06-04T22:25:44.437933", "end_time": "2025-06-04T22:25:44.615902", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16898465156555176, "start_time": "2025-06-04T22:25:44.616902", "end_time": "2025-06-04T22:25:44.785886", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16897225379943848, "start_time": "2025-06-04T22:25:44.786875", "end_time": "2025-06-04T22:25:44.955846", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17197394371032715, "start_time": "2025-06-04T22:25:44.956846", "end_time": "2025-06-04T22:25:45.128820", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16597366333007812, "start_time": "2025-06-04T22:25:45.128820", "end_time": "2025-06-04T22:25:45.294793", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 32, in <module>\n    import pyvisa\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1669764518737793, "start_time": "2025-06-04T22:25:45.294793", "end_time": "2025-06-04T22:25:45.461770", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16397786140441895, "start_time": "2025-06-04T22:25:45.462765", "end_time": "2025-06-04T22:25:45.626743", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16496658325195312, "start_time": "2025-06-04T22:25:45.626743", "end_time": "2025-06-04T22:25:45.791710", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16297292709350586, "start_time": "2025-06-04T22:25:45.792710", "end_time": "2025-06-04T22:25:45.955683", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16298413276672363, "start_time": "2025-06-04T22:25:45.956682", "end_time": "2025-06-04T22:25:46.119666", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16598272323608398, "start_time": "2025-06-04T22:25:46.120656", "end_time": "2025-06-04T22:25:46.286639", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16397452354431152, "start_time": "2025-06-04T22:25:46.287627", "end_time": "2025-06-04T22:25:46.451602", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16398096084594727, "start_time": "2025-06-04T22:25:46.452601", "end_time": "2025-06-04T22:25:46.616582", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16398167610168457, "start_time": "2025-06-04T22:25:46.617573", "end_time": "2025-06-04T22:25:46.781555", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497445106506348, "start_time": "2025-06-04T22:25:46.782553", "end_time": "2025-06-04T22:25:46.947528", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1639728546142578, "start_time": "2025-06-04T22:25:46.948519", "end_time": "2025-06-04T22:25:47.112492", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 50, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497397422790527, "start_time": "2025-06-04T22:25:47.113492", "end_time": "2025-06-04T22:25:47.278466", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17097187042236328, "start_time": "2025-06-04T22:25:47.279464", "end_time": "2025-06-04T22:25:47.450436", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497349739074707, "start_time": "2025-06-04T22:25:47.451437", "end_time": "2025-06-04T22:25:47.616411", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 53, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16597199440002441, "start_time": "2025-06-04T22:25:47.616411", "end_time": "2025-06-04T22:25:47.782383", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497373580932617, "start_time": "2025-06-04T22:25:47.783381", "end_time": "2025-06-04T22:25:47.948355", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16402411460876465, "start_time": "2025-06-04T22:25:47.949355", "end_time": "2025-06-04T22:25:48.113379", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17795991897583008, "start_time": "2025-06-04T22:25:48.114375", "end_time": "2025-06-04T22:25:48.292335", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18297076225280762, "start_time": "2025-06-04T22:25:48.293335", "end_time": "2025-06-04T22:25:48.476306", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1809687614440918, "start_time": "2025-06-04T22:25:48.477306", "end_time": "2025-06-04T22:25:48.658274", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17497038841247559, "start_time": "2025-06-04T22:25:48.659275", "end_time": "2025-06-04T22:25:48.834246", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769697666168213, "start_time": "2025-06-04T22:25:48.835246", "end_time": "2025-06-04T22:25:49.012216", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17798709869384766, "start_time": "2025-06-04T22:25:49.013217", "end_time": "2025-06-04T22:25:49.191204", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 39, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17895984649658203, "start_time": "2025-06-04T22:25:49.192197", "end_time": "2025-06-04T22:25:49.371157", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18197035789489746, "start_time": "2025-06-04T22:25:49.372158", "end_time": "2025-06-04T22:25:49.554128", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1829690933227539, "start_time": "2025-06-04T22:25:49.555128", "end_time": "2025-06-04T22:25:49.738097", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17796945571899414, "start_time": "2025-06-04T22:25:49.739098", "end_time": "2025-06-04T22:25:49.917068", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_10.py\", line 101, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769700050354004, "start_time": "2025-06-04T22:25:49.918068", "end_time": "2025-06-04T22:25:50.095038", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_12.py\", line 52, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17597270011901855, "start_time": "2025-06-04T22:25:50.096038", "end_time": "2025-06-04T22:25:50.272010", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_1_b.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1789705753326416, "start_time": "2025-06-04T22:25:50.273009", "end_time": "2025-06-04T22:25:50.451980", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_3.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17497014999389648, "start_time": "2025-06-04T22:25:50.452980", "end_time": "2025-06-04T22:25:50.627951", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_4.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769697666168213, "start_time": "2025-06-04T22:25:50.628952", "end_time": "2025-06-04T22:25:50.805921", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_6.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17996978759765625, "start_time": "2025-06-04T22:25:50.806922", "end_time": "2025-06-04T22:25:50.986892", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_7.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17396998405456543, "start_time": "2025-06-04T22:25:50.987892", "end_time": "2025-06-04T22:25:51.161863", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_8.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1749706268310547, "start_time": "2025-06-04T22:25:51.162864", "end_time": "2025-06-04T22:25:51.337834", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19396734237670898, "start_time": "2025-06-04T22:25:51.337834", "end_time": "2025-06-04T22:25:51.531802", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248211.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19696712493896484, "start_time": "2025-06-04T22:25:51.531802", "end_time": "2025-06-04T22:25:51.728769", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248212.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19347763061523438, "start_time": "2025-06-04T22:25:51.728769", "end_time": "2025-06-04T22:25:51.922247", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248213.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19096875190734863, "start_time": "2025-06-04T22:25:51.924246", "end_time": "2025-06-04T22:25:52.115215", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_24822.py\", line 40, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19496774673461914, "start_time": "2025-06-04T22:25:52.116215", "end_time": "2025-06-04T22:25:52.311182", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_24823.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.17597007751464844, "start_time": "2025-06-04T22:25:52.312184", "end_time": "2025-06-04T22:25:52.488154", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.21596407890319824, "start_time": "2025-06-04T22:25:52.489154", "end_time": "2025-06-04T22:25:52.705118", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.17997121810913086, "start_time": "2025-06-04T22:25:52.706119", "end_time": "2025-06-04T22:25:52.886090", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16597294807434082, "start_time": "2025-06-04T22:25:52.886090", "end_time": "2025-06-04T22:25:53.052063", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_3.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17797064781188965, "start_time": "2025-06-04T22:25:53.052063", "end_time": "2025-06-04T22:25:53.230033", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_5.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1809682846069336, "start_time": "2025-06-04T22:25:53.230033", "end_time": "2025-06-04T22:25:53.411001", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_8.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769719123840332, "start_time": "2025-06-04T22:25:53.411001", "end_time": "2025-06-04T22:25:53.587974", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 53, in <module>\n    import numpy as np\nModuleNotFoundError: No module named 'numpy'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17496919631958008, "start_time": "2025-06-04T22:25:53.588974", "end_time": "2025-06-04T22:25:53.763944", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 70, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769709587097168, "start_time": "2025-06-04T22:25:53.764944", "end_time": "2025-06-04T22:25:53.941915", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1749715805053711, "start_time": "2025-06-04T22:25:53.941915", "end_time": "2025-06-04T22:25:54.116886", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17597150802612305, "start_time": "2025-06-04T22:25:54.117887", "end_time": "2025-06-04T22:25:54.293858", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17796921730041504, "start_time": "2025-06-04T22:25:54.293858", "end_time": "2025-06-04T22:25:54.471828", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1749722957611084, "start_time": "2025-06-04T22:25:54.472827", "end_time": "2025-06-04T22:25:54.647800", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769697666168213, "start_time": "2025-06-04T22:25:54.647800", "end_time": "2025-06-04T22:25:54.824769", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_3_3_1.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16897296905517578, "start_time": "2025-06-04T22:25:54.825769", "end_time": "2025-06-04T22:25:54.994742", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_A_Frequency.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17797017097473145, "start_time": "2025-06-04T22:25:54.995742", "end_time": "2025-06-04T22:25:55.173712", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_B_Supression.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769711971282959, "start_time": "2025-06-04T22:25:55.174712", "end_time": "2025-06-04T22:25:55.351683", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1789705753326416, "start_time": "2025-06-04T22:25:55.352683", "end_time": "2025-06-04T22:25:55.531654", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_D_Power.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769719123840332, "start_time": "2025-06-04T22:25:55.531654", "end_time": "2025-06-04T22:25:55.708625", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_E_Diversity.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17997026443481445, "start_time": "2025-06-04T22:25:55.708625", "end_time": "2025-06-04T22:25:55.888596", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1769697666168213, "start_time": "2025-06-04T22:25:55.888596", "end_time": "2025-06-04T22:25:56.065565", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 62, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17597198486328125, "start_time": "2025-06-04T22:25:56.066566", "end_time": "2025-06-04T22:25:56.242538", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1789689064025879, "start_time": "2025-06-04T22:25:56.242538", "end_time": "2025-06-04T22:25:56.421507", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17797112464904785, "start_time": "2025-06-04T22:25:56.422507", "end_time": "2025-06-04T22:25:56.600478", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_J_Squitter.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 1, "failed": 10, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 12.853426218032837, "average_sequence_time": 0.1760743317538745, "sequences_per_hour": 20445.910338778172, "optimization_effectiveness": {"optimization_success_rate": 5.47945205479452, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 69, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 10, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 69 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}