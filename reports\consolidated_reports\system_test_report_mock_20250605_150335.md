# TXD Qualification Test System - System Test Report

**Generated**: 2025-06-05 15:03:35
**Execution Mode**: MOCK
**Report Version**: 1.0.0

## Executive Summary: ❌ FAILED

| Metric | Value |
|--------|-------|
| Total Sequences | 11 |
| Passed | 8 |
| Failed | 3 |
| Errors | 0 |
| Timeouts | 0 |
| Success Rate | 72.7% |
| Total Execution Time | 1692.8 seconds |

## Procedure Breakdown

### ❌ DO385
- **Total Sequences**: 11
- **Passed**: 8
- **Failed**: 3
- **Success Rate**: 72.7%

## Performance Metrics

- **Total Execution Time**: 1692.8 seconds
- **Average Sequence Time**: 153.9 seconds
- **Sequences Per Hour**: 23.4
- **Optimization Status**: ACTIVE

## Recommendations

1. Review failed sequences and address root causes before production use
2. Investigate 3 failed sequences
3. Verify hardware connections before live mode execution
4. Run mock mode tests regularly to validate sequence logic
5. Monitor execution times for performance regression
6. Keep system test reports for compliance documentation

## Individual Sequence Results

### ✅ DO385/DO385_2_2_3_3.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ✅ DO385/DO385_2_2_3_5.py
- **Status**: PASSED
- **Execution Time**: 100.2 seconds

### ❌ DO385/DO385_2_2_3_8.py
- **Status**: FAILED
- **Execution Time**: 33.3 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ✅ DO385/DO385_2_2_4_4_1_1.py
- **Status**: PASSED
- **Execution Time**: 552.7 seconds

### ✅ DO385/DO385_2_2_4_4_2_1.py
- **Status**: PASSED
- **Execution Time**: 310.5 seconds

### ✅ DO385/DO385_2_2_4_4_2_2.py
- **Status**: PASSED
- **Execution Time**: 272.4 seconds

### ✅ DO385/DO385_2_2_4_5_4_1.py
- **Status**: PASSED
- **Execution Time**: 0.4 seconds

### ✅ DO385/DO385_2_2_4_6_2_1_2.py
- **Status**: PASSED
- **Execution Time**: 123.3 seconds

### ✅ DO385/DO385_2_2_4_6_2_2_2.py
- **Status**: PASSED
- **Execution Time**: 272.4 seconds

### ❌ DO385/DO385_2_2_4_6_4_2.py
- **Status**: FAILED
- **Execution Time**: 27.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ❌ DO385/DO385_2_3_3_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

