# -*- coding: utf-8 -*-
"""
Created on Tues April 27 3:20:30 2020

@author: E589493
         <PERSON><PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Receiver Sensitivity, Section 2.2.8a.
             
             "When the input signal at the equipment input terminals
             is the test signal having 70% reply efficiency:

             a. The equipment minimum sensitivity level shall not
             exceed -83dBm with or without TACAN modulation in the 
             absence of all interfering signals."
             
INPUTS:      RM, atc, SigGen ARINC client, RFPathLoss_Top 
OUTPUTS:     Results[4] - Minimum SignalLevels for ChanX and ChanY with/without TACAN

             Note: Assumes RF BOB is configured in TestStand for SG Mixing.

HISTORY:

3/03/2020   KF    Initial Release.
06/22/2020   AS    Added tvl statements
                   Added ARINC client
03/10/2021   MRS   Updates for new handlers and Lobster.                                 
"""

#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers import N5172BSigGen

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def init_DME_Standard(rm, cable_loss, atc, ARINC):
    """ Sets DME Standard Conditions to DME Channel 56X with VOR Pair 0 111.9MHz at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    rm.logMessage(0,"*Test_189_2_2_8 - Initializing ATC into DME mode ") 
    ARINC.writeChannel(111.90)
    time.sleep(5)
    atc.DMEMode()
    atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 40")
    time.sleep(1)
    atc.gwrite(':ATC:DME:EFF 70')
    time.sleep(1)
    atc.gwrite(':ATC:DME:POWER -48')
    time.sleep(10)

def set_VOR_PAIR5(atc, ARINC, sg):
    """ Sets up channel VOR Pair 5 117.9"""
    ARINC.writeChannel(117.95)
    time.sleep(5)
    #Turn Off RF
    atc.gwrite(":ATC:DME:CHANNEL:MODE 5VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 117.9")
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 40")
    time.sleep(1)
    atc.gwrite(':ATC:DME:EFF 70')
    time.sleep(1)
    atc.gwrite(':ATC:DME:POWER -48')
    time.sleep(10)

def initSensitDMEStart(rm, atc, powerLevel):
    """ This Function sets the efficiency to 70m and changes the reutrn power level """
    rm.logMessage(0,"*Test_189_2_2_8 - Setting Power Level to " + str(powerLevel) + " and Efficiency to 70%") 
    #Set Power Lewvel to powerLevel 
    cmd = ':ATC:DME:POWER ' + str(powerLevel)
    atc.gwrite(cmd)
    #Set Reply Efficiency to 70%
    atc.gwrite(':ATC:DME:EFF 70')
    time.sleep(15)

def setupSigGen(rm, sg, state):
    """ Initializes sigGen"""
    rm.logMessage(0,"*Test_189_2_2_8 - Initializing SigGen ") 

    sg.Reset()

    #Download the WaveForm
    #res = sg.downloadwaveform('TACAN')
    #rm.logMessage(1,'File Result: ' + str(res))

    #Set Up Freq/Power
    sg.setFrequency('1017.0MHz')
    sg.setPower('-60dBm')
    sg.setModulationState("off")
    sg.setRF(state)
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('TACAN',10e6)
    time.sleep(5)
    sg.setModulationState(state)
    sg.setRF(state)

def readNauticalMiles(ARINC):
    """ Looks for signal of ~40nm """
    result = -1
    result = ARINC.getDistance_201()
    return result

def isTracking(rm,ARINC):
    """ Determines if signal is tracking ~40 nm"""
    # get nautical miles reading
    nMiles = readNauticalMiles(ARINC)
    rm.logMessage(0,"-------------------------------------------------------------------------")
    rm.logMessage(0,"DISTANCE: " + str(nMiles))
    return nMiles >= 39.83 and nMiles <= 40.17   # threshold of +/- .17

def findSignal(rm, signalLevel, sg, atc, ARINC):
    """" Determines the minimum RF signal in dBm that can find signal. Returns signal in nm if found, otherwise returns -1
         Takes in a starting signal level, SigGen, ATC, ARINC client, and boolean TACANS_on to determine if TACANS is running, and initial TACANS level"""
  
  
    while (isTracking(rm,ARINC)):   # Pass: found signal
        rm.logMessage(0,"*Test_189_2_2_8 - Found Signal. SignalLevel = " + str(signalLevel))
        
        signalLevel = signalLevel - 1

        #Set Power Lewvel to -100dBm 
        cmd = ':ATC:DME:POWER ' + str(signalLevel)
        atc.gwrite(cmd)
        #Set Reply Efficiency to 70%
        atc.gwrite(':ATC:DME:EFF 70')
        time.sleep(10)


    return signalLevel
   


##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_2_8(rm,atc,sg,ARINC,PathLoss):


    """ DO-189, Receiver Sensitivity: Sect 2.2.8"""
    
    rm.logMessage(2,"*** DO-189, Receiver Sensitivity: Sect 2.2.8 ***")

    #Initialize Results
    results = [-1.0,-1.0,-1.0,-1.0]
    iPwr = -75.0                         #initial ATC power level
    
    # Channel X
    rm.logMessage(1,"Channel 111.90")
    init_DME_Standard(rm, 0, atc, ARINC)
    time.sleep(5)
 
    # without TACANS
    rm.logMessage(1,"TACANS OFF")
    initSensitDMEStart(rm, atc, iPwr)
    results[0] = findSignal(rm, iPwr, sg, atc, ARINC)
    rm.logMessage(0,"ChnX Signal (w/o TACAN): " + str(results[0]))


    # with TACANS
    rm.logMessage(1,"TACANS ON")
    setupSigGen(rm, sg, 'ON')
    initSensitDMEStart(rm, atc, iPwr)
    results[1] = findSignal(rm, iPwr, sg, atc, ARINC)
    rm.logMessage(0,"ChnX Signal (w TACAN): " + str(results[1]))
    
    
    # Switch to Channel Y
    set_VOR_PAIR5(atc, ARINC, sg)
    rm.logMessage(1,"Channel 117.95")
    sg.setRF("OFF")

    # without TACANS
    rm.logMessage(1,"TACANS OFF")
    initSensitDMEStart(rm, atc, iPwr)
    results[2]= findSignal(rm, iPwr, sg, atc, ARINC)
    rm.logMessage(0,"ChnY Signal (w/o TACAN): " + str(results[2]))

    # with TACANS
    rm.logMessage(1,"TACANS ON")
    #Set Sig Gen Frequency and Power
    sg.setFrequency('1087.0MHz')
    sg.setPower('-60dBm')
    sg.setRF("ON")
    initSensitDMEStart(rm, atc, iPwr)
    results[3] = findSignal(rm, iPwr, sg, atc, ARINC)
    rm.logMessage(0,"ChnY Signal (w TACAN): " + str(results[3]))

    #Adjust Power Levels by RF PathLoss
    results[0] = results[0] - PathLoss
    results[1] = results[1] - PathLoss
    results[2] = results[2] - PathLoss
    results[3] = results[3] - PathLoss

    rm.logMessage(0,"Min Power Levels: " + str(results))

    #Trun off signal generators.
    sg.setRF("OFF")
    atc.gwrite(":ATC:DME:STOP")

    rm.logMessage(2,"Done,Closing Session.")

    return results



if __name__ == "__main__":
    #Initialize Intruuments
    rm = ate_rm()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)
	
    #Initialize Signal Generator
    sg = N5172BSigGen(rm)
    sg.Reset()
    #Load TACAN Waveform into SG (if not pre-loaded)
    #res = sg.downloadwaveform('TACAN')
    #rm.logMessage(1,'File Result: ' + str(res))


    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    
    PathLoss = 12.25

    Test_2_2_8(rm,atc,sg,ARINC,PathLoss)
    
    
    #Close the ATC and Signal Generator. 
    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    sg.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
