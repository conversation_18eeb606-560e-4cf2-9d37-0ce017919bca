#!/usr/bin/env python3
"""
Unit Tests for ARINC_Client Handler
Tests both original functionality and communication optimizations
"""

import unittest
import time
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_arinc429 import MockARIN<PERSON>429
from tests.mocks.mock_resource_manager import MockResourceManager


class TestARINCClient(unittest.TestCase):
    """Test cases for ARINC_Client handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = MockResourceManager()
        self.mock_arinc = MockARINC429()
        self.test_server_path = r"C:\Test\ARINC_Server"
        
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self, 'arinc_client'):
            try:
                self.arinc_client.close()
            except:
                pass
                
    def test_arinc_client_initialization(self):
        """Test ARINC client initialization"""
        # Test successful initialization
        client = self.mock_arinc
        self.assertIsNotNone(client)
        self.assertEqual(client.server_path, self.test_server_path)
        
    def test_arinc_connection_management(self):
        """Test ARINC connection establishment and teardown"""
        client = self.mock_arinc
        
        # Test connection
        result = client.connect()
        self.assertTrue(result)
        self.assertTrue(client.is_connected())
        
        # Test disconnection
        client.disconnect()
        self.assertFalse(client.is_connected())
        
    def test_arinc_data_transmission(self):
        """Test ARINC 429 data transmission"""
        client = self.mock_arinc
        client.connect()
        
        # Test label transmission
        test_label = 0x350
        test_data = 0x12345678
        
        result = client.transmit_label(test_label, test_data)
        self.assertTrue(result)
        
        # Verify data was queued for transmission
        transmitted_data = client.get_transmitted_data()
        self.assertIn((test_label, test_data), transmitted_data)
        
    def test_arinc_data_reception(self):
        """Test ARINC 429 data reception"""
        client = self.mock_arinc
        client.connect()
        
        # Simulate received data
        test_label = 0x350
        test_data = 0x87654321
        client.simulate_received_data(test_label, test_data)
        
        # Test data reception
        received_data = client.receive_label(test_label)
        self.assertEqual(received_data, test_data)
        
    def test_arinc_label_filtering(self):
        """Test ARINC label filtering functionality"""
        client = self.mock_arinc
        client.connect()
        
        # Set up label filter
        filter_labels = [0x350, 0x351, 0x352]
        client.set_label_filter(filter_labels)
        
        # Simulate data for filtered and non-filtered labels
        client.simulate_received_data(0x350, 0x12345678)  # Should be received
        client.simulate_received_data(0x360, 0x87654321)  # Should be filtered out
        
        # Test filtered reception
        filtered_data = client.get_filtered_data()
        self.assertEqual(len(filtered_data), 1)
        self.assertEqual(filtered_data[0][0], 0x350)
        
    def test_arinc_error_handling(self):
        """Test ARINC error handling and recovery"""
        client = self.mock_arinc
        
        # Test connection failure handling
        client.simulate_connection_error()
        result = client.connect()
        self.assertFalse(result)
        
        # Test recovery
        client.clear_connection_error()
        result = client.connect()
        self.assertTrue(result)
        
    def test_arinc_communication_timing(self):
        """Test ARINC communication timing and delays"""
        client = self.mock_arinc
        client.connect()
        
        # Test transmission timing
        start_time = time.time()
        client.transmit_label(0x350, 0x12345678)
        end_time = time.time()
        
        # Should complete quickly (mock implementation)
        self.assertLess(end_time - start_time, 0.1)
        
    def test_arinc_server_management(self):
        """Test ARINC server process management"""
        client = self.mock_arinc
        
        # Test server startup
        result = client.start_server()
        self.assertTrue(result)
        self.assertTrue(client.is_server_running())
        
        # Test server shutdown
        client.stop_server()
        self.assertFalse(client.is_server_running())
        
    def test_arinc_data_validation(self):
        """Test ARINC data validation and formatting"""
        client = self.mock_arinc
        
        # Test valid data
        valid_label = 0x350
        valid_data = 0x12345678
        self.assertTrue(client.validate_arinc_data(valid_label, valid_data))
        
        # Test invalid label (out of range)
        invalid_label = 0x400
        self.assertFalse(client.validate_arinc_data(invalid_label, valid_data))
        
        # Test invalid data (out of range)
        invalid_data = 0x100000000
        self.assertFalse(client.validate_arinc_data(valid_label, invalid_data))
        
    def test_arinc_loopback_functionality(self):
        """Test ARINC loopback testing capability"""
        client = self.mock_arinc
        client.connect()
        
        # Enable loopback mode
        client.enable_loopback()
        
        # Transmit data
        test_label = 0x350
        test_data = 0x12345678
        client.transmit_label(test_label, test_data)
        
        # Should receive the same data back
        received_data = client.receive_label(test_label)
        self.assertEqual(received_data, test_data)
        
        # Disable loopback
        client.disable_loopback()
        
    def test_arinc_performance_metrics(self):
        """Test ARINC performance monitoring"""
        client = self.mock_arinc
        client.connect()
        
        # Perform multiple transmissions
        for i in range(10):
            client.transmit_label(0x350 + i, 0x12345678 + i)
            
        # Check performance metrics
        metrics = client.get_performance_metrics()
        self.assertGreaterEqual(metrics['transmissions'], 10)
        self.assertGreater(metrics['throughput'], 0)
        
    def test_arinc_optimization_validation(self):
        """Test that MEDIUM priority communication optimizations work correctly"""
        client = self.mock_arinc
        client.connect()
        
        # Test optimized communication timing
        start_time = time.time()
        
        # Perform operations that would have used 1s delays (now 0.5s)
        for _ in range(5):
            client.transmit_label(0x350, 0x12345678)
            client.receive_label(0x350)
            
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 5 operations × 1s = 5s minimum
        # Optimized: 5 operations × 0.5s = 2.5s minimum
        self.assertLess(end_time - start_time, 4.0)  # Allow some margin


if __name__ == '__main__':
    unittest.main()
