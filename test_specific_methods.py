#!/usr/bin/env python3
"""
Test script to validate specific mock method implementations
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path to simulate mock mode
current_dir = Path.cwd()
mock_handlers_path = current_dir / "MockHandlers"
sys.path.insert(0, str(mock_handlers_path))

print("Testing specific mock method implementations...")

# Test D3054Scope.digiEdgePos
print("\n1. Testing D3054Scope.digiEdgePos:")
try:
    from TXDLib.Handlers.D3054Scope import D3054Scope
    scope = D3054Scope()
    result = scope.digiEdgePos(50/1000.0, source=1)
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result) if isinstance(result, (list, tuple)) else 'N/A'}")
    if isinstance(result, (list, tuple)) and len(result) == 2:
        print(f"Positive edges: {len(result[0])} items")
        print(f"Negative edges: {len(result[1])} items")
        print("SUCCESS: digiEdgePos returns proper tuple")
    else:
        print(f"FAILED: Expected tuple, got {type(result)}")
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()

# Test ARINC_Client.TCAS_Decode
print("\n2. Testing ARINC_Client.TCAS_Decode:")
try:
    from TXDLib.Handlers.ARINC_Client import ARINC_Client
    arinc = ARINC_Client()
    result = arinc.TCAS_Decode("test_data")
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result) if isinstance(result, (list, tuple)) else 'N/A'}")
    if isinstance(result, (list, tuple)) and len(result) == 4:
        print(f"Intruders: {len(result[0])} items")
        print(f"Ranges: {len(result[1])} items") 
        print(f"Altitudes: {len(result[2])} items")
        print(f"Bearings: {len(result[3])} items")
        print("SUCCESS: TCAS_Decode returns proper tuple")
    else:
        print(f"FAILED: Expected 4-tuple, got {type(result)}")
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()

# Test ate_rm instantiation
print("\n3. Testing ate_rm instantiation:")
try:
    from TXDLib.Handlers import ate_rm
    rm = ate_rm()
    print(f"rm type: {type(rm)}")
    rm.logMessage(1, "Test message")
    print("SUCCESS: ate_rm instantiation and method call working")
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()
