{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:19:52.716742", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0, "success_rate": 18.181818181818183, "total_execution_time": 2.468963146209717, "start_time": "2025-06-05T14:19:50.246796", "end_time": "2025-06-05T14:19:52.715759"}, "sequence_results": [{"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.20140576362609863, "start_time": "2025-06-05T14:19:50.247861", "end_time": "2025-06-05T14:19:50.449269", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.21859478950500488, "start_time": "2025-06-05T14:19:50.450570", "end_time": "2025-06-05T14:19:50.669161", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 222, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.21408963203430176, "start_time": "2025-06-05T14:19:50.670467", "end_time": "2025-06-05T14:19:50.884558", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 215, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.3886535167694092, "start_time": "2025-06-05T14:19:50.885448", "end_time": "2025-06-05T14:19:51.274102", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 227, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.21648597717285156, "start_time": "2025-06-05T14:19:51.275555", "end_time": "2025-06-05T14:19:51.492041", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 292, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22005176544189453, "start_time": "2025-06-05T14:19:51.493653", "end_time": "2025-06-05T14:19:51.713688", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 232, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.15059185028076172, "start_time": "2025-06-05T14:19:51.715016", "end_time": "2025-06-05T14:19:51.865608", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20973849296569824, "start_time": "2025-06-05T14:19:51.866523", "end_time": "2025-06-05T14:19:52.076263", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 186, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22158527374267578, "start_time": "2025-06-05T14:19:52.077960", "end_time": "2025-06-05T14:19:52.299545", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 229, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20844030380249023, "start_time": "2025-06-05T14:19:52.300751", "end_time": "2025-06-05T14:19:52.509192", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 223, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20460939407348633, "start_time": "2025-06-05T14:19:52.510354", "end_time": "2025-06-05T14:19:52.714964", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 173, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO385": {"total": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 2.468963146209717, "average_sequence_time": 0.22445119510997424, "sequences_per_hour": 16039.121548166004, "optimization_effectiveness": {"optimization_success_rate": 18.181818181818183, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 9, "failure_by_procedure": {"DO385": 9}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 9 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}