# TXD Qualification Test System - System Test Report

**Generated**: 2025-06-04 22:25:56
**Execution Mode**: MOCK
**Report Version**: 1.0.0

## Executive Summary: ❌ FAILED

| Metric | Value |
|--------|-------|
| Total Sequences | 73 |
| Passed | 4 |
| Failed | 69 |
| Errors | 0 |
| Timeouts | 0 |
| Success Rate | 5.5% |
| Total Execution Time | 12.9 seconds |

## Procedure Breakdown

### ❌ DO181
- **Total Sequences**: 35
- **Passed**: 0
- **Failed**: 35
- **Success Rate**: 0.0%

### ❌ DO189
- **Total Sequences**: 9
- **Passed**: 0
- **Failed**: 9
- **Success Rate**: 0.0%

### ❌ DO282
- **Total Sequences**: 8
- **Passed**: 3
- **Failed**: 5
- **Success Rate**: 37.5%

### ❌ DO385
- **Total Sequences**: 11
- **Passed**: 1
- **Failed**: 10
- **Success Rate**: 9.1%

### ❌ FAR43
- **Total Sequences**: 10
- **Passed**: 0
- **Failed**: 10
- **Success Rate**: 0.0%

## Performance Metrics

- **Total Execution Time**: 12.9 seconds
- **Average Sequence Time**: 0.2 seconds
- **Sequences Per Hour**: 20445.9
- **Optimization Status**: ACTIVE

## Recommendations

1. Review failed sequences and address root causes before production use
2. Investigate 69 failed sequences
3. Verify hardware connections before live mode execution
4. Run mock mode tests regularly to validate sequence logic
5. Monitor execution times for performance regression
6. Keep system test reports for compliance documentation

## Individual Sequence Results

### ❌ DO181/DO_181E_2_3_2_10_Step1a.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step1a.py", line 47, in...

### ❌ DO181/DO_181E_2_3_2_10_Step1a_11-14-23.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step1a_11-14-23.py", li...

### ❌ DO181/DO_181E_2_3_2_10_Step1b.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step1b.py", line 46, in...

### ❌ DO181/DO_181E_2_3_2_10_Step1c.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step1c.py", line 46, in...

### ❌ DO181/DO_181E_2_3_2_10_Step2a.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step2a.py", line 56, in...

### ❌ DO181/DO_181E_2_3_2_10_Step2a_11-14-23.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step2a_11-14-23.py", li...

### ❌ DO181/DO_181E_2_3_2_10_Step2b.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step2b.py", line 56, in...

### ❌ DO181/DO_181E_2_3_2_10_Step3.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_10_Step3.py", line 46, in ...

### ❌ DO181/DO_181E_2_3_2_12.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_12.py", line 32, in <modul...

### ❌ DO181/DO_181E_2_3_2_1_step1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step1.py", line 35, in <...

### ❌ DO181/DO_181E_2_3_2_1_step2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step2.py", line 33, in <...

### ❌ DO181/DO_181E_2_3_2_1_step3.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step3.py", line 33, in <...

### ❌ DO181/DO_181E_2_3_2_1_step4.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step4.py", line 32, in <...

### ❌ DO181/DO_181E_2_3_2_1_step5.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step5.py", line 32, in <...

### ❌ DO181/DO_181E_2_3_2_1_step6.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step6.py", line 32, in <...

### ❌ DO181/DO_181E_2_3_2_1_step7.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_1_step7.py", line 32, in <...

### ❌ DO181/DO_181E_2_3_2_2_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_2_1.py", line 32, in <modu...

### ❌ DO181/DO_181E_2_3_2_2_2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_2_2.py", line 45, in <modu...

### ❌ DO181/DO_181E_2_3_2_2_2_11-14-23.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_2_2_11-14-23.py", line 45,...

### ❌ DO181/DO_181E_2_3_2_3_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_1.py", line 50, in <modu...

### ❌ DO181/DO_181E_2_3_2_3_1_old.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_1_old.py", line 48, in <...

### ❌ DO181/DO_181E_2_3_2_3_2a.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_2a.py", line 55, in <mod...

### ❌ DO181/DO_181E_2_3_2_3_2a_old.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_2a_old.py", line 53, in ...

### ❌ DO181/DO_181E_2_3_2_3_2b.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_2b.py", line 38, in <mod...

### ❌ DO181/DO_181E_2_3_2_3_2b_old.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_3_2b_old.py", line 38, in ...

### ❌ DO181/DO_181E_2_3_2_4.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_4.py", line 48, in <module...

### ❌ DO181/DO_181E_2_3_2_5_Step1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step1.py", line 38, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step2.py", line 35, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step3.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step3.py", line 37, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step4.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step4.py", line 33, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step5.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step5.py", line 38, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step6.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step6.py", line 39, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step7.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step7.py", line 37, in <...

### ❌ DO181/DO_181E_2_3_2_5_Step8.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_5_Step8.py", line 36, in <...

### ❌ DO181/DO_181E_2_3_2_8.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO181\DO_181E_2_3_2_8.py", line 32, in <module...

### ❌ DO189/DO_189_2_2_10.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_10.py", line 101, in <module>...

### ❌ DO189/DO_189_2_2_12.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_12.py", line 52, in <module>
...

### ❌ DO189/DO_189_2_2_1_b.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_1_b.py", line 32, in <module>...

### ❌ DO189/DO_189_2_2_3.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_3.py", line 45, in <module>
 ...

### ❌ DO189/DO_189_2_2_4.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_4.py", line 36, in <module>
 ...

### ❌ DO189/DO_189_2_2_6.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_6.py", line 33, in <module>
 ...

### ❌ DO189/DO_189_2_2_7.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_7.py", line 51, in <module>
 ...

### ❌ DO189/DO_189_2_2_8.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_2_2_8.py", line 38, in <module>
 ...

### ❌ DO189/DO_189_DME_SG_Load.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO189\DO_189_DME_SG_Load.py", line 28, in <mod...

### ❌ DO282/DO282_248211.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO282\DO282_248211.py", line 41, in <module>
 ...

### ❌ DO282/DO282_248212.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO282\DO282_248212.py", line 41, in <module>
 ...

### ❌ DO282/DO282_248213.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO282\DO282_248213.py", line 42, in <module>
 ...

### ❌ DO282/DO282_24822.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO282\DO282_24822.py", line 40, in <module>
  ...

### ❌ DO282/DO282_24823.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO282\DO282_24823.py", line 35, in <module>
  ...

### ✅ DO282/FEC.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ✅ DO282/UAT_CONNECTION.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ✅ DO282/reedsolo.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ❌ DO385/DO385_2_2_3_3.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_3_3.py", line 28, in <module>
...

### ❌ DO385/DO385_2_2_3_5.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_3_5.py", line 33, in <module>
...

### ❌ DO385/DO385_2_2_3_8.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_3_8.py", line 51, in <module>
...

### ❌ DO385/DO385_2_2_4_4_1_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_4_1_1.py", line 53, in <modu...

### ❌ DO385/DO385_2_2_4_4_2_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_4_2_1.py", line 70, in <modu...

### ❌ DO385/DO385_2_2_4_4_2_2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_4_2_2.py", line 56, in <modu...

### ✅ DO385/DO385_2_2_4_5_4_1.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ❌ DO385/DO385_2_2_4_6_2_1_2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_6_2_1_2.py", line 41, in <mo...

### ❌ DO385/DO385_2_2_4_6_2_2_2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_6_2_2_2.py", line 55, in <mo...

### ❌ DO385/DO385_2_2_4_6_4_2.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_2_4_6_4_2.py", line 43, in <modu...

### ❌ DO385/DO385_2_3_3_1.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\DO385\DO385_2_3_3_1.py", line 43, in <module>
...

### ❌ FAR43/FAR43_A_Frequency.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_A_Frequency.py", line 32, in <modu...

### ❌ FAR43/FAR43_B_Supression.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_B_Supression.py", line 42, in <mod...

### ❌ FAR43/FAR43_C_Sensitivity.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_C_Sensitivity.py", line 47, in <mo...

### ❌ FAR43/FAR43_D_Power.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_D_Power.py", line 55, in <module>
...

### ❌ FAR43/FAR43_E_Diversity.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_E_Diversity.py", line 42, in <modu...

### ❌ FAR43/FAR43_F_ModeSAddress.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_F_ModeSAddress.py", line 37, in <m...

### ❌ FAR43/FAR43_G_ModeSFormat.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_G_ModeSFormat.py", line 62, in <mo...

### ❌ FAR43/FAR43_H_ModeSAllCall.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_H_ModeSAllCall.py", line 43, in <m...

### ❌ FAR43/FAR43_I_ATCRBSOnly.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_I_ATCRBSOnly.py", line 33, in <mod...

### ❌ FAR43/FAR43_J_Squitter.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "Procedures\FAR43\FAR43_J_Squitter.py", line 42, in <modul...

