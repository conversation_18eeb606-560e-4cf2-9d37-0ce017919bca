#!/usr/bin/env python3
"""
TXD Qualification Test System - Sequence Runner
Core sequence execution engine for running test procedures
"""

import os
import sys
import time
import subprocess
import importlib.util
from datetime import datetime
from pathlib import Path
# from typing import List, Dict, Any, Optional  # Not available in Python 3.4


class SequenceRunner:
    """Core sequence execution engine"""
    
    def __init__(self, mode='mock'):
        """
        Initialize sequence runner
        
        Args:
            mode: 'mock' or 'live' execution mode
        """
        self.mode = mode
        self.procedures_dir = Path("Procedures")
        self.results = []
        self.start_time = None
        self.end_time = None
        self.current_sequence = None
        
    def discover_sequences(self, procedures=None):
        """
        Discover all available test sequences
        
        Args:
            procedures: List of specific procedures to include (e.g., ['DO282', 'FAR43'])
                       If None, discovers all procedures
        
        Returns:
            Dictionary mapping procedure names to list of sequence files
        """
        sequences = {}
        
        # Define all available procedure directories
        all_procedures = ["DO181", "DO189", "DO282", "DO385", "FAR43"]
        
        # Use specified procedures or all procedures
        target_procedures = procedures if procedures else all_procedures
        
        for proc_name in target_procedures:
            proc_path = self.procedures_dir / proc_name
            if proc_path.exists() and proc_path.is_dir():
                # Find all Python files in the procedure directory
                py_files = [f.name for f in proc_path.glob("*.py") 
                           if not f.name.startswith('__')]
                if py_files:
                    sequences[proc_name] = sorted(py_files)
                    
        return sequences
    
    def execute_sequence(self, procedure, sequence_file):
        """
        Execute a single test sequence
        
        Args:
            procedure: Procedure name (e.g., 'DO282')
            sequence_file: Sequence file name (e.g., 'DO282_24823.py')
        
        Returns:
            Dictionary containing execution results
        """
        sequence_path = self.procedures_dir / procedure / sequence_file
        
        if not sequence_path.exists():
            return {
                'procedure': procedure,
                'sequence': sequence_file,
                'status': 'ERROR',
                'error': 'Sequence file not found: {0}'.format(sequence_path),
                'execution_time': 0,
                'start_time': datetime.now().isoformat(),
                'end_time': datetime.now().isoformat()
            }
        
        print("  Executing: {0}/{1}".format(procedure, sequence_file))

        start_time = time.time()
        start_timestamp = datetime.now()
        
        try:
            # Set environment variable to indicate execution mode
            env = os.environ.copy()
            env['TXD_EXECUTION_MODE'] = self.mode.upper()
            
            # Execute the sequence
            result = subprocess.run(
                [sys.executable, str(sequence_path)],
                capture_output=True,
                text=True,
                timeout=3600,  # 1 hour timeout
                env=env
            )
            
            end_time = time.time()
            end_timestamp = datetime.now()
            execution_time = end_time - start_time
            
            # Determine status
            if result.returncode == 0:
                status = 'PASSED'
            else:
                status = 'FAILED'
            
            return {
                'procedure': procedure,
                'sequence': sequence_file,
                'status': status,
                'return_code': result.returncode,
                'execution_time': execution_time,
                'start_time': start_timestamp.isoformat(),
                'end_time': end_timestamp.isoformat(),
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            end_timestamp = datetime.now()
            return {
                'procedure': procedure,
                'sequence': sequence_file,
                'status': 'TIMEOUT',
                'return_code': -1,
                'execution_time': 3600,
                'start_time': start_timestamp.isoformat(),
                'end_time': end_timestamp.isoformat(),
                'stdout': '',
                'stderr': 'Sequence timed out after 1 hour'
            }
            
        except Exception as e:
            end_timestamp = datetime.now()
            return {
                'procedure': procedure,
                'sequence': sequence_file,
                'status': 'ERROR',
                'return_code': -1,
                'execution_time': time.time() - start_time,
                'start_time': start_timestamp.isoformat(),
                'end_time': end_timestamp.isoformat(),
                'stdout': '',
                'stderr': str(e)
            }
    
    def execute_all_sequences(self, procedures=None):
        """
        Execute all discovered sequences
        
        Args:
            procedures: List of specific procedures to execute
        
        Returns:
            List of execution results for all sequences
        """
        print("Starting System Test execution in {0} mode".format(self.mode.upper()))
        print("=" * 70)

        self.start_time = time.time()
        self.results = []

        # Discover sequences
        sequences = self.discover_sequences(procedures)

        if not sequences:
            print("No sequences found to execute")
            return []

        total_sequences = sum(len(seq_list) for seq_list in sequences.values())
        print("Found {0} sequences across {1} procedures".format(total_sequences, len(sequences)))
        
        # Execute all sequences
        sequence_count = 0
        for procedure, sequence_files in sequences.items():
            print("\nExecuting {0} procedures ({1} sequences):".format(procedure, len(sequence_files)))

            for sequence_file in sequence_files:
                sequence_count += 1
                print("  [{0}/{1}] {2}/{3}".format(sequence_count, total_sequences, procedure, sequence_file))

                result = self.execute_sequence(procedure, sequence_file)
                self.results.append(result)

                # Print immediate status
                status_icon = "✅" if result['status'] == 'PASSED' else "❌"
                print("    {0} {1} ({2:.1f}s)".format(status_icon, result['status'], result['execution_time']))

                if result['status'] in ['FAILED', 'ERROR', 'TIMEOUT']:
                    print("    Error: {0}".format(result.get('stderr', 'Unknown error')))
        
        self.end_time = time.time()
        
        return self.results
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """
        Get summary of execution results
        
        Returns:
            Dictionary containing execution summary
        """
        if not self.results:
            return {}
        
        total_sequences = len(self.results)
        passed = len([r for r in self.results if r['status'] == 'PASSED'])
        failed = len([r for r in self.results if r['status'] == 'FAILED'])
        errors = len([r for r in self.results if r['status'] == 'ERROR'])
        timeouts = len([r for r in self.results if r['status'] == 'TIMEOUT'])
        
        total_execution_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        return {
            'execution_mode': self.mode,
            'total_sequences': total_sequences,
            'passed': passed,
            'failed': failed,
            'errors': errors,
            'timeouts': timeouts,
            'success_rate': (passed / total_sequences * 100) if total_sequences > 0 else 0,
            'total_execution_time': total_execution_time,
            'start_time': datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
            'end_time': datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None
        }
