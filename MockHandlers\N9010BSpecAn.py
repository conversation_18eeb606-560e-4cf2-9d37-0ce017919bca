"""
Mock implementation of N9010BSpecAn.py - Spectrum Analyzer
Provides mock spectrum analyzer functionality without hardware dependencies
"""

import time
import random
import math
from typing import List, Tuple


class N9010BSpecAn:
    def __init__(self, rm):
        self.resourceManager = rm
        self.connected = True
        self.center_freq = 1000e6  # 1 GHz default
        self.span = 100e6  # 100 MHz default
        self.ref_level = 0  # 0 dBm default
        self.points = 1001  # Default number of points
        
        # Mock successful connection
        self.resourceManager.logMessage(0, "Mock Spectrum Analyzer Connection Success")

    def basicWrite(self, cmd: str):
        """Mock basic write command"""
        time.sleep(0.001)  # Minimal delay
        self.resourceManager.logMessage(1, f"[MOCK SPEC] > {cmd}")
        
        # Parse some commands to maintain state
        if ":FREQ:CENT" in cmd.upper():
            try:
                freq_str = cmd.split()[-1]
                self.center_freq = float(freq_str.replace('MHZ', 'e6').replace('GHZ', 'e9').replace('KHZ', 'e3'))
            except:
                pass
        elif ":FREQ:SPAN" in cmd.upper():
            try:
                span_str = cmd.split()[-1]
                self.span = float(span_str.replace('MHZ', 'e6').replace('GHZ', 'e9').replace('KHZ', 'e3'))
            except:
                pass
        elif ":DISP:WIND:TRAC:Y:RLEV" in cmd.upper():
            try:
                ref_str = cmd.split()[-1]
                self.ref_level = float(ref_str.replace('DBM', ''))
            except:
                pass

    def basicQuery(self, cmd: str, logEnable: bool = False) -> str:
        """Mock basic query command with realistic responses"""
        time.sleep(0.002)  # Minimal delay
        
        # Generate realistic responses
        if "*IDN?" in cmd.upper():
            resp = "Agilent Technologies,N9010B,MY12345678,A.13.14"
        elif ":FREQ:CENT?" in cmd.upper():
            resp = str(self.center_freq)
        elif ":FREQ:SPAN?" in cmd.upper():
            resp = str(self.span)
        elif ":CALC:MARK1:Y?" in cmd.upper() or ":CALCULATE:MARKER1:Y?" in cmd.upper():
            # Mock marker power reading
            resp = str(self.ref_level - 10 + random.uniform(-5, 5))
        elif ":CALC:MARK1:X?" in cmd.upper() or ":CALCULATE:MARKER1:X?" in cmd.upper():
            # Mock marker frequency reading - return realistic 1030 MHz value
            resp = str(1030e6 + random.uniform(-10e3, 10e3))
        elif ":CALC:LLINE1:FAIL?" in cmd.upper():
            # Mock limit line result (mostly pass)
            resp = "0" if random.random() > 0.1 else "1"
        elif "FREQ" in cmd.upper() and "?" in cmd:
            # Any frequency query
            resp = str(self.center_freq)
        elif "POWER" in cmd.upper() and "?" in cmd:
            # Any power query
            resp = str(self.ref_level + random.uniform(-5, 5))
        else:
            resp = "OK"
            
        if logEnable:
            self.resourceManager.logMessage(1, f"[MOCK SPEC] < {resp}")
        return resp

    def basicTvlMsg(self, tvlTxt: str):
        self.resourceManager.logMessage(1, tvlTxt)
    
    def Ident(self) -> str:
        return self.basicQuery('*IDN?')

    def Reset(self):
        """Mock spectrum analyzer reset"""
        self.basicWrite('*RST')
        self.resourceManager.logMessage(1, "Mock SpecAnz Resetting...")
        time.sleep(1.5)  # Reduced from 5s - optimized reset delay
        self.resourceManager.logMessage(1, "Mock SpecAnz complete\n")
        
    def close(self):
        """Mock spectrum analyzer close"""
        self.connected = False

    # Frequency and span commands
    def CenterFreqSet(self, freq: float, units: str):
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(f':FREQuency:CENTer {freq} {units}')
        else:
            self.resourceManager.logMessage(3, f"Invalid frequency units ({units})")

    def CenterFreqRead(self):
        return self.basicQuery(':FREQuency:CENTer?')

    def ResBandwidthSet(self, freq: float, units: str):
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(f':BANDwidth:RESolution {freq} {units}')
        else:
            self.resourceManager.logMessage(3, f"Invalid bandwidth units ({units})")

    def VidBandwidthSet(self, freq: float, units: str):
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(f':BANDwidth:VIDeo {freq} {units}')
        else:
            self.resourceManager.logMessage(3, f"Invalid bandwidth units ({units})")

    def SpanSet(self, freq: float, units: str):
        if units.lower() in ["", "hz", "khz", "mhz", "ghz"]:
            self.basicWrite(f':FREQuency:SPAN {freq} {units}')
        else:
            self.resourceManager.logMessage(3, f"Invalid span units ({units})")

    def SweepTimeSet(self, sec: float, units: str):
        if units.lower() in ["", "s", "ms", "us", "ns"]:
            self.basicWrite(f'SWE:TIME {sec} {units}')
        else:
            self.resourceManager.logMessage(3, f"Invalid time units ({units})")

    def SweepContSet(self, cont: str):
        self.basicWrite(f':INITiate:CONTinuous {cont}')

    def sweepNumPoints(self, points: int):
        self.points = points
        self.basicWrite(f":SENSe:SWEep:POINts {points}")

    def TraceTypeSet(self, traceType: str):
        if traceType.lower() in ["write", "average", "maxhold", "minhold"]:
            self.basicWrite(f':TRAC:TYPE {traceType}')
        else:
            self.resourceManager.logMessage(3, f"Invalid trace type ({traceType})")

    def setDetector(self, mode: str):
        valid_modes = ["AVER", "AVERAGE", "NEG", "NEGATIVE", "NORM", "NORMAL",
                      "POS", "POSITIVE", "SAMP", "SAMPLE", "QPE", "QPEAK", 
                      "EAV", "EAVERAGE", "RAV", "RAVERAGE"]
        if mode.upper() in valid_modes:
            self.basicWrite(f":DETector:TRACe {mode}")
        else:
            self.resourceManager.logMessage(3, f"Invalid detector mode ({mode})")

    def setRefLevel(self, ref_lvl: float, unit: str):
        self.basicWrite(f":DISPlay:WINDow:TRACe:Y:RLEVel {ref_lvl} {unit}")

    def setDisplayLine(self, lineLevel: float):
        self.basicWrite(f":DISPlay:WINDow:TRACe:Y:DLINe {lineLevel}")

    def enableDisplayLine(self):
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:DLINe:STATe ON")

    def disableDisplayLine(self):
        self.basicWrite(":DISPlay:WINDow:TRACe:Y:DLINe:STATe OFF")

    # Marker functions
    def MarkerSet(self, num: int, state: str):
        self.basicWrite(f':CALCULATE:MARKER{num}:STATE {state}\n')

    def MarkerMode(self, mode: str):
        valid_modes = ["POS", "POSITION", "DELT", "DELTA", "FIX", "FIXED", "OFF"]
        if str(mode).upper() in valid_modes:
            self.basicWrite(f"CALCulate:MARKer1:MODE {mode}")
        else:
            self.resourceManager.logMessage(3, f"Invalid marker mode ({mode})")

    def GetMaxPeakPower(self) -> str:
        """Mock max peak power measurement"""
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(0.01)  # Reduced from 1s
        self.basicWrite(':CALCULATE:MARKER1:MAX\n')
        time.sleep(0.01)  # Reduced from 1s
        # Return a realistic power value
        power = self.ref_level - 10 + random.uniform(-5, 5)
        return str(power)

    def GetMaxPeakFreq(self) -> str:
        """Mock max peak frequency measurement"""
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(0.01)  # Reduced from 1s
        self.basicWrite(':CALCULATE:MARKER1:MAX\n')
        time.sleep(0.01)  # Reduced from 1s
        # Return a realistic frequency value near 1030 MHz
        freq = 1030e6 + random.uniform(-10e3, 10e3)  # 1030 MHz ± 10 kHz
        return str(freq)

    def BandwidthPowerCalc(self, centfreq: float, centunits: str, span: float, spanunits: str) -> str:
        """Mock bandwidth power calculation"""
        self.basicWrite(":CALCULATE:MARKER1:STATE ON\n")
        time.sleep(0.01)  # Reduced from 1s
        self.basicWrite(":CALCULATE:MARKER1:MODE POS\n")
        time.sleep(0.01)
        self.basicWrite(f'CALCULATE:MARKER1:X:CENTER {centfreq} {centunits}')
        time.sleep(0.01)
        self.basicWrite(f'CALCULATE:MARKER1:X:SPAN {span} {spanunits}')
        time.sleep(0.01)
        self.basicWrite(":CALCULATE:MARKER1:FUNCTION BPOWER\n")
        time.sleep(0.01)
        return self.basicQuery(':CALCULATE:MARKER1:Y?')

    def setMkrRefLevel(self, mkr: int):
        """Mock marker reference level setting"""
        ref_pwr = float(self.basicQuery(f":CALCULATE:MARKER{mkr}:Y?"))
        self.setRefLevel(ref_pwr, "")

    def LimitResult(self) -> bool:
        """Mock limit line result"""
        res = int(self.basicQuery(":CALCUlate:LLINE1:FAIL?"))
        return res == 0

    def LimitSetLine(self, data_list: List[str]):
        """Mock limit line programming"""
        self.basicWrite("CALCulate:LLINE1:FREQuency:CMODe:RELative 1")
        self.basicWrite("CALCulate:LLINe1:AMPLitude:CMODe:RELative 1")
        format_str = ", ".join(data_list)
        self.basicWrite(f":CALCULate:LLINE1:DATA {format_str}")

    def LimitSetState(self, state: int):
        self.basicWrite(f"CALCulate:LLINe:STATe {state}")

    def disableAverage(self):
        self.basicWrite(":AVERage:STATe OFF")

    def enableAverage(self):
        self.basicWrite(":AVERage:STATe ON")

    def setAttenuation(self, val: float):
        self.basicWrite(f":POWer:RF:ATTenuation {val}")

    def setExternalTrigger(self):
        self.basicWrite(":TRIGger:SEQuence:SOURCe EXTernal")

    def setExternalTriggerSource(self, slope: str):
        if slope.upper() in ["POS", "POSITIVE", "NEG", "NEGATIVE"]:
            self.basicWrite(f"TRIGger:SEQuence:SLOPe {slope}")
        else:
            self.resourceManager.logMessage(3, f"Invalid trigger slope ({slope})")

    def setExternalTriggerDelay(self, time_val: float):
        self.basicWrite(f"TRIGger:SEQuence:DELay {time_val}")

    def loadLimit(self, trace_csv: str):
        """Mock limit line loading"""
        self.basicWrite(f':MMEMory:LOAD:LIMit LLINE1, "{trace_csv}"')

    def dataSpecRead(self) -> Tuple[List[float], List[float]]:
        """Mock spectrum data read with realistic data"""
        return self._generate_mock_spectrum_data()

    def dataSpecFetch(self) -> Tuple[List[float], List[float]]:
        """Mock spectrum data fetch with realistic data"""
        return self._generate_mock_spectrum_data()

    def _generate_mock_spectrum_data(self) -> Tuple[List[float], List[float]]:
        """Generate realistic mock spectrum data"""
        freq = []
        pwr = []

        start_freq = self.center_freq - self.span/2
        freq_step = self.span / (self.points - 1)

        for i in range(self.points):
            f = start_freq + i * freq_step
            # Generate realistic spectrum with noise floor and some peaks
            p = self.ref_level - 60 + random.uniform(-5, 5)  # Noise floor

            # Add some peaks for realism
            if abs(f - self.center_freq) < self.span/10:
                p += 40 + random.uniform(-3, 3)  # Center peak

            freq.append(f)
            pwr.append(p)

        return freq, pwr

    def plotSpecAn(self, freq: List[float], pwr: List[float]):
        """Mock plotting function"""
        self.resourceManager.logMessage(1, "Mock spectrum plot generated")

    def plotSave(self, file_name: str, freq: List[float], pwr: List[float]):
        """Mock plot save function"""
        self.resourceManager.logMessage(1, f"Mock spectrum plot saved as {file_name}.png")
