# -*- coding: utf-8 -*-
"""
Created on Tue Jan  6 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 G Mode S Format requiremensts.
             
             Mode S Formats: Interrogate the Mode S transponder with uplink 
             formats (UF) for which it is equipped and verify that the replies 
             are made in the correct format. Use the surveillance formats 
             UF = 4 and 5. Verify that the altitude reported in the replies
             to UF = 4 are the same as that reported in a valid ATCRBS 
             Mode C reply. Verify that the identity reported in the replies 
             to UF = 5 are the same as that reported in a valid ATCRBS 
             Mode 3/A reply. If the transponder is so equipped, use the 
             communication formats UF = 20, 21, and 24.    
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     DF4_Msg_pr  = % reply
             DF5_Msg_pr  = % reply
             DF20_Msg_pr = % reply
             DF21_Msg_pr = % reply
             ModeA_Ident = ModeA Squak Code
             ModeC_Alt   = ModeC Altitude
             DF4_Alt     = decoded DF4 Altitude
             DF20_Alt    = decoded DF20 Altitude
             DF5_Squawk  = decoded DF5 Squak
             DF21_Squawk = decoded DF20 Squak
             DF4_ICAO = ICAO Address in DF4 Msg
             DF5_ICAO = ICAO Address in DF5 Msg
             --Temp Output files:
                FAR43_G_ModeA_out_log -- log file for ATCRBS ModeA
                FAR43_G_ModeC_out_log -- log file for ATCRBS ModeC
                FAR43_G_DF4_out.log -- log file of DF4 Messages
                FAR43_G_DF5_out.log -- Log file of DF5 Messages
                FAR43_G_DF20_out.log -- log file of DF20 Messages
                FAR43_G_DF21_out.log -- Log file of DF21 Messages
             
             NOTEs: 
                 1) Step1: Interrogations are DF4 messages (with Address = 000004), Replies DF4
                 2) Step2: Interrogations are DF5 messages (with Address = 000004), Replies DF5
                 3) Step3: Interrogations are DF4 messages (with Address = 000004, RR=16), Replies DF20 (112bit)
                 4) Step4: Interrogations are DF5 messages (with Address = 000004, RR=16), Replies DF21 (112bit)
                 5) Step2: Interrogations are UF24 messages (with Address = 000004)
                              

HISTORY:
01/06/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time
import os

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import ATC_RGS_Log_Decode


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

def getModeReply(atc,mode):
    """ Gets the ModeA or ModeC reply code from the ATC.  Based on "mode", interrogations are
    sent to the XPDR and the Reply Code is returned.   """

    if (mode == 'A'):
        #Set Up Transponder -- MODE A
        atc.transponderModeA()
    else:
        #Set Up Transponder -- MODE C
        atc.transponderModeC()

    #Lower the PRF
    atc.gwrite(":ATC:XPDR:PRF 40")       #Pulse Repatition
              
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    

    #Get Mode Reply
    if (mode == 'A'):
        ModeReply = atc.getModeAReply(2) 
    else:
        ModeReply = atc.getModeCReply(2)  

    #Repeat 10 times 
    count = 0
    while (ModeReply == 20 or ModeReply == -1.0) and count < 10:
        time.sleep(10)
        if (mode == 'A'):
           ModeReply = atc.getModeAReply(2) 
        else:
           ModeReply = atc.getModeCReply(2)
        count = count + 1 
        print ("Count: ",count)
      
    #Turn Off RF    
    atc.gwrite(":ATC:XPDR:RF OFF")


    print ("ModeReply: ",ModeReply)

    return ModeReply


def log_Mode_ATCRBS(rm,atc,mode):
    """ This routine logs Mode A or Mode C data to a log file. """

    rm.logMessage(1,"Processing ModeA or ModeC log file.")

    #Set Up Transponder -- MODE A or MODE C
    if mode == 'ModeA':
        atc.transponderModeA()
    else:
        atc.transponderModeC()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna

    #Lower the PRF
    atc.gwrite(":ATC:XPDR:PRF 100")       #Pulse Repatition,lower rate than normal, for fewer messages
              
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    atc.waitforstatus()    

    #start data logging
    atc.data_log_start()

    #capture at least two seconds of data
    time.sleep(2)

    #stop recording and download data, log file generated for verification.
    if mode == 'ModeA':
        atc.data_log_stop("FAR43_G_ModeA.log")
    else:
        atc.data_log_stop("FAR43_G_ModeC.log")
    time.sleep(15)     #time to download log           
   
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
        
    #decode the data
    if mode == 'ModeA':
        ATC_RGS_Log_Decode.main("FAR43_G_ModeA.log")
    else:
        ATC_RGS_Log_Decode.main("FAR43_G_ModeC.log")
    time.sleep(2)

    return


def post_process_atcrbs(rm,mode):
    """ This function post processes the ATCRBS Log files, and returns
    either the Squawk code or Altitude depending on 'mode'.  """
    
    rm.logMessage(1,"PostProcessing ModeA or ModeC log file.")

    if mode == 'ModeA':
       fname = 'FAR43_G_ModeAout.log'
    else:
       fname = 'FAR43_G_ModeCout.log'
   
    #open the processed log file
    try:
        fn = open(fname,'r')
    except IOError:
        rm.logMessage(3,"Post Processing Second ATCRBS File: ERROR File Not Found.")
        return -1

    #Read In lines
    res  = '-1'
    for line in fn:
        #print(line)
        if mode == 'ModeA':
            idx = line.find('Swk: ')
            res = line[idx+5: idx+10]
            #print("AIDx: %d, res: %s",idx,res)
        else:
            idx = line.find('Alt: ')
            res = line[idx+5: idx+10]
            if res == 'None ':
                res = '-1300'
            #print("CIDx: %d, res: %s",idx,res)

        #quit early if string found
        if idx > 0:
            break

    #Close the file
    fn.close()

    return int(res)

def post_process_modes(rm,mode):
    """ This routine post-processes ModeS log files for DF4,DF5,DF20 and DF21 messages.
    Returns Squawk or Altitude and ICAO depending on message type """

    rm.logMessage(1,"PostProcessing ModeS log file.")
    dfparam = -1     #squawk or altitude
    icao = -1        #ICAO

    #filename
    if mode == 'DF4':
        fname = 'FAR43_G_DF4out.log'
    elif mode == 'DF5':
        fname = 'FAR43_G_DF5out.log'
    elif mode == 'DF20':
        fname = 'FAR43_G_DF20out.log'
    elif mode == 'DF21':
        fname = 'FAR43_G_DF21out.log'

    #open file
    try:
        fn = open(fname,'r')
    except IOError:
        rm.logMessage(3,"Post Processing Second ModeS File: ERROR File Not Found.")
        return -1
   
    #Read In lines
    idx1  = -1
    idx2  = -1
    for line in fn:
        #print(line)

        #Check line for DFx type
        idx1 = line.find(mode)  
  
        if idx1 > 0:             #mode (df type) string found in line
 
            #parse the line depending on message type
            if mode == 'DF4':
                idx1 = line.find('AC:')
                idx2 = line.find('ICAO:')
                dfparam = line[idx1+4:idx1+9]
                icao = line[idx2+6:idx2+12]

            elif mode == 'DF5':
                idx1 = line.find('ID:')
                idx2 = line.find('ICAO:')
                dfparam = line[idx1+4:idx1+9]
                icao = line[idx2+6:idx2+12]

            elif mode == 'DF20':
                idx1 = line.find('AC:')
                idx2 = line.find('ICAO:')
                dfparam = line[idx1+4:idx1+9]
                icao = line[idx2+6:idx2+12]

            elif mode == 'DF21':
                idx1 = line.find('ID:')
                idx2 = line.find('ICAO:')
                dfparam = line[idx1+4:idx1+9]
                icao = line[idx2+6:idx2+12]
        

        #quit early if string found,
        # no need to process anything further (data should be the same in every line)
        if idx1 > 0:
            break


    #Close the file
    fn.close()

    

    #Change Altitude from 'None' to a int
    if dfparam == 'None ':
        dfparam = '-1300'

    #Convert strings to ints
    dfparam = int(dfparam)
    icao = int(icao)

    return dfparam,icao
       
def delete_logfiles(fname):
    """ This routine deletes log files. """

    #delete the log file, if not needed
    #if its deleleted already, not a problem.
    try:
       os.remove(fname)
    except OSError:
        rm.logMessage(3,"Delete First ATCRBS File:File Not Found.")

    return
  
    

##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_G(rm,atc,rfbob):
    """ FAR43, G - MODE S Format """
    rm.logMessage(2,"*** FAR43 G, MODE S Format ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Results
    DF4_Msg_pr  = 0.0
    DF5_Msg_pr  = 0.0
    DF20_Msg_pr = 0.0
    DF21_Msg_pr = 0.0
    ModeA_Ident = -1.0
    ModeC_Alt   = -1.0
    DF4_Alt     = -1.0
    DF20_Alt    = -1.0
    DF5_Squawk  = -1.0
    DF21_Squawk = -1.0
    DF4_ICAO = -1
    DF5_ICAO = -1
    DF20_ICAO = -1
    DF21_ICAO = -1

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()

    #Get ATCRBS Ident and Altitude,put results in Log files
    log_Mode_ATCRBS(rm,atc,'ModeA')
    time.sleep(5)
    log_Mode_ATCRBS(rm,atc,'ModeC')
    time.sleep(5)
    
    #Set Up Transponder -- MODE S  *** DF4 ****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 20000000000004") #Mode S Message DF4,PR0 Adrs000004
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #start data logging
    atc.data_log_start()
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    val = replyrate[1]                                     #ModeS Bottom
    DF4_Msg_pr = val                                       #%replies
    
    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_G_DF4.log")
    time.sleep(15)     #time to download log
               
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_G_DF4.log")
    time.sleep(2)
    

    #Set Up Transponder -- MODE S  *** DF5 ****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28000000000004") #Mode S Message DF5,PR0 Adrs000004
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #start data logging
    atc.data_log_start()
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    val = replyrate[1]                                     #ModeS Bottom
    DF5_Msg_pr = val                                         #%replies
            
    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_G_DF5.log")
    time.sleep(15)     #time to download log
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_G_DF5.log")
    time.sleep(2)
    
    
    #Set Up Transponder -- MODE S  *** DF20 ****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 20800000000004") #Mode S Message DF4, RR=16, Adrs000004, get DF20,112Bit reply
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #start data logging
    atc.data_log_start()
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    val = replyrate[1]                                     #ModeS Bottom
    DF20_Msg_pr = val                                        #%replies
            
    
    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_G_DF20.log")
    time.sleep(15)     #time to download log
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_G_DF20.log")
    time.sleep(2)
    
    
    #Set Up Transponder -- MODE S  *** DF21 ****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 28800000000004") #Mode S Message DF4, RR=16, Adrs000004, get DF21,112Bit reply
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            

    #start data logging
    atc.data_log_start()
                    
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    val = replyrate[1]                                   #ModeS Bottom
    DF21_Msg_pr = val                                    #%replies
            
    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_G_DF21.log")
    time.sleep(15)     #time to download log
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_G_DF21.log")
    time.sleep(2)

    
    #Post Process the Log files,get required parameters
    ModeA_Ident = post_process_atcrbs(rm,'ModeA')
    ModeC_Alt = post_process_atcrbs(rm,'ModeC')
    DF4_Alt,DF4_ICAO   = post_process_modes(rm,'DF4')
    DF5_Squawk, DF5_ICAO = post_process_modes(rm,'DF5')
    DF20_Alt, DF20_ICAO = post_process_modes(rm,'DF20')
    DF21_Squawk, DF21_ICAO = post_process_modes(rm,'DF21')


    rm.logMessage(2,"PercentReplies: %f,%f,%f,%f" % (DF4_Msg_pr,DF5_Msg_pr,DF20_Msg_pr,DF21_Msg_pr))   
    rm.logMessage(2,"ModeA Ident: %d,ModeC Alt %d" % (ModeA_Ident,ModeC_Alt))
    rm.logMessage(2,"DF4_Alt: %d, DF4 ICAO: %d" % (DF4_Alt,DF4_ICAO))
    rm.logMessage(2,"DF5_Squawk: %d, DF5 ICAO: %d" % (DF5_Squawk,DF5_ICAO))
    rm.logMessage(2,"DF20_Alt: %d, DF20 ICAO: %d" % (DF20_Alt,DF20_ICAO))
    rm.logMessage(2,"DF21_Squawk: %d, DF21 ICAO: %d" % (DF21_Squawk,DF21_ICAO))
    rm.logMessage(2,"Done, closing session")
    
    #CleanUp, delete uneeded log files
    delete_logfiles('FAR43_G_ModeA.log')
    delete_logfiles('FAR43_G_ModeC.log')
    delete_logfiles('FAR43_G_DF4.log')
    delete_logfiles('FAR43_G_DF5.log')
    delete_logfiles('FAR43_G_DF20.log')
    delete_logfiles('FAR43_G_DF21.log')

    #Pass results back to TestStand.
    return [DF4_Msg_pr,DF5_Msg_pr,DF20_Msg_pr,DF21_Msg_pr,ModeA_Ident,ModeC_Alt,
    DF4_Alt,DF4_ICAO,DF5_Squawk,DF5_ICAO,DF20_Alt,DF20_ICAO,DF21_Squawk,DF21_ICAO]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    #Get DF4,DF5,DF20, and DF21 messages
    res = FAR43_G(rm,atc_obj,rf_obj)
 

    atc_obj.close()
    rf_obj.disconnect()
 



