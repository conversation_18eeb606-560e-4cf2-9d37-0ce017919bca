#!/usr/bin/env python3
"""
Test script to debug TXDLib import issues
"""

import sys
import os
from pathlib import Path

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("Python path:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

print("\nTesting TXDLib imports...")

try:
    import TXDLib
    print("✅ TXDLib import successful")
    print(f"TXDLib location: {TXDLib.__file__}")
except ImportError as e:
    print(f"❌ TXDLib import failed: {e}")

try:
    import TXDLib.Procedures
    print("✅ TXDLib.Procedures import successful")
    print(f"TXDLib.Procedures location: {TXDLib.Procedures.__file__}")
except ImportError as e:
    print(f"❌ TXDLib.Procedures import failed: {e}")

try:
    import TXDLib.Procedures.DO282
    print("✅ TXDLib.Procedures.DO282 import successful")
    print(f"TXDLib.Procedures.DO282 location: {TXDLib.Procedures.DO282.__file__}")
except ImportError as e:
    print(f"❌ TXDLib.Procedures.DO282 import failed: {e}")

try:
    from TXDLib.Procedures.DO282 import UAT_CONNECTION
    print("✅ UAT_CONNECTION import successful")
    print(f"UAT_CONNECTION location: {UAT_CONNECTION.__file__}")
except ImportError as e:
    print(f"❌ UAT_CONNECTION import failed: {e}")

print("\nChecking file system structure:")
txdlib_path = Path("TXDLib")
if txdlib_path.exists():
    print(f"✅ TXDLib directory exists: {txdlib_path.absolute()}")
    
    procedures_path = txdlib_path / "Procedures"
    if procedures_path.exists():
        print(f"✅ Procedures directory exists: {procedures_path.absolute()}")
        
        init_file = procedures_path / "__init__.py"
        if init_file.exists():
            print(f"✅ Procedures __init__.py exists: {init_file.absolute()}")
        else:
            print(f"❌ Procedures __init__.py missing: {init_file.absolute()}")
            
        do282_path = procedures_path / "DO282"
        if do282_path.exists():
            print(f"✅ DO282 directory exists: {do282_path.absolute()}")
            
            do282_init = do282_path / "__init__.py"
            if do282_init.exists():
                print(f"✅ DO282 __init__.py exists: {do282_init.absolute()}")
            else:
                print(f"❌ DO282 __init__.py missing: {do282_init.absolute()}")
                
            uat_conn = do282_path / "UAT_CONNECTION.py"
            if uat_conn.exists():
                print(f"✅ UAT_CONNECTION.py exists: {uat_conn.absolute()}")
            else:
                print(f"❌ UAT_CONNECTION.py missing: {uat_conn.absolute()}")
        else:
            print(f"❌ DO282 directory missing: {do282_path.absolute()}")
    else:
        print(f"❌ Procedures directory missing: {procedures_path.absolute()}")
else:
    print(f"❌ TXDLib directory missing: {txdlib_path.absolute()}")
