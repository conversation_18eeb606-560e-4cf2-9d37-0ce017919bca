import time

class MSO56():
    def __init__(self, ate_rm):
        MSO56IP = "TCPIP0::************::inst0::INSTR"
        self.resourceManager = ate_rm
        try:
            self.MSO56 = self.resourceManager.rm.open_resource(MSO56IP)
            self.resourceManager.logMessage(0, "Resource Opened")
        except:
            self.resourceManager.logMessage(3, "Resource Failed to Open")
            raise

    # Basic Commands

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.resourceManager.logMessage(1, tvlTxt)
        resp = self.MSO56.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=True):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.resourceManager.logMessage(1, tvlTxt)
        resp = self.MSO56.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.resourceManager.logMessage(1, tvlTxt)
        return resp

    def rawQuery(self, cmd):
        """ Read raw data, needed when extracting non-ascii data to avoid UnicodeDecodeError """
        tvlTxt = "> %s" % cmd
        self.resourceManager.logMessage(1, tvlTxt)
        self.MSO56.write("%s" % cmd)
        resp = self.MSO56.read_raw()
        return resp

    def Ident(self):
        """ Returns the Scope identification string. """
        return self.basicQuery("*IDN?")

    def Reset(self):
        """ Resets the Oscillioscope """
        self.basicWrite("*RST")
        print("Resetting...")
        time.sleep(5)
        print("complete\n")

    def close(self):
        """ Closes the Oscillioscope """
        self.MSO56.close()

    # XTime and YScale Commands

    def timeScale(self, time, unit):
        """ Sets the x-scale time base given <time><unit>/div """
        self.basicWrite("HORizontal:SCALe {}".format(self.timeAdjust(time, unit)))

    def voltDiv(self, chan, volts, unit):
        """ Sets the y-scale voltage base given <chan#>, <volts><unit>/div. Ex inputs: 1, 10, "mV" """
        try:
            float(volts)
            if (int(chan) in range(1, 7)):
                self.basicWrite("CH{0}:SCALe {1}".format(str(chan), self.voltAdjust(volts, unit)))
            else:
                self.resourceManager.logMessage(3, "Invalid Volt unit or Chan")
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid Volts {}".format(e.args))

    def getVoltDiv(self, chan):
        """ Returns the y-scale voltage base given <chan#> as a string. Ex inputs: 2 """
        if int(chan) in range(1, 7):
            return self.basicQuery("CH{}:SCALe?".format(str(chan)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")
    
    def setTimePosition(self, timePos):
        """ Sets the x-scale time position relative to the trigger point given <seconds>. """
        try:
            float(timePos)
            self.basicWrite("HORizontal:DELay:MODE 1")
            self.basicWrite("HORizontal:DELay:TIMe {}".format(str(timePos)))
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid time in seconds {}".format(e.args))

    def chanDisplay(self, chan, state):
        """ Turns on or off the displayed chan given <int chan# 1-4> and <state: 1[on], 0[off]> """
        if ((int(chan) in range(1, 7)) and (int(state) == 1 or int(state) == 0)):
            self.basicWrite("DISplay:GLObal:CH{0}:STATE {1}".format(str(chan), str(state)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel or State.")
    
    def chanInvert(self, chan, invert):
        """ Turns on or off the displayed chan given <int chan# 1-6> and <invert: 1[on], 0[off]> """
        if ((int(chan) in range(1, 7)) and (int(invert) == 1 or int(invert) == 0)):
            self.basicWrite("CH{0}:INVERT {1}".format(str(chan), str(invert)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel or State.")

    def chanPosition(self, chan, divisions):
        """ Sets channel position to divisions above or below center """
        if int(chan) in range(1, 7):
            self.basicWrite("CH{}:POSition {}".format(str(chan), str(divisions)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")
    
    def chanOffset(self, chan, offset, unit):
        """ Offset Channel view by offset amount. Possible units are V, mV, and uV """
        if int(chan) in range(1, 7):
            self.basicWrite("CH{}:OFFSet {}".format(str(chan), self.voltAdjust(offset, unit)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")

    def chanCoupling(self, chan, coupling):
        """ Set channel to DC or AC coupling """
        if int(chan) in range(1, 7):
            if coupling.upper() in ["DC", "AC"]:
                self.basicWrite("CH{}:COUPling {}".format(str(chan), str(coupling)))
            else:
                self.resourceManager.logMessage(3, "Invalid Channel Coupling. Options are DC or AC")
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")

    # Search Commands

    # def getPwSearch(self, settingType, setting):
    #     """ Returns the result Pulse width search given the <searchType> and <setting>. Search types include:
    #     "GREaterthan", "LESSthan", "POLarity", "QUALifier", "RANGe", "SOURce". Example input for search: 'GREaterthan', 2 """
    #     typeList = ["GREaterthan", "LESSthan", "POLarity", "QUALifier", "RANGe", "SOURce"]
    #     if setting_type in typeList:
    #         self.basicWrite("SEARCH:SEARCH:{0} {1}".format(setting_type, str(setting)))
    #         count = self.oscope.query("SEARch:COUNt?")
    #         if count == 0:
    #             print("No Search Found\n")
    #         return count
    #     else:
    #         self.resourceManager.logMessage(3, "Invalid search setting. Avaliable settings: GREaterthan, LESSthan, POLarity, QUALifier, RANGe, SOURce")

    # Trigger Commands

    def setEdgeTrig(self, chan, level, slope, coupling):
        """ Configures an analog edge trigger event. Settings include trigger source channel, level, coupling, and slope. """
        if int(chan) in range(1, 7):
            if slope.upper() in ["RISE", "FALL", "EITHER"]:
                if coupling.upper() in ["DC", "HFREJ", "LFREJ", "NOISEREJ"]:
                    self.basicWrite("TRIGger:A:TYPe EDGE")
                    self.basicWrite("TRIGger:A:EDGE:SOUrce CH{}".format(str(chan)))
                    self.basicWrite("TRIGger:A:LEVel:CH{} {}".format(str(chan), str(level)))
                    self.basicWrite("TRIGger:A:EDGE:SLOpe {}".format(str(slope)))
                    self.basicWrite("TRIGger:A:EDGE:COUPling {}".format(str(coupling)))
                else:
                    self.resourceManager.logMessage(3, "Invalid Coupling option")
            else:
                self.resourceManager.logMessage(3, "Invalid Slope option")
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")

    ''' TODO: Other types of triggers needed? '''

    # Measure Commands

    def createMeasurement(self, measId, measType):
        """ Creates a new automated measurement for the scope to monitor """
        if measType.upper() in ["ACCOMMONMODE", "ACRMS", "AMPLITUDE", "AREA", "BASE", "BITAMPLITUDE", "BITHIGH", "BITLOW",
                                "BURSTWIDTH", "COMMONMODE", "DATARATE", "DCD", "DDJ", "DDRAOS", "DDRAOSPERTCK", "DDRAOSPERUI",
                                "DDRAUS", "DDRAUSPERTCK", "DDRAUSPERUI", "DDRHOLDDIFF", "DDRSETUPDIFF", "DDRTCHABS", "DDRTCHAVERAGE",
                                "DDRTCKAVERAGE", "DDRTCLABS", "DDRTCLAVERAGE", "DDRTERRMN", "DDRTERRN", "DDRTJITCC", "DDRTJITDUTY", 
                                "DDRTJITPER", "DDRTPST", "DDRTRPRE", "DDRTWPRE", "DDRVIXAC", "DDRTDQSCK", "DELAY", "DJ", "DJDIRAC", "DPMOVERSHOOT",
                                "DPMUNDERSHOOT", "DPMRIPPLE", "DPMTURNOFFTIME", "DPMTURNONTIME", "EYEHIGH", "EYELOW", "FALLSLEWRATE", "FALLTIME", "FREQUENCY",
                                "F2", "F4", "F8", "HIGH", "HEIGHT", "HEIGHTBER", "HIGHTIME", "HOLD", "IMDAPOWERQUALITY", "IMDAHARMONICS", "IMDAINPUTVOLTAGE",
                                "IMDAINPUTCURRENT", "IMDAINPUTPOWER", "IMDAPHASORDIAGRAM", "IMDAEFFICIENCY", "IMDALINERIPPLE", "IMDASWITCHRIPPLE",
                                "JITTERSUMMARY", "J2", "J9", "LOW", "LOWTIME", "MAXIMUM", "MEAN", "MINIMUM", "NDUTY", "NPERIOD",
                                "NPJ", "NOVERSHOOT", "NWIDTH", "PDUTTY", "PERIOD", "PHASE", "PHASENOISE", "PJ", "PK2PK", "POVERSHOOT", 
                                "PWIDTH", "QFACTOR", "RISESLEWRATE", "RISETIME", "RJ", "RJDIRAC", "RMS", "SRJ", "SSCFREQDEV",
                                "SSCMODRATE", "SETUP", "SKEW", "TIE", "TIMEOUTSIDELEVEL", "TJBER",
                                "TNTRATIO", "TOP", "UNITINTERVAL", "VDIFFXOVR", "WIDTH", "WIDTHBER"]:
            self.basicWrite("MEASUrement:MEAS{}:TYPe {}".format(str(measId), measType))
        else:
            self.resourceManager.logMessage(3, "Invalid Measurement Type")

    def setMeasurementSource(self, measId, chan):
        """Sets the measurement source of a particular measurement"""
        self.basicWrite("MEASUrement:MEAS{}:SOURce CH{}".format(str(measId), str(chan)))

    def getMeasurementMax(self, measId):
        """ Get maximum value for all accumulated acquisitions of specified measurement """
        return(self.basicQuery("MEASUrement:MEAS{}:RESUlts:ALLAcqs:MAXimum?".format(str(measId))))
    
    def getMeasurementMean(self, measId):
        """ Get mean value for all accumulated acquisitions of specified measurement """
        return(self.basicQuery("MEASUrement:MEAS{}:RESUlts:ALLAcqs:MEAN?".format(str(measId))))
    
    def getMeasurementMin(self, measId):
        """ Get minimum value for all accumulated acquisitons of specified measurement """
        return(self.basicQuery("MEASUrement:MEAS{}:RESUlts:ALLAcqs:MINimum?".format(str(measId))))

    def getMeasurementPkToPk(self, measId):
        """ Get peak-to-peak value for all accumulated acquisitions of specified measurement """
        return(self.basicQuery("MEASUrement:MEAS{}:RESUlts:ALLAcqs:PK2PK?".format(str(measId))))

    def getMeasurementStdDev(self, measId):
        """ Get standard deviation value for all accumulated acquisitions of specified measurement """
        return(self.basicQuery("MEASUrement:MEAS{}:RESUlts:ALLAcqs:STDDev?".format(str(measId))))

    def refLevelType(self, refType):
        """ Sets reference level setting in %. Options are TENNinety, TWENtyeighty, and CUSTom. """
        if refType.upper() in ["TENN", "TENNINETY", "TWEN", "TWENTYEIGHTY", "CUST", "CUSTOM"]:
            self.basicWrite("MEASUrement:REFLevels:PERCent:TYPE {}".format(str(refType)))
        else:
            self.resourceManager.logMessage(3, "Invalid Reference Level setting. Options are TENNinety, TWENtyeighty, and CUSTom")
    
    def configCustomRefLevels(self, riseHigh, riseMid, riseLow, fallHigh, fallMid, fallLow):
        """ Specifies reference level % values for "CUSTom" reference level type (see refLevelType() to enable) """
        self.basicWrite("MEASUrement:REFLevels:PERCent:RISEHigh {}".format(riseHigh))
        self.basicWrite("MEASUrement:REFLevels:PERCent:RISEMid {}".format(riseMid))
        self.basicWrite("MEASUrement:REFLevels:PERCent:RISELow {}".format(riseLow))
        self.basicWrite("MEASUrement:REFLevels:PERCent:FALLHigh {}".format(fallHigh))
        self.basicWrite("MEASUrement:REFLevels:PERCent:FALLMid {}".format(fallMid))
        self.basicWrite("MEASUrement:REFLevels:PERCent:FALLLow {}".format(fallLow))
    
    def setChannelTermination(self, chan, terminationVal):
        """ Configures channel termination impedance. Can be 50 Ω or 1000000 Ω. """
        if chan in range(1, 7):
            if terminationVal in [50, 1000000]:
                self.basicWrite("CH{}:TERmination {}".format(str(chan), str(terminationVal)))
            else:
                self.resourceManager.logMessage(3, "Invalid Probe Termination Impedance. Options are 50 Ω or 1000000 Ω")
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")
    
    def clearAllMeasurements(self):
        """ Clears all automated measurements. """
        measList = (self.basicQuery("MEASUrement:LIST?")).split(",")
        for measurement in measList:
            self.basicWrite("MEASUrement:DELete \"{}\"".format(measurement))
        self.resourceManager.logMessage(1, "All Measurements Cleared")

    # Acquistion Commands

    def acqSetType(self, acqType):
        """ Selects acquisition mode. Options are SAMple, PEAKdetect, HIRes, AVErage, ENVelope """
        if acqType.upper() in ["SAM", "SAMPLE", "PEAK", "PEAKDETECT", "HIR", "HIRES", "AVE", "AVERAGE", "ENV", "ENVELOPE"]:
            self.basicWrite("ACQuire:MODe {}".format(str(acqType)))
        else:
            self.resourceManager.logMessage(3, "Invalid Acquisition Mode. Options are SAMple, PEAKdetect, HIRes, AVErage, ENVelope")

    def acqState(self, state):
        """ Starts or stops acquisitions. """
        if state.upper() in ["OFF", "ON"]:
            self.basicWrite("ACQuire:STATE {}".format(str(state)))
        else:
            self.resourceManager.logMessage(3, "Invalid Acquisition State. Can either be OFF or ON")

    def acqStopAfter(self, state):
        """ Determines whether the instrument continually acquires acquisitions(RUNStop) or acquires a single sequence(SEQuence). """
        if state.upper() in ["RUNSTOP", "SEQUENCE"]:
            self.basicWrite("ACQuire:STOPAfter {}".format(str(state)))
        else:
            self.resourceManager.logMessage(3, "Invalid stop command. Either RUNStop or SEQuence")

    # Waveform Commands

    def cursorDisplay(self, state):
        """ Turn on/off display cursors for waveview. """
        if state in ["OFF", "ON"]:
            self.basicWrite("DISplay:WAVEView1:CURSor:CURSOR1:STATE {}".format(state))
        else:
            self.resourceManager.logMessage(3, "Cursor state must be OFF or ON.")

    def setCursorPos(self, aVal, bVal, units):
        """ Set A and B cursors to specified time locations on waveview. """
        self.basicWrite("DISplay:WAVEView1:CURSor:CURSOR1:WAVEform:APOSition {}".format(self.timeAdjust(aVal, units)))
        self.basicWrite("DISplay:WAVEView1:CURSor:CURSOR1:WAVEform:BPOSition {}".format(self.timeAdjust(bVal, units)))

    def getCursorValue(self, aorb):
        """ Gets A or B cursor vertical value. """
        if aorb in ["A", "B"]:
            return self.basicQuery("DISplay:WAVEView1:CURSor:CURSOR1:HBArs:{}POSition?".format(str(aorb)))
        else:
            self.resourceManager.logMessage(3, "Cursor must be A or B.")


    def setCursorSource(self, chan):
        """ Set source for A and B cursors. """
        if chan in range(1, 7):
            self.basicWrite("DISplay:WAVEView1:CURSor:CURSOR1:ASOUrce CH{}".format(str(chan)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")

    def waveSource(self, chan):
        """ Select location of waveform data to be transferred to instrument """
        if chan in range(1, 7):
            self.basicWrite("DATa:SOUrce CH{}".format(str(chan)))
        else:
            self.resourceManager.logMessage(3, "Invalid Channel")
    
    def waveDataFormat(self, dataFormat):
        """ Set format of outgoing waveform data. """
        if dataFormat.upper() in ["ASCII", "RIBINARY", "RPBINARY", "FPBINARY", "SRIBINARY", "SRPBINARY", "SFPBINARY"]:
            self.basicWrite("DATa:ENCdg {}".format(str(dataFormat)))
        else:
            self.resourceManager.logMessage(3, "Invalid Data Format")
        
    def waveDataBinWidth(self, dataFormat):
        """ Sets binary field data width (bytes per point) for outgoing waveform data. Can be 1, 2, or 8 bytes per waveform point. """
        if str(dataFormat) in ["1", "2", "8"]:
            self.basicWrite("WFMOutpre:BYT_Nr {}".format(str(dataFormat)))
        else:
            self.resourceManager.logMessage(3, "Invalid binary data width. Must be 1, 2, or 8 bytes")
    
    def waveDataRange(self, start, stop):
        """ Sets start and last data point for waveform transfer. """
        self.basicWrite("DATa:STARt {}".format(str(start)))
        self.basicWrite("DATa:STOP {}".format(str(stop)))

    def wavePreamble(self):
        """ Returns outgoing waveform's formatting data. """
        return(self.rawQuery("WFMOutpre?"))

    def waveRawData(self):
        """ Returns outgoing waveform data. """
        return(self.rawQuery("CURVe?"))

    def retrieveWave(self, chanSource, dataFormat="ASCIi", dataWidth=2, start=1, stop=9999999999999):
        """ Retrieve waveform data points from the scope. Function performs all the neccessary actions to perform transfer. """
        self.waveSource(chanSource)
        self.waveDataFormat(dataFormat)
        self.waveDataBinWidth(dataWidth)
        self.waveDataRange(start, stop)
        preamble = self.wavePreamble()
        waveData = self.waveRawData()
        return (preamble, waveData)
    
    # Support Functions

    def timeAdjust(self, value, unit):
        """ time scales "s", "ms", "us", "ns", "ps" """
        unit = unit.lower()
        try:
            float(value)
            if unit == "s":
                return (str(value))
            elif unit == "ms":
                return (str(value) + "e-3")
            elif unit == "us":
                return (str(value) + "e-6")
            elif unit == "ns":
                return (str(value) + "e-9")
            elif unit == "ps":
                return (str(value) + "e-12")
            else:
                self.resourceManager.logMessage(3, "Invalid timescale option ({})".format(unit))
                return 0
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid time value {}".format(e.args))

    def voltAdjust(self, value, unit):
        """ volt scales "v", "mv", "uv" """
        unit = unit.lower()
        try:
            float(value)
            if unit == "v":
                return (str(value))
            elif unit == "mv":
                return (str(value) + "e-3")
            elif unit == "uv":
                return (str(value) + "e-6")
            else:
                self.resourceManager.logMessage(3, "Invalid volt unit option ({})".format(unit))
                return 0
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid volt value {}".format(e.args))
