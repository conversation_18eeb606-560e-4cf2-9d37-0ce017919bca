#!/usr/bin/env python3
"""
TXD Qualification Test System - Report Generator
Generates consolidated reports for system test execution
"""

import json
import os
from datetime import datetime
from pathlib import Path
# from typing import Dict, Any, List, Optional  # Not available in Python 3.4


class ReportGenerator:
    """Consolidated report generator for system test results"""
    
    def __init__(self, output_dir: str = "reports"):
        """
        Initialize report generator
        
        Args:
            output_dir: Directory to save reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.output_dir / "system_test_reports").mkdir(exist_ok=True)
        (self.output_dir / "consolidated_reports").mkdir(exist_ok=True)
    
    def generate_consolidated_report(self, 
                                   execution_summary: Dict[str, Any],
                                   sequence_results: List[Dict[str, Any]],
                                   report_format: str = "both") -> Dict[str, str]:
        """
        Generate consolidated system test report
        
        Args:
            execution_summary: Summary of test execution
            sequence_results: List of individual sequence results
            report_format: "json", "markdown", or "both"
        
        Returns:
            Dictionary containing paths to generated report files
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        mode = execution_summary.get('execution_mode', 'unknown')
        
        report_data = {
            "report_info": {
                "report_type": "TXD System Test - Consolidated Report",
                "generation_timestamp": datetime.now().isoformat(),
                "execution_mode": mode,
                "report_version": "1.0.0"
            },
            "execution_summary": execution_summary,
            "sequence_results": sequence_results,
            "analysis": self._analyze_results(execution_summary, sequence_results)
        }
        
        generated_files = {}
        
        # Generate JSON report
        if report_format in ["json", "both"]:
            json_path = self.output_dir / "consolidated_reports" / f"system_test_report_{mode}_{timestamp}.json"
            self._generate_json_report(report_data, json_path)
            generated_files["json"] = str(json_path)
        
        # Generate Markdown report
        if report_format in ["markdown", "both"]:
            md_path = self.output_dir / "consolidated_reports" / f"system_test_report_{mode}_{timestamp}.md"
            self._generate_markdown_report(report_data, md_path)
            generated_files["markdown"] = str(md_path)
        
        return generated_files
    
    def _analyze_results(self, 
                        execution_summary: Dict[str, Any], 
                        sequence_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze test results and provide insights
        
        Args:
            execution_summary: Summary of test execution
            sequence_results: List of individual sequence results
        
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            "overall_status": "UNKNOWN",
            "procedure_breakdown": {},
            "performance_metrics": {},
            "failure_analysis": {},
            "recommendations": []
        }
        
        # Determine overall status
        if execution_summary.get('failed', 0) == 0 and execution_summary.get('errors', 0) == 0:
            analysis["overall_status"] = "PASSED"
        else:
            analysis["overall_status"] = "FAILED"
        
        # Analyze by procedure
        procedure_stats = {}
        for result in sequence_results:
            procedure = result.get('procedure', 'unknown')
            if procedure not in procedure_stats:
                procedure_stats[procedure] = {
                    'total': 0, 'passed': 0, 'failed': 0, 'errors': 0, 'timeouts': 0
                }
            
            procedure_stats[procedure]['total'] += 1
            status = result.get('status', 'unknown').lower()
            if status == 'passed':
                procedure_stats[procedure]['passed'] += 1
            elif status == 'failed':
                procedure_stats[procedure]['failed'] += 1
            elif status == 'error':
                procedure_stats[procedure]['errors'] += 1
            elif status == 'timeout':
                procedure_stats[procedure]['timeouts'] += 1
        
        analysis["procedure_breakdown"] = procedure_stats
        
        # Performance metrics
        total_time = execution_summary.get('total_execution_time', 0)
        total_sequences = execution_summary.get('total_sequences', 0)
        
        analysis["performance_metrics"] = {
            "total_execution_time": total_time,
            "average_sequence_time": total_time / total_sequences if total_sequences > 0 else 0,
            "sequences_per_hour": (total_sequences / (total_time / 3600)) if total_time > 0 else 0,
            "optimization_effectiveness": self._calculate_optimization_effectiveness(sequence_results)
        }
        
        # Failure analysis
        failed_sequences = [r for r in sequence_results if r.get('status') in ['FAILED', 'ERROR', 'TIMEOUT']]
        analysis["failure_analysis"] = {
            "total_failures": len(failed_sequences),
            "failure_by_procedure": self._group_failures_by_procedure(failed_sequences),
            "common_failure_patterns": self._identify_failure_patterns(failed_sequences)
        }
        
        # Generate recommendations
        analysis["recommendations"] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _calculate_optimization_effectiveness(self, sequence_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate effectiveness of optimizations"""
        # This would compare actual execution times with pre-optimization baselines
        # For now, return estimated effectiveness based on successful optimizations
        
        total_sequences = len(sequence_results)
        successful_sequences = len([r for r in sequence_results if r.get('status') == 'PASSED'])
        
        return {
            "optimization_success_rate": (successful_sequences / total_sequences * 100) if total_sequences > 0 else 0,
            "estimated_time_savings": "157-187 seconds per full test suite",
            "optimization_status": "ACTIVE" if successful_sequences > 0 else "NEEDS_REVIEW"
        }
    
    def _group_failures_by_procedure(self, failed_sequences: List[Dict[str, Any]]) -> Dict[str, int]:
        """Group failures by procedure"""
        failure_counts = {}
        for failure in failed_sequences:
            procedure = failure.get('procedure', 'unknown')
            failure_counts[procedure] = failure_counts.get(procedure, 0) + 1
        return failure_counts
    
    def _identify_failure_patterns(self, failed_sequences: List[Dict[str, Any]]) -> List[str]:
        """Identify common failure patterns"""
        patterns = []
        
        # Check for timeout patterns
        timeouts = [f for f in failed_sequences if f.get('status') == 'TIMEOUT']
        if len(timeouts) > 0:
            patterns.append(f"Timeout issues detected in {len(timeouts)} sequences")
        
        # Check for specific error patterns
        error_messages = [f.get('stderr', '') for f in failed_sequences if f.get('stderr')]
        if error_messages:
            patterns.append("Hardware communication errors detected")
        
        return patterns
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        if analysis["overall_status"] == "FAILED":
            recommendations.append("Review failed sequences and address root causes before production use")
        
        failure_count = analysis["failure_analysis"]["total_failures"]
        if failure_count > 0:
            recommendations.append(f"Investigate {failure_count} failed sequences")
        
        # Add standard recommendations
        recommendations.extend([
            "Verify hardware connections before live mode execution",
            "Run mock mode tests regularly to validate sequence logic",
            "Monitor execution times for performance regression",
            "Keep system test reports for compliance documentation"
        ])
        
        return recommendations
    
    def _generate_json_report(self, report_data: Dict[str, Any], output_path: Path):
        """Generate JSON format report"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    def _generate_markdown_report(self, report_data: Dict[str, Any], output_path: Path):
        """Generate Markdown format report"""
        with open(output_path, 'w', encoding='utf-8') as f:
            # Header
            f.write("# TXD Qualification Test System - System Test Report\n\n")

            # Report info
            report_info = report_data["report_info"]
            f.write("**Generated**: {0}\n".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            f.write("**Execution Mode**: {0}\n".format(report_info['execution_mode'].upper()))
            f.write("**Report Version**: {0}\n\n".format(report_info['report_version']))
            
            # Executive Summary
            summary = report_data["execution_summary"]
            analysis = report_data["analysis"]
            
            status_icon = "✅" if analysis["overall_status"] == "PASSED" else "❌"
            f.write("## Executive Summary: {0} {1}\n\n".format(status_icon, analysis['overall_status']))

            f.write("| Metric | Value |\n")
            f.write("|--------|-------|\n")
            f.write("| Total Sequences | {0} |\n".format(summary.get('total_sequences', 0)))
            f.write("| Passed | {0} |\n".format(summary.get('passed', 0)))
            f.write("| Failed | {0} |\n".format(summary.get('failed', 0)))
            f.write("| Errors | {0} |\n".format(summary.get('errors', 0)))
            f.write("| Timeouts | {0} |\n".format(summary.get('timeouts', 0)))
            f.write("| Success Rate | {0:.1f}% |\n".format(summary.get('success_rate', 0)))
            f.write("| Total Execution Time | {0:.1f} seconds |\n\n".format(summary.get('total_execution_time', 0)))
            
            # Procedure Breakdown
            f.write("## Procedure Breakdown\n\n")
            for procedure, stats in analysis["procedure_breakdown"].items():
                total = stats['total']
                passed = stats['passed']
                success_rate = (passed / total * 100) if total > 0 else 0
                status_icon = "✅" if stats['failed'] == 0 and stats['errors'] == 0 else "❌"

                f.write("### {0} {1}\n".format(status_icon, procedure))
                f.write("- **Total Sequences**: {0}\n".format(total))
                f.write("- **Passed**: {0}\n".format(passed))
                f.write("- **Failed**: {0}\n".format(stats['failed']))
                f.write("- **Success Rate**: {0:.1f}%\n\n".format(success_rate))
            
            # Performance Metrics
            f.write("## Performance Metrics\n\n")
            perf = analysis["performance_metrics"]
            f.write("- **Total Execution Time**: {0:.1f} seconds\n".format(perf['total_execution_time']))
            f.write("- **Average Sequence Time**: {0:.1f} seconds\n".format(perf['average_sequence_time']))
            f.write("- **Sequences Per Hour**: {0:.1f}\n".format(perf['sequences_per_hour']))
            f.write("- **Optimization Status**: {0}\n\n".format(perf['optimization_effectiveness']['optimization_status']))

            # Recommendations
            f.write("## Recommendations\n\n")
            for i, recommendation in enumerate(analysis["recommendations"], 1):
                f.write("{0}. {1}\n".format(i, recommendation))
            f.write("\n")
            
            # Individual Sequence Results (summary)
            f.write("## Individual Sequence Results\n\n")
            for result in report_data["sequence_results"]:
                status = result.get('status', 'UNKNOWN')
                status_icon = "✅" if status == 'PASSED' else "❌"
                procedure = result.get('procedure', 'unknown')
                sequence = result.get('sequence', 'unknown')
                exec_time = result.get('execution_time', 0)

                f.write("### {0} {1}/{2}\n".format(status_icon, procedure, sequence))
                f.write("- **Status**: {0}\n".format(status))
                f.write("- **Execution Time**: {0:.1f} seconds\n".format(exec_time))

                if status in ['FAILED', 'ERROR', 'TIMEOUT']:
                    error_msg = result.get('stderr', 'No error details available')
                    truncated_msg = error_msg[:100] + ('...' if len(error_msg) > 100 else '')
                    f.write("- **Error**: {0}\n".format(truncated_msg))

                f.write("\n")
    
    def generate_quick_summary(self, execution_summary: Dict[str, Any]) -> str:
        """
        Generate a quick text summary for console output
        
        Args:
            execution_summary: Summary of test execution
        
        Returns:
            Formatted summary string
        """
        mode = execution_summary.get('execution_mode', 'unknown').upper()
        total = execution_summary.get('total_sequences', 0)
        passed = execution_summary.get('passed', 0)
        failed = execution_summary.get('failed', 0)
        errors = execution_summary.get('errors', 0)
        timeouts = execution_summary.get('timeouts', 0)
        success_rate = execution_summary.get('success_rate', 0)
        exec_time = execution_summary.get('total_execution_time', 0)
        
        status = "PASSED" if (failed + errors + timeouts) == 0 else "FAILED"
        status_icon = "✅" if status == "PASSED" else "❌"
        
        summary = """
{0} SYSTEM TEST EXECUTION COMPLETE ({1} MODE)
{2}
Total Sequences:    {3}
Passed:            {4}
Failed:            {5}
Errors:            {6}
Timeouts:          {7}
Success Rate:      {8:.1f}%
Execution Time:    {9:.1f} seconds

Overall Status:    {10}
{11}
""".format(status_icon, mode, '='*60, total, passed, failed, errors, timeouts, success_rate, exec_time, status, '='*60)
        return summary
