# Routines for the NI PXI Scope
import niscope                     

def measureParametricData(minSampleRate, voltage, numSamples):
    """ This routine reads the PXI Scope (5110) waveform data and measures parametric data for a DIFFERENTIAL signal. 
        Returns a list of the following measurements in this order: 
        Rise Time, Fall Time, Pulse Width, Period, Frequency, Maximum, Minimum, Peak to Peak, Amplitude
        Inputs: 
        minSampleRate - Minimum sample rate for acquiring waveform data (make sure it's >2x maximum waveform frequency)
        voltage - expected voltage scale of waveform
        numSamples - Number of samples to acquire """
        
     # Opening session
    with niscope.Session("PXI1Slot4") as session:

        # Configure channels and timing 
        session.configure_horizontal_timing(min_sample_rate=minSampleRate, min_num_pts=numSamples, ref_position=0.0, num_records=1, enforce_realtime=True)
        session.configure_vertical(range=voltage, coupling=niscope.VerticalCoupling.DC, enabled=True)

        session.meas_chan_low_ref_level = 10
        session.meas_chan_mid_ref_level = 50
        session.meas_chan_high_ref_level = 90

        session.configure_trigger_edge("0", 0.0, niscope.TriggerCoupling.DC)

        with session.initiate():
            # Get Waveform Data
            session.channels['0-1'].fetch_array_measurement(array_meas_function=niscope.ArrayMeasurement.SUBTRACT_CHANNELS,
                                                            meas_wfm_size=numSamples, num_records=1, meas_num_samples=numSamples)

            # Parametric Scalar Measurements from above data (MeasurementStats structure)
            riseTimeData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.RISE_TIME)
            fallTimeData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.FALL_TIME)
            pulseWidthData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.WIDTH_POS)
            periodData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.PERIOD)
            freqData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.FREQUENCY)
            maxData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.VOLTAGE_MAX)
            minData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.VOLTAGE_MIN)
            pkToPkData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.VOLTAGE_PEAK_TO_PEAK)
            amplitudeData = session.channels["0-1"].fetch_measurement_stats(scalar_meas_function=niscope.ScalarMeasurement.AMPLITUDE)

    # Extract scalar integer value from MeasurementStats data structures
    riseTime = riseTimeData[0][0].result
    fallTime = fallTimeData[1][0].result
    pulseWidth = pulseWidthData[2][0].result
    period = periodData[3][0].result
    freq = freqData[4][0].result
    maximum = maxData[5][0].result
    minimum = minData[6][0].result
    pkToPk = pkToPkData[7][0].result
    amplitude = amplitudeData[8][0].result

    return [riseTime, fallTime, pulseWidth, period, freq, maximum, minimum, pkToPk, amplitude]
    # return [riseTimeData, fallTimeData, pulseWidthData, periodData, freqData, maxData, minData, pkToPkData, amplitudeData]