#!/usr/bin/env python3
"""
Simple test of legacy module imports
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path
current_dir = Path.cwd()
mock_handlers_path = current_dir / "MockHandlers"
sys.path.insert(0, str(mock_handlers_path))

print("Testing legacy module imports...")
print(f"Python path: {sys.path[:3]}")

try:
    # Import mock CLR first
    import mock_clr
    print("SUCCESS: mock_clr imported")
    
    # Add common directory to path FIRST (higher priority)
    common_path = mock_handlers_path / "common"
    sys.path.insert(1, str(common_path))  # Insert after MockHandlers but before others
    print(f"Added common path: {common_path}")

    # Test atc import
    import atc
    print("SUCCESS: atc module imported")

    # Test powersupply import from common directory
    import importlib.util
    powersupply_spec = importlib.util.spec_from_file_location(
        "powersupply",
        common_path / "powersupply.py"
    )
    powersupply = importlib.util.module_from_spec(powersupply_spec)
    powersupply_spec.loader.exec_module(powersupply)
    print("SUCCESS: powersupply module imported from common")
    
    # Test clr import
    import clr
    print("SUCCESS: clr module imported")
    
    # Test ATC class instantiation with mock resource manager
    class MockResourceManager:
        def __init__(self):
            pass

    rm = MockResourceManager()
    atc_obj = atc.ATC(rm)
    print("SUCCESS: atc.ATC instantiated")

    # Test power supply instantiation
    print(f"powersupply module attributes: {dir(powersupply)}")
    pwr_obj = powersupply.Powersup(rm)
    print("SUCCESS: powersupply.Powersup instantiated")
    
    print("\nAll legacy imports working!")
    
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()
