import time, sys

def enable_4VDC_DigitalBOB(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 4.0
    currLim = 1.0
    chnl = 1

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)
    dcps.setOutputState("ON", chnl)

    aterm.logMessage(1, "Procedure Ended")

def disable_4VDC_DigitalBOB(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    chnl = 1
    
    dcps.setOutputState("OFF", chnl)

    aterm.logMessage(1, "Procedure Ended")

def enable_18VDC_RFBreakout(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 18.0
    currLim = 1.0
    chnl = 4

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)
    dcps.setOutputState("ON", chnl)

    aterm.logMessage(1, "Procedure Ended")

def disable_18VDC_RFBreakout(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    chnl = 4
    
    dcps.setOutputState("OFF", chnl)

    aterm.logMessage(1, "Procedure Ended")

def setup_ATE_Power(aterm):
    aterm.logMessage(1, "Procedure Started")
    dcps=aterm.instruments["dcps"]
    voltage = 18.0
    currLim = 1.0
    chnl = 4

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)
    
    voltage = 4.0
    currLim = 2.0
    chnl = 1

    dcps.setVolt(voltage, chnl)
    dcps.setCurrLim(currLim, chnl)
    time.sleep(0.5)

    aterm.logMessage(1, "Procedure Ended")