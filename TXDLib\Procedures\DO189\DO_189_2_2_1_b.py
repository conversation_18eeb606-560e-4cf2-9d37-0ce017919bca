# -*- coding: utf-8 -*-
"""
Created on Wed Feb 26 3:02:30 2020

@author: E589493
         K<PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Dynamic Range Accuracy, Section 2.2.1b
             
             "Dynamic Range - A variation in input signal level in the 
             absence of fruit from -10 dBm to -83 dBm."
             
INPUTS:      Top_Cable_Loss, atc, ARINC, Power Levels
OUTPUTS:     Distance

HISTORY:

02/26/2020   KF    Initial Release.
06/22/2020   AS    Added tvl statements, Added ARINC
03/10/2021   MRS   Updates for new handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
       

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def waitforstatus(atc):
    """ Wait for Valid Status. """
    STATUS = str( atc.query(":ATC:STATUS?") )
    while STATUS != '\n20':
        STATUS = str(atc.query(":ATC:STATUS?"))
        print("STATUS: ",STATUS)
        time.sleep(1)    


def init_DME_Standard(cable_loss, rm,atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel X1 with VOR Pair 0 134.4MHz at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    ARINC.writeChannel(134.40)
    time.sleep(5)
    rm.logMessage(0,"*Test_189_2_2_1 - initializing DME") 
    atc.DMEMode()
    #atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))
    rm.logMessage(0,"*Test_189_2_2_1 - DME initialized") 

def atc_power_level(Step_PowerLevel, rm, atc):
    """ This function set the next power level to be measured eg -50 dBm for DO-189 2.2.1b"""
    rm.logMessage(0,"*Test_189_2_2_1 - Setting ATC power level to " + str(Step_PowerLevel)) 

    cmd = ':ATC:DME:POWER ' + str(Step_PowerLevel)
    atc.gwrite(cmd)

def read_Distance(ARINC,rm):
    """Returns distance in NM
        To be used in TestStand"""
    time.sleep(5)
    distance = ARINC.getDistance_201()
    time.sleep(5)
    rm.logMessage(0,("DISTANCE: " + str(distance)))
    return distance

def pass_fail_distance(ARINC,rm):
    """Prints pass if distance = 34NM +/- .17, fail otherwise.
    This runs specifically from command-line"""
    time.sleep(5)
    distance = ARINC.getDistance_201()
    time.sleep(5)
    rm.logMessage(0,"DISTANCE: " + str(distance))
    if(33.83 <= distance and distance <= 34.17):
         rm.logMessage(0,"PASS")
    else:
        rm.logMessage(0,"FAIL")
      
def main():

    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
 
    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    
    cable_loss = 0

    #Initialize the DME to the Standard conditions
    init_DME_Standard(cable_loss, rm, atc, ARINC)
    
    #Setup Power Level to -10dBM
    atc_power_level(-10, rm, atc)
    pass_fail_distance(ARINC, rm)

    #Setup Power Level to -50dBM
    atc_power_level(-50, rm, atc)
    pass_fail_distance(ARINC, rm)

    #Setup Power Level to -83dBM
    atc_power_level(-83, rm, atc)
    pass_fail_distance(ARINC, rm)


    atc.close()
 
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()

if __name__ == "__main__":
    main()