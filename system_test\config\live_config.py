#!/usr/bin/env python3
"""
TXD Qualification Test System - Live Configuration
Configuration settings for live execution mode with real hardware
"""

# from typing import Dict, Any, List  # Not available in Python 3.4


class LiveConfig:
    """Configuration settings for live mode execution"""
    
    # Hardware connection settings
    HARDWARE_CONNECTIONS = {
        'atc5000ng': {
            'connection_type': 'ethernet',
            'ip_address': '*************',
            'port': 5025,
            'timeout': 30.0,
            'retry_attempts': 3
        },
        'rgs2000ng': {
            'connection_type': 'ethernet',
            'ip_address': '*************',
            'port': 5025,
            'timeout': 30.0,
            'retry_attempts': 3
        },
        'spectrum_analyzer': {
            'connection_type': 'visa',
            'resource_string': 'TCPIP0::*************::inst0::INSTR',
            'timeout': 10.0,
            'retry_attempts': 2
        },
        'signal_generator': {
            'connection_type': 'visa',
            'resource_string': 'TCPIP0::*************::inst0::INSTR',
            'timeout': 10.0,
            'retry_attempts': 2
        },
        'power_meter': {
            'connection_type': 'visa',
            'resource_string': 'TCPIP0::*************::inst0::INSTR',
            'timeout': 10.0,
            'retry_attempts': 2
        },
        'oscilloscope': {
            'connection_type': 'visa',
            'resource_string': 'TCPIP0::*************::inst0::INSTR',
            'timeout': 15.0,
            'retry_attempts': 2
        },
        'dc_power_supply': {
            'connection_type': 'visa',
            'resource_string': 'TCPIP0::*************::inst0::INSTR',
            'timeout': 10.0,
            'retry_attempts': 2
        }
    }
    
    # Optimized timing settings for live hardware
    LIVE_TIMINGS = {
        'connection_timeout': 30.0,
        'command_timeout': 10.0,
        'measurement_timeout': 30.0,
        'scenario_loading_timeout': 300.0,  # 5 minutes max
        'rf_stabilization_timeout': 60.0,   # 1 minute max
        'instrument_reset_timeout': 60.0,   # 1 minute max
        'communication_retry_delay': 0.5,   # Optimized from 1.0s
        'status_polling_interval': 0.1,     # For active status checking
        'max_polling_attempts': 100         # Maximum status check attempts
    }
    
    # Hardware validation settings
    HARDWARE_VALIDATION = {
        'required_hardware': [
            'atc5000ng',
            'rgs2000ng',
            'spectrum_analyzer',
            'signal_generator',
            'power_meter'
        ],
        'optional_hardware': [
            'oscilloscope',
            'dc_power_supply',
            'ni_discretes',
            'ni_multiio',
            'pickering'
        ],
        'validation_timeout': 60.0,
        'skip_missing_optional': True
    }
    
    # Safety settings
    SAFETY_SETTINGS = {
        'max_rf_power': 10.0,           # dBm
        'max_dc_voltage': 30.0,         # Volts
        'max_dc_current': 5.0,          # Amps
        'emergency_shutdown_enabled': True,
        'power_monitoring_enabled': True,
        'automatic_safe_state': True
    }
    
    # Performance optimization settings
    OPTIMIZATION_SETTINGS = {
        'enable_status_polling': True,      # Use status polling instead of fixed delays
        'enable_command_batching': True,    # Batch configuration commands
        'enable_parallel_operations': False, # Parallel operations (use with caution)
        'enable_adaptive_timeouts': True,   # Adaptive timeout based on hardware response
        'fallback_to_fixed_delays': True,   # Fallback if optimizations fail
        'optimization_validation': True     # Validate optimizations don't affect accuracy
    }
    
    @classmethod
    def get_hardware_connection(cls, device: str) -> Dict[str, Any]:
        """
        Get hardware connection settings for a device
        
        Args:
            device: Device name
        
        Returns:
            Dictionary containing connection settings
        """
        return cls.HARDWARE_CONNECTIONS.get(device, {})
    
    @classmethod
    def get_live_timing(cls, operation: str) -> float:
        """
        Get live timing setting for an operation
        
        Args:
            operation: Operation name
        
        Returns:
            Timeout or delay value in seconds
        """
        return cls.LIVE_TIMINGS.get(operation, 10.0)
    
    @classmethod
    def is_hardware_required(cls, device: str) -> bool:
        """
        Check if hardware is required for testing
        
        Args:
            device: Device name
        
        Returns:
            True if hardware is required
        """
        return device in cls.HARDWARE_VALIDATION['required_hardware']
    
    @classmethod
    def is_hardware_optional(cls, device: str) -> bool:
        """
        Check if hardware is optional for testing
        
        Args:
            device: Device name
        
        Returns:
            True if hardware is optional
        """
        return device in cls.HARDWARE_VALIDATION['optional_hardware']
    
    @classmethod
    def get_safety_limit(cls, parameter: str) -> float:
        """
        Get safety limit for a parameter
        
        Args:
            parameter: Safety parameter name
        
        Returns:
            Safety limit value
        """
        return cls.SAFETY_SETTINGS.get(parameter, 0.0)
    
    @classmethod
    def is_optimization_enabled(cls, optimization: str) -> bool:
        """
        Check if an optimization is enabled
        
        Args:
            optimization: Optimization name
        
        Returns:
            True if optimization is enabled
        """
        return cls.OPTIMIZATION_SETTINGS.get(optimization, False)
    
    @classmethod
    def get_configuration_summary(cls) -> Dict[str, Any]:
        """
        Get summary of live configuration
        
        Returns:
            Dictionary containing configuration summary
        """
        return {
            'mode': 'LIVE',
            'required_hardware': cls.HARDWARE_VALIDATION['required_hardware'],
            'optional_hardware': cls.HARDWARE_VALIDATION['optional_hardware'],
            'total_hardware_interfaces': len(cls.HARDWARE_CONNECTIONS),
            'optimization_status': 'ACTIVE' if any(cls.OPTIMIZATION_SETTINGS.values()) else 'DISABLED',
            'safety_features': {
                'emergency_shutdown': cls.SAFETY_SETTINGS['emergency_shutdown_enabled'],
                'power_monitoring': cls.SAFETY_SETTINGS['power_monitoring_enabled'],
                'automatic_safe_state': cls.SAFETY_SETTINGS['automatic_safe_state']
            },
            'timing_optimizations': {
                'status_polling': cls.OPTIMIZATION_SETTINGS['enable_status_polling'],
                'command_batching': cls.OPTIMIZATION_SETTINGS['enable_command_batching'],
                'adaptive_timeouts': cls.OPTIMIZATION_SETTINGS['enable_adaptive_timeouts'],
                'communication_retry_delay': "{0}s".format(cls.LIVE_TIMINGS['communication_retry_delay'])
            }
        }
    
    @classmethod
    def validate_configuration(cls):
        """
        Validate live configuration settings
        
        Returns:
            Dictionary containing validation results
        """
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Check for required settings
        for device in cls.HARDWARE_VALIDATION['required_hardware']:
            if device not in cls.HARDWARE_CONNECTIONS:
                validation_results['errors'].append("Missing connection config for required device: {0}".format(device))
                validation_results['valid'] = False
        
        # Check safety settings
        if not cls.SAFETY_SETTINGS['emergency_shutdown_enabled']:
            validation_results['warnings'].append("Emergency shutdown is disabled")
        
        # Check optimization settings
        if not cls.OPTIMIZATION_SETTINGS['fallback_to_fixed_delays']:
            validation_results['warnings'].append("Fallback to fixed delays is disabled")
        
        return validation_results
