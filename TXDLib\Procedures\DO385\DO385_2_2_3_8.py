# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-385 MOPs requirement for
             Transmit Pulse Characteristic, Section *******
             
             Step1: Mode S Preamble. Interrogate the transponder with a standard
			 Mode A/Mode S All-Call.  Display the Mode S reply on an Oscilloscope.
			 Measure the pulse duration of the first four reply pulses.  Measure
			 the pulse spacing between the leading edge of the first and each of the 
			 second, third, and fourth pulses.
             
             Step2: Mode S Reply Data Pulses: Measure the pulse spacing of
			 the fifth reply pulse with reference to the first reply pulse.
             
             
             Step3: Mode S Reply Pulse Shape: measure the rise time of the reply 
			 pulses.
             
             Step4: Mode S Reply Pulse Spacing Tolerance: Determine that the leading edge of any
			 reply pulse is within 50 nanoseconds of its assigned position.
              
             
INPUTS:      RM, RGS, Scope, PathLoss
OUTPUTS:     'PulseDuration' -- array of pulse durations in ModeS Preamble
             'PulseSpacing'  -- array of pulse spacing in ModeS Preamble
             'PulseSpacing5' -- pulse spacing between 1st and 5th Pulse
             'PowerDifference -- max power deviation in reply pulses
             'RiseTime'  -- average Rise/Fall times of reply pulses
             'FallTime'  -- average Fall times of reply pulses
             'Tolerance' -- average Pulse Position tolerance

HISTORY:

04/13/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review     
08/06/2020   AKS    Added comments per review 
03/01/2021   MRS    Updates for New Handlers                           
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG 
from TXDLib.Handlers.D3054Scope import D3054Scope


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def modeX_Setup(scope_obj,timescale,threshold,trigger = 1):
    """ Basic Scope setup for Mode S Pulse. 
   Chan 2: Trigger, Chan3: Data ... RFBOB must be set up apriori."""
    
    scope_obj.Reset()
    #Display Chan 1 
    scope_obj.chanDisplay(1,1)
    scope_obj.chanDisplay(2,0)
    scope_obj.chanDisplay(3,0)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 50, "mV")
    scope_obj.voltDiv(2, 50, "mV")
    scope_obj.voltDiv(3, 50, "mV")
    scope_obj.voltDiv(4, 40, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    #scope_obj.invertChan(3,1)
    #scope_obj.invertChan(4,1)
    #Set Impdance 1 and 2 to 50 Ohms
    scope_obj.setChanImpdance(1,5)
    scope_obj.setChanImpdance(2,5)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 1 is trigger
    scope_obj.trigRun()

def measurePulseChar(scope_obj, edgePPosition, source = 1):
    """Function that measures PWidth, Rise, and fall for a given Pulse rising edge position.
    Fall time is taken by moving the cursor edgePPosition + PWidth. """

    #Measure Chan 'source'
    scope_obj.setMeasureSource(source)
    result = []

    scope_obj.setTimePosition(edgePPosition)
    result.append(scope_obj.measPWidth())
    scope_obj.timeScale(.5, "us")
    time.sleep(.1)
    result.append(scope_obj.measRiseTime())
    scope_obj.setTimePosition(edgePPosition + result[0])
    time.sleep(.1)
    result.append(scope_obj.measFallTime())
    time.sleep(.1)

    return result  

def trigDetect(scope_obj):
    """ Function that triggers a given waveform and returns all detected edge positions based
    on trigger position. """

    #Trigger Source Chan is set in setup (modex_setup)
    if scope_obj.trigSingle(2):
        return 1
    return 0




##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_2_3_8(rm,rgs,scope,PathLoss):
    """ DO-385, ModeS Tranmit Pulse Characteristics: Sect ******* """
    
    rm.logMessage(2,"*** DO-385, ModeS Transmit Pulse Characteristics: Sect **********\r\n")
    
  

    #Results read by TestStand
    PulseDuration = [0.0,0.0,0.0,0.0]    #array of pulse durations in ModeS Preamble
    PulseSpacing = [0.0,0.0,0.0,0.0]     #array of pulse spacing in ModeS Preamble
    PulseSpacing5 = 0.0                  #pulse spacing between 1st and 5th Pulse
    RiseTime = 0.0                       #average Rise times of reply pulses
    FallTime = 0.0                       #average Fall times of reply pulses
    Tolerance = 0.0                      #average Pulse Position tolerance
    
    #Load ModeS Bearing Scenario (< +/- 10 deg)
    rm.logMessage(0,"*Test_*******: Start Scenario")  
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('Brglt10ModeS.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)
   
    #Steps 1,2 and 3, Preamble and Rise/Fall Time Avg
    rm.logMessage(0,"*Test_2_2_3_8 - Start Pulse Measurements")   
    #Set Up the OScope for Pulse Measurements    
    tmescale =  50.0            #scope time scale (usec/div)
    threshold = 50.0           #threshold (mV)
    trigger = 1
    modeX_Setup(scope,tmescale,threshold,trigger)
    time.sleep(5)

    #Scale Factor for Scope Measurements
    sf = 1.0e6      #usec
    if trigDetect(scope) == 1:
        # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
        PEdges,NEdges = scope.digiEdgePos(50/1000.0,source = 1)
        
       
        #Number of Positive/Negative Edges
        print("PEdges: ",len(PEdges),PEdges)     
        print("NEdges: ",len(NEdges),NEdges)
        if len(PEdges) == 0:
            rm.logMessage(3,"Test_2_2_3_8 - Error, No Edges Detected")
        else:
            #Loop thru Positive Edges and gather pulse data
            for edge_pos in PEdges:
                #print ("EdgePos: ",edge_pos)
                pulse_char = measurePulseChar(scope, edge_pos,source = 1)
                rm.logMessage(0,"Pulse duration: " + str(pulse_char[0]))
                rm.logMessage(0,"Pulse rise: " + str(pulse_char[1]))
                rm.logMessage(0,"Pulse fall: " + str(pulse_char[2]))
                RiseTime = RiseTime + pulse_char[1]                      #average Rise times of reply pulses
                FallTime = FallTime + pulse_char[2]                      #average Fall times of reply pulses
                
            #Comput Rise/Fall Averages
            RiseTime = (RiseTime/len(PEdges)) * sf
            FallTime = (FallTime/len(PEdges)) * sf
            
            # Uncomment for module built in plot
            scope.timeScale(tmescale,"us")
            scope.setTimePosition(PEdges[0])
            #scope.plotWave()
            

            #Evaluate Preamble Pulses for Results
            PulseDuration[0] = (NEdges[0] - PEdges[0]) * sf - 0.3    #0.3 is to the 6dB power level
            PulseDuration[1] = (NEdges[1] - PEdges[1]) * sf - 0.3
            PulseDuration[2] = (NEdges[2] - PEdges[2]) * sf - 0.3
            PulseDuration[3] = (NEdges[3] - PEdges[3]) * sf - 0.3
            PulseSpacing[0] = (PEdges[1] - PEdges[0]) * sf
            PulseSpacing[1] = (PEdges[2] - PEdges[0]) * sf
            PulseSpacing[2] = (PEdges[3] - PEdges[0]) * sf
            PulseSpacing[3] = (PEdges[4] - PEdges[0]) * sf
            PulseSpacing5 = (PEdges[4] - PEdges[0])    * sf                #pulse spacing between 1st and 5th Pulse
            span =( NEdges[len(PEdges)-1]- PEdges[0]) * sf
            print("*** MESSAGE SPAN ***",span )
            span =( PEdges[4] - PEdges[0]) * sf
            print("*** PreAmble Span ***",span)
            span = (NEdges[len(PEdges)-1] - PEdges[4]) * sf
            print("*** Data Span ***",span)
    
    #concatonate lists    
    return PulseDuration + PulseSpacing + [PulseSpacing5] + [RiseTime] + [FallTime] + [Tolerance]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)


    scope_obj = D3054Scope(rm)
    scope_obj.Reset()

   
    res = Test_2_2_3_8(rm,rgs,scope_obj,12.0)
    
    scope_obj.close()
    rgs.close()



