"""
Mock implementation of D3054Scope.py
Provides mock functionality without external dependencies
"""

import time
import random
from typing import Any, List, Dict, Optional, Union


class MockD3054Scope:
    """Mock implementation of D3054Scope"""

    def __init__(self, *args, **kwargs):
        """Mock initialization"""
        self.connected = True
        self.initialized = True

        # Handle common initialization patterns
        if args and hasattr(args[0], 'logMessage'):
            self.resourceManager = args[0]
            self.resourceManager.logMessage(1, f"Mock D3054Scope initialized successfully")
        else:
            print(f"Mock D3054Scope initialized successfully")

    def digiEdgePos(self, threshold, source=1):
        """Mock digital edge position detection - returns tuple of positive and negative edges"""
        import time
        import random
        time.sleep(0.01)  # Minimal delay

        # Generate realistic edge data
        num_edges = random.randint(5, 15)
        pos_edges = [random.uniform(0, 1) for _ in range(num_edges)]
        neg_edges = [random.uniform(0, 1) for _ in range(num_edges)]

        if hasattr(self, 'resourceManager') and hasattr(self.resourceManager, 'logMessage'):
            self.resourceManager.logMessage(1, f"Mock digiEdgePos: {len(pos_edges)} pos edges, {len(neg_edges)} neg edges")
        else:
            print(f"Mock digiEdgePos: {len(pos_edges)} pos edges, {len(neg_edges)} neg edges")

        return pos_edges, neg_edges
    
    def __getattr__(self, name):
        """Mock any missing methods dynamically"""
        def mock_method(*args, **kwargs):
            """Dynamic mock method"""
            time.sleep(0.001)  # Minimal realistic delay
            
            # Handle common method patterns
            if name.lower().startswith(('get', 'read', 'query', 'measure')):
                # Return realistic mock data for getters/queries
                if 'freq' in name.lower():
                    return 1030.0 + random.uniform(-1.0, 1.0)
                elif 'power' in name.lower() or 'pwr' in name.lower():
                    return -20.0 + random.uniform(-10.0, 10.0)
                elif 'voltage' in name.lower() or 'volt' in name.lower():
                    return 5.0 + random.uniform(-0.5, 0.5)
                elif 'current' in name.lower():
                    return 0.1 + random.uniform(-0.05, 0.05)
                elif 'temp' in name.lower():
                    return 25.0 + random.uniform(-5.0, 5.0)
                elif 'status' in name.lower():
                    return "OK" if random.random() > 0.1 else "BUSY"
                elif 'id' in name.lower():
                    return f"Mock D3054Scope,Model123,SN456789,FW1.0"
                elif 'time' in name.lower():
                    return 0.1 + random.uniform(-0.01, 0.01)
                elif 'width' in name.lower():
                    return 0.45 + random.uniform(-0.05, 0.05)
                elif 'delay' in name.lower():
                    return 3.0 + random.uniform(-0.2, 0.2)
                else:
                    return "OK"
            elif name.lower().startswith(('set', 'write', 'send', 'config', 'init')):
                # Setters/writers return success
                return True
            elif name.lower() in ['close', 'cleanup', 'disconnect', 'stop']:
                # Cleanup methods
                self.connected = False
                return True
            elif name.lower() in ['reset', 'restart']:
                # Reset methods with optimized timing
                time.sleep(0.5)  # Reduced reset time
                return True
            else:
                # Default return for unknown methods
                return True
                
        return mock_method

# Create class alias for backward compatibility
D3054Scope = MockD3054Scope
