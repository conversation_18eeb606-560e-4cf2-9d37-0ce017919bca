# TXD Qualification Test System - Testing Infrastructure Guide

## Overview

This document provides comprehensive guidance for using the TXD Qualification Test System's testing and build infrastructure. The system includes unit tests, integration tests, mock interfaces, build validation, and comprehensive reporting.

## Quick Start

### 1. Run Complete Test Suite
```bash
# Run all tests and generate all reports
python run_all_tests.py

# Run tests for specific procedure
python run_all_tests.py --procedure DO282

# Skip system tests (for CI/CD environments)
python run_all_tests.py --skip-system
```

### 2. Individual Commands
```bash
# Build and validate system
python build.py

# Run unit tests only
python test_unit.py

# Run integration tests only
python test_integration.py

# Run system tests with real procedures
python run_system.py --procedure DO282

# Generate specific reports
python generate_reports.py --type unit
python generate_reports.py --type integration
python generate_reports.py --type performance
python generate_reports.py --type optimization
python generate_reports.py --type all
```

## Directory Structure

```
ProcedureQual/
├── build.py                          # Build system and validation
├── test_unit.py                      # Unit test runner
├── test_integration.py               # Integration test runner
├── run_system.py                     # System test runner
├── generate_reports.py               # Report generator
├── run_all_tests.py                  # Comprehensive test runner
├── tests/
│   ├── unit/                         # Unit tests
│   │   ├── test_atc5000ng.py        # ATC5000NG handler tests
│   │   ├── test_power_meter.py      # Power meter tests
│   │   ├── test_spectrum_analyzer.py # Spectrum analyzer tests
│   │   ├── test_signal_generator.py # Signal generator tests
│   │   ├── test_oscilloscope.py     # Oscilloscope tests
│   │   ├── test_arinc_client.py     # ARINC 429 tests
│   │   ├── test_do282_procedures.py # DO282 procedure tests
│   │   ├── test_do189_procedures.py # DO189 procedure tests
│   │   └── test_far43_procedures.py # FAR43 procedure tests
│   ├── integration/                  # Integration tests
│   │   └── test_hardware_communication.py
│   ├── mocks/                        # Mock interfaces
│   │   ├── mock_atc5000ng.py        # ATC5000NG mock
│   │   ├── mock_power_meter.py      # Power meter mock
│   │   ├── mock_spectrum_analyzer.py # Spectrum analyzer mock
│   │   ├── mock_signal_generator.py # Signal generator mock
│   │   ├── mock_oscilloscope.py     # Oscilloscope mock
│   │   ├── mock_arinc429.py         # ARINC 429 mock
│   │   ├── mock_uat_connection.py   # UAT connection mock
│   │   └── mock_resource_manager.py # Resource manager mock
│   └── reports/                      # Generated reports
└── build/                            # Build artifacts
```

## Testing Categories

### 1. Unit Tests
**Purpose**: Test individual components in isolation using mock interfaces

**Coverage**:
- All Handler files (instrument control layer)
- All Procedure files (test implementation layer)
- Sleep optimization validation (HIGH and MEDIUM priority)
- Error handling and fallback mechanisms
- Backward compatibility verification

**Key Features**:
- Mock hardware interfaces for isolated testing
- Optimization timing validation
- Error condition testing
- Performance regression detection

### 2. Integration Tests
**Purpose**: Test component interactions and end-to-end workflows

**Coverage**:
- Hardware communication protocols
- Multi-instrument coordination
- Real-world test scenarios
- Optimization effectiveness validation

**Key Features**:
- Mock hardware for consistent testing
- End-to-end procedure validation
- Communication protocol testing
- Performance measurement

### 3. System Tests
**Purpose**: Execute real test procedures with actual hardware

**Coverage**:
- Complete DO282, DO189, FAR43, DO181, DO385 procedures
- Real hardware validation
- Production environment testing
- Optimization time savings verification

**Key Features**:
- Real hardware execution
- Production procedure validation
- Performance benchmarking
- Compliance verification

## Optimization Validation

### HIGH PRIORITY Optimizations (157-187s total savings)

1. **Scenario Loading (DO282)**
   - Files: `DO282_24823.py`, `DO282_248212.py`
   - Original: 80s total delays
   - Optimized: Adaptive polling with status verification
   - Savings: 50-70s per test

2. **RF Stabilization (FAR43)**
   - File: `FAR43_A_Frequency.py`
   - Original: 50s total delays (25s × 2)
   - Optimized: 30s total delays (15s × 2)
   - Savings: 20s per test

3. **Instrument Reset (ATC5000NG)**
   - File: `ATC5000NG.py`
   - Original: 15s delay
   - Optimized: 8s delay with status verification
   - Savings: 7s per reset

### MEDIUM PRIORITY Optimizations

1. **Communication Retries (ATC5000NG)**
   - Original: 1s retry delays
   - Optimized: 0.5s retry delays
   - Savings: 8s per test with retries

2. **Measurement Settling (DO189)**
   - Files: `DO_189_2_2_3.py`, `DO_189_2_2_6.py`, `DO_189_2_2_10.py`
   - Original: Various fixed delays
   - Optimized: Status polling and batched commands
   - Savings: 50s total across procedures

## Report Types

### 1. Unit Test Report
- Test coverage and pass/fail status
- Optimization validation results
- Performance metrics
- Error analysis

### 2. Integration Test Report
- Hardware communication validation
- End-to-end test results
- Multi-component interaction testing
- System integration status

### 3. Performance Report
- Optimization time savings analysis
- Before/after performance comparison
- Target achievement validation
- Performance regression detection

### 4. System Health Report
- Overall system status
- Component health monitoring
- Issue identification and recommendations
- Maintenance guidance

### 5. Optimization Validation Report
- Detailed optimization implementation status
- Time savings verification
- Fallback mechanism validation
- Compliance with safety requirements

## Command Reference

### Build System
```bash
python build.py
```
**Purpose**: Validate project structure, dependencies, and optimizations
**Output**: Build report in `tests/reports/build_report.md`

### Unit Testing
```bash
python test_unit.py
```
**Purpose**: Run all unit tests with mock interfaces
**Output**: Unit test report with coverage and optimization validation

### Integration Testing
```bash
python test_integration.py
```
**Purpose**: Run integration tests with mock or real hardware
**Output**: Integration test report with communication validation

### System Testing
```bash
# Run all available procedures
python run_system.py

# Run specific procedure
python run_system.py --procedure DO282
python run_system.py --procedure DO189
python run_system.py --procedure FAR43
```
**Purpose**: Execute real test procedures
**Output**: System test report with procedure results

### Report Generation
```bash
# Generate all reports
python generate_reports.py --type all

# Generate specific report types
python generate_reports.py --type unit
python generate_reports.py --type integration
python generate_reports.py --type performance
python generate_reports.py --type system_health
python generate_reports.py --type optimization
```
**Purpose**: Generate comprehensive analysis reports
**Output**: Multiple report formats (JSON and Markdown)

### Comprehensive Testing
```bash
# Complete test suite
python run_all_tests.py

# Test specific procedure
python run_all_tests.py --procedure DO282

# CI/CD mode (skip system tests)
python run_all_tests.py --skip-system
```
**Purpose**: Run complete testing workflow
**Output**: Execution summary and all reports

## Safety and Compliance

### Safety-Critical Considerations
- All optimizations maintain original functionality
- Fallback mechanisms preserve safety margins
- Status polling provides active verification
- Error handling maintains system integrity

### Compliance Validation
- DO-282 UAT testing compliance
- DO-189 DME testing compliance  
- FAR-43 transponder testing compliance
- DO-181 Mode S testing compliance
- DO-385 TCAS testing compliance

### Quality Assurance
- Comprehensive test coverage
- Mock interface validation
- Performance regression testing
- Documentation and traceability

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Python version (3.7+)
   - Verify all dependencies installed
   - Ensure project structure is complete

2. **Test Failures**
   - Review test output for specific errors
   - Check mock interface configuration
   - Verify optimization implementations

3. **Hardware Communication Issues**
   - Validate VISA drivers installed
   - Check network connectivity
   - Verify instrument addresses

4. **Report Generation Issues**
   - Ensure reports directory exists
   - Check file permissions
   - Verify test data availability

### Support
For technical support or questions about the testing infrastructure, refer to:
- Build logs in `tests/reports/build_report.md`
- Test execution logs in individual report files
- System health reports for component status
- Optimization validation reports for performance analysis
