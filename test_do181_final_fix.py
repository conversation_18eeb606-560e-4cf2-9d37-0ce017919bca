#!/usr/bin/env python3
"""
Test the final DO181 sequence fix
"""

import subprocess
import sys
import os
from pathlib import Path

# Set up environment like sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

# Test the failing sequence
sequence = "TXDLib/Procedures/DO181/DO_181E_2_3_2_8.py"

print(f"Testing: {sequence}")
print("=" * 60)

try:
    result = subprocess.run(
        [sys.executable, sequence],
        capture_output=True,
        text=True,
        timeout=300,  # 5 minute timeout
        env=env
    )
    
    print(f"Return code: {result.returncode}")
    if result.returncode == 0:
        print("✅ PASSED - data_log_start() fix working!")
    else:
        print("❌ FAILED")
        print("STDERR:")
        print(result.stderr[-1000:])  # Last 1000 chars of error
        
except subprocess.TimeoutExpired:
    print("⏱️ TIMEOUT - Likely working but slow")
except Exception as e:
    print(f"❌ EXCEPTION: {e}")

print("\nTesting data_log_start method directly...")
try:
    from TXDLib.Handlers import ate_rm, ATC5000NG
    rm = ate_rm()
    atc = ATC5000NG(rm)
    atc.data_log_start()
    print("✅ data_log_start() method working!")
except Exception as e:
    print(f"❌ data_log_start() failed: {e}")
