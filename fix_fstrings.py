#!/usr/bin/env python3
"""
Quick script to fix f-strings for Python 3.4 compatibility
"""

import re
import os

def fix_fstrings_in_file(file_path):
    """Fix f-strings in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple regex to find f-strings and convert them
        # This is a basic conversion - may need manual review for complex cases
        
        # Pattern: f"text {var} more text"
        pattern = r'f"([^"]*?)"'
        
        def replace_fstring(match):
            fstring_content = match.group(1)
            
            # Find variables in curly braces
            var_pattern = r'\{([^}]+)\}'
            variables = re.findall(var_pattern, fstring_content)
            
            # Replace {var} with {0}, {1}, etc.
            new_content = fstring_content
            for i, var in enumerate(variables):
                new_content = new_content.replace('{' + var + '}', '{' + str(i) + '}')
            
            # Create format string
            format_args = ', '.join(variables)
            if format_args:
                return '"{0}".format({1})'.format(new_content, format_args)
            else:
                return '"{0}"'.format(new_content)
        
        # Apply the replacement
        new_content = re.sub(pattern, replace_fstring, content)
        
        # Also handle f'...' (single quotes)
        pattern_single = r"f'([^']*?)'"
        new_content = re.sub(pattern_single, replace_fstring, new_content)
        
        # Write back if changed
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("Fixed f-strings in: {0}".format(file_path))
            return True
        else:
            print("No f-strings found in: {0}".format(file_path))
            return False
            
    except Exception as e:
        print("Error processing {0}: {1}".format(file_path, e))
        return False

def main():
    """Main function"""
    files_to_fix = [
        'build.py',
        'test_unit.py', 
        'test_integration.py',
        'run_system.py',
        'generate_reports.py',
        'run_all_tests.py'
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_fstrings_in_file(file_path):
                fixed_count += 1
        else:
            print("File not found: {0}".format(file_path))
    
    print("\nFixed f-strings in {0} files".format(fixed_count))

if __name__ == "__main__":
    main()
