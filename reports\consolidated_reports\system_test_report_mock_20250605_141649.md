# TXD Qualification Test System - System Test Report

**Generated**: 2025-06-05 14:16:49
**Execution Mode**: MOCK
**Report Version**: 1.0.0

## Executive Summary: ❌ FAILED

| Metric | Value |
|--------|-------|
| Total Sequences | 8 |
| Passed | 3 |
| Failed | 5 |
| Errors | 0 |
| Timeouts | 0 |
| Success Rate | 37.5% |
| Total Execution Time | 1.5 seconds |

## Procedure Breakdown

### ❌ DO282
- **Total Sequences**: 8
- **Passed**: 3
- **Failed**: 5
- **Success Rate**: 37.5%

## Performance Metrics

- **Total Execution Time**: 1.5 seconds
- **Average Sequence Time**: 0.2 seconds
- **Sequences Per Hour**: 19775.4
- **Optimization Status**: ACTIVE

## Recommendations

1. Review failed sequences and address root causes before production use
2. Investigate 5 failed sequences
3. Verify hardware connections before live mode execution
4. Run mock mode tests regularly to validate sequence logic
5. Monitor execution times for performance regression
6. Keep system test reports for compliance documentation

## Individual Sequence Results

### ❌ DO282/DO282_248211.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ❌ DO282/DO282_248212.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ❌ DO282/DO282_248213.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ❌ DO282/DO282_24822.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ❌ DO282/DO282_24823.py
- **Status**: FAILED
- **Execution Time**: 0.2 seconds
- **Error**: Traceback (most recent call last):
  File "C:\DevForTXD\FormalQual_TXD\ProcedureQual\TXDLib\Procedur...

### ✅ DO282/FEC.py
- **Status**: PASSED
- **Execution Time**: 0.1 seconds

### ✅ DO282/UAT_CONNECTION.py
- **Status**: PASSED
- **Execution Time**: 0.2 seconds

### ✅ DO282/reedsolo.py
- **Status**: PASSED
- **Execution Time**: 0.1 seconds

