"""
Mock implementation of ARINC_Client.py
Provides mock functionality without external dependencies
"""

import time
import random
from typing import Any, List, Dict, Optional, Union


class MockARINC_Client:
    """Mock implementation of ARINC_Client"""

    def __init__(self, *args, **kwargs):
        """Mock initialization"""
        self.connected = True
        self.initialized = True

        # Handle common initialization patterns
        if args and hasattr(args[0], 'logMessage'):
            self.resourceManager = args[0]
            self.resourceManager.logMessage(1, f"Mock ARINC_Client initialized successfully")
        else:
            print(f"Mock ARINC_Client initialized successfully")

    def TCAS_Decode(self, data):
        """Mock TCAS decode - returns tuple of (intruders, ranges, altitudes, bearings)"""
        import time
        import random
        time.sleep(0.01)  # Minimal delay

        # Generate realistic TCAS data - lists for each component
        num_intruders = random.randint(1, 5)
        intruders = [random.randint(1, 100) for _ in range(num_intruders)]
        ranges = [random.uniform(0, 50) for _ in range(num_intruders)]
        altitudes = [random.randint(0, 40000) for _ in range(num_intruders)]
        bearings = [random.uniform(0, 360) for _ in range(num_intruders)]

        if hasattr(self, 'resourceManager'):
            self.resourceManager.logMessage(1, f"Mock TCAS_Decode: {num_intruders} intruders")

        return intruders, ranges, altitudes, bearings
    
    def __getattr__(self, name):
        """Mock any missing methods dynamically"""
        def mock_method(*args, **kwargs):
            """Dynamic mock method"""
            time.sleep(0.001)  # Minimal realistic delay
            
            # Handle specific ARINC methods that return tuples
            if name.upper() == 'TCAS_DECODE':
                # Return tuple for TCAS decode (intruder, range, altitude, bearing)
                # Based on the code, it expects lists for each component
                num_intruders = random.randint(1, 5)
                intruders = [random.randint(1, 100) for _ in range(num_intruders)]
                ranges = [random.uniform(0, 50) for _ in range(num_intruders)]
                altitudes = [random.randint(0, 40000) for _ in range(num_intruders)]
                bearings = [random.uniform(0, 360) for _ in range(num_intruders)]
                return intruders, ranges, altitudes, bearings
            elif name.lower() in ['read_data', 'get_data']:
                # Return mock ARINC data
                return f"ARINC_DATA_{random.randint(1000, 9999)}"
            # Handle common method patterns
            elif name.lower().startswith(('get', 'read', 'query', 'measure')):
                # Return realistic mock data for getters/queries
                if 'freq' in name.lower():
                    return 1030.0 + random.uniform(-1.0, 1.0)
                elif 'power' in name.lower() or 'pwr' in name.lower():
                    return -20.0 + random.uniform(-10.0, 10.0)
                elif 'voltage' in name.lower() or 'volt' in name.lower():
                    return 5.0 + random.uniform(-0.5, 0.5)
                elif 'current' in name.lower():
                    return 0.1 + random.uniform(-0.05, 0.05)
                elif 'temp' in name.lower():
                    return 25.0 + random.uniform(-5.0, 5.0)
                elif 'status' in name.lower():
                    return "OK" if random.random() > 0.1 else "BUSY"
                elif 'id' in name.lower():
                    return f"Mock ARINC_Client,Model123,SN456789,FW1.0"
                elif 'time' in name.lower():
                    return 0.1 + random.uniform(-0.01, 0.01)
                elif 'width' in name.lower():
                    return 0.45 + random.uniform(-0.05, 0.05)
                elif 'delay' in name.lower():
                    return 3.0 + random.uniform(-0.2, 0.2)
                else:
                    return "OK"
            elif name.lower().startswith(('set', 'write', 'send', 'config', 'init')):
                # Setters/writers return success
                return True
            elif name.lower() in ['close', 'cleanup', 'disconnect', 'stop']:
                # Cleanup methods
                self.connected = False
                return True
            elif name.lower() in ['reset', 'restart']:
                # Reset methods with optimized timing
                time.sleep(0.5)  # Reduced reset time
                return True
            else:
                # Default return for unknown methods
                return True
                
        return mock_method

# Create class alias for backward compatibility
ARINC_Client = MockARINC_Client

# Additional common patterns for specific handlers
