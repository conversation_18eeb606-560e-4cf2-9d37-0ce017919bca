﻿# -*- coding: utf-8 -*-
"""
Created on Tu<PERSON> Jan  5 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 B Reply Supression requiremensts.
             
             When Classes 1B and 2B ATCRBS Transponders, or Classes 1B, 2B, 
             and 3B Mode S transponders are interrogated Mode 3/A at an interrogation
             rate between 230 and 1,000 interrogations per second; or when Classes 1A
             and 2A ATCRBS Transponders, or Classes 1B, 2A, 3A, and 4 Mode S 
             transponders are interrogated at a rate between 230 and 1,200 Mode 3/A
             interrogations per second:

             (1) Verify that the transponder does not respond to more than 1 percent 
             of ATCRBS interrogations when the amplitude of P2 pulse is equal to the P1 pulse.

             (2) Verify that the transponder replies to at least 90 percent of ATCRBS 
             interrogations when the amplitude of the P2 pulse is 9 dB less than the P1 pulse. 
             If the test is conducted with a radiated test signal, the interrogation rate 
             shall be 235 ±5 interrogations per second unless a higher rate has been 
             approved for the test equipment used at that location.
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     ReplyRatio_1percent -  %replies at 1% or less
             ReplyRatio_90percent=  %replies at 90% or greater

HISTORY:
01/05/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

    
##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_B(rm,atc,rfbob):
    """ FAR43, B - Reply Supression """
    rm.logMessage(2,"*** FAR43 B, Supression ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Reply Ratio's (values read by TestStand)
    ReplyRatio_1percent = -1.0
    ReplyRatio_90percent= -1.0

    #Initialize ATC to Transponder mode
    atc.transponderMode()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    
    #Turn SLS ON
    atc.gwrite(":ATC:XPDR:SLS ON")
    #atc.gwrite(":ATC:XPDR:PUL:P2POWER)

        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    

    #get reply ratio (with SLS ON)
    replyrate = atc.getPercentReply(2)
            
    val = replyrate[1]         #ATCRBS Bottom
        
    # fix for erroneous reply rate
    count = 0
    while val == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        val = replyrate[1]
        count = count + 1

    ReplyRatio_1percent = val
    
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    #Turn SLS ON, Set P2 Power -9 dB below P1
    atc.gwrite(":ATC:XPDR:SLS ON")
    atc.gwrite(":ATC:XPDR:PUL:P2POWER -9")
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)



    atc.waitforstatus()            
        
    #get reply ration (with SLS at -9dB)
    replyrate = atc.getPercentReply(2)
            
    val = replyrate[1]         #ATCRBS Bottom
        
    # fix for erroneous reply rate
    count = 0
    while val == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        val = replyrate[1]
        count = count + 1

    ReplyRatio_90percent = val

    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
   
    rm.logMessage(2,"ReplyRatio's: %f,%f" % (ReplyRatio_1percent,ReplyRatio_90percent))   
    rm.logMessage(2,"Done, closing session")

    
    return [ReplyRatio_1percent, ReplyRatio_90percent]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()
    
    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
   
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
    
    res = FAR43_B(rm,atc_obj,rf_obj)
    
    atc_obj.close()
    rf_obj.disconnect()
 
