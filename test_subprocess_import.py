#!/usr/bin/env python3
"""
Test script to debug subprocess import issues
"""

import subprocess
import sys
import os
from pathlib import Path

# Simulate the environment setup from sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

# Add TXDLib to Python path for imports
current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'  # Additional flag for mock detection

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

print("Environment setup:")
print(f"TXD_EXECUTION_MODE: {env.get('TXD_EXECUTION_MODE')}")
print(f"TXD_MOCK_MODE: {env.get('TXD_MOCK_MODE')}")
print(f"PYTHONPATH: {env.get('PYTHONPATH')}")

# Test the import in a subprocess
test_script = '''
import sys
import os
print("Subprocess Python path:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

print("\\nEnvironment variables:")
print(f"TXD_EXECUTION_MODE: {os.environ.get('TXD_EXECUTION_MODE')}")
print(f"TXD_MOCK_MODE: {os.environ.get('TXD_MOCK_MODE')}")

print("\\nTesting imports...")
try:
    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc
    print("SUCCESS: UAT_CONNECTION import successful")
except ImportError as e:
    print(f"FAILED: UAT_CONNECTION import failed: {e}")
'''

print("\nRunning subprocess test...")
result = subprocess.run(
    [sys.executable, '-c', test_script],
    capture_output=True,
    text=True,
    env=env
)

print("STDOUT:")
print(result.stdout)
print("STDERR:")
print(result.stderr)
print(f"Return code: {result.returncode}")
