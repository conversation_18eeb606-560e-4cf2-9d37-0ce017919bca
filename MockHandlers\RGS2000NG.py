"""
Mock implementation of RGS2000NG.py - RGS Test Equipment
Provides mock RGS functionality without network dependencies
"""

import threading
import time
import random
from typing import Any


class RGS2000NG:
    def __init__(self, ate_rm):
        self.resourceManager = ate_rm
        self.connected = True
        self.status = "*|20"  # Ready status
        self.current_scenario = None
        self.scenario_running = False
        
        # Mock successful connection
        self.resourceManager.logMessage(0, "Mock RGS Connection Success")

    def basicTvlMsg(self, tvlTxt: str):
        self.resourceManager.logMessage(1, tvlTxt)
        
    def basicWrite(self, cmd: str):
        """Mock basic write command"""
        time.sleep(0.001)  # Minimal delay
        self.resourceManager.logMessage(1, f"[MOCK RGS] > {cmd}")
        
        # Parse some commands to maintain state
        if ":RGS:SCE:STA" in cmd.upper():
            self.scenario_running = True
        elif ":RGS:SCE:STOP" in cmd.upper():
            self.scenario_running = False
        elif ":RGS:RESET" in cmd.upper():
            time.sleep(2)  # Reduced from 15s - optimized reset delay
            
        return 0

    def basicQuery(self, cmd: str, logEnable: bool = False) -> str:
        """Mock basic query command with realistic responses"""
        time.sleep(0.002)  # Minimal delay
        
        # Generate realistic responses
        if "*IDN?" in cmd.upper():
            resp = "RGS2000NG,Mock,SN789012,FW3.0"
        elif ":RGS:STATUS?" in cmd.upper():
            resp = self.status
        elif "FREQ?" in cmd.upper():
            resp = str(1030.0 + random.uniform(-0.5, 0.5))
        elif "FALL?" in cmd.upper():
            resp = str(0.1 + random.uniform(-0.01, 0.01))
        elif "RISE?" in cmd.upper():
            resp = str(0.1 + random.uniform(-0.01, 0.01))
        elif "WID?" in cmd.upper():
            resp = str(0.45 + random.uniform(-0.05, 0.05))
        else:
            resp = "*"  # Success indicator
            
        if logEnable:
            self.resourceManager.logMessage(1, f"[MOCK RGS] < {resp}")
        return resp
    
    def Ident(self) -> str:
        """Mock identification"""
        return self.basicQuery("*IDN?")

    def Reset(self):
        """Mock reset with optimized timing"""
        self.basicWrite(":RGS:RESET")
        time.sleep(2)  # Reduced from 15s - optimized reset delay
        self.basicTvlMsg("Mock reset complete\n")
    
    def close(self):
        """Mock RGS close"""
        self.connected = False
         
    def rgsStatus(self) -> str:
        """Mock status query"""
        return self.basicQuery(":RGS:STATUS?")

    def setGeneratorFreq(self, gen: str, freq: float):
        """Mock generator frequency setting"""
        self.basicWrite(f":RGS:SET:{gen}:FREQ {freq}")

    # Scenario Commands
    def init_own_aircraft_pos(self, altitude: str):
        """Mock aircraft position initialization"""
        self.basicWrite(":RGS:OWN:LAT 48.834340")
        self.basicWrite(":RGS:OWN:LONG -122.214200")
        self.basicWrite(":RGS:OWN:HEAD 0")
        self.basicWrite(f":RGS:OWN:ALT {altitude}")
        self.basicWrite(":RGS:OWN:MSADDR 4")
        
    def loadScen(self, scenario: str):
        """Mock scenario loading with optimized timing"""
        self.basicWrite(f":RGS:SCE:LOAD {scenario}")
        self.current_scenario = scenario
        time.sleep(2)  # Reduced from 50s to 2s - scenario loading optimization
        self.resourceManager.logMessage(1, f"Mock scenario {scenario} loaded")

    def startScen(self):
        """Mock scenario start"""
        self.basicWrite(":RGS:SCE:STA")
        self.scenario_running = True
        
        # Mock monitoring thread (simplified)
        monitor = threading.Timer(1, self.monitor_rgs)  # Reduced from 60s
        monitor.start()

    def monitor_rgs(self):
        """Mock RGS monitoring"""
        if self.rgsStatus() != "*|20" and self.scenario_running:
            self.resourceManager.logMessage(1, "Mock RGS monitoring - restarting scenario")

    def stopScen(self):
        """Mock scenario stop"""
        self.basicWrite(":RGS:SCE:STOP")
        self.scenario_running = False

    def saveScen(self, file: str):
        """Mock scenario save"""
        self.basicWrite(f":RGS:SCE:SAVE {file}")

    def resetScen(self):
        """Mock scenario reset"""
        self.basicWrite(":RGS:SCE:RESET")
        self.current_scenario = None
        self.scenario_running = False

    # Measurement Commands
    def pulse_measure(self, measurement: int):
        """Mock pulse measurement setup"""
        self.basicWrite(f":RGS:MEA:SET:PUL {measurement}")

    def pulse_falltime(self) -> str:
        """Mock pulse fall time measurement"""
        falltime = 0.1 + random.uniform(-0.01, 0.01)
        self.resourceManager.logMessage(1, f"Mock pulse fall time: {falltime}")
        return str(falltime)
    
    def pulse_risetime(self) -> str:
        """Mock pulse rise time measurement"""
        risetime = 0.1 + random.uniform(-0.01, 0.01)
        self.resourceManager.logMessage(1, f"Mock pulse rise time: {risetime}")
        return str(risetime)
    
    def pulse_width(self) -> str:
        """Mock pulse width measurement"""
        width = 0.45 + random.uniform(-0.05, 0.05)
        self.resourceManager.logMessage(1, f"Mock pulse width: {width}")
        return str(width)

    def gwrite(self, cmd: str):
        """Mock generic write"""
        return self.basicWrite(cmd)
        
    def gquery(self, cmd: str) -> str:
        """Mock generic query"""
        return self.basicQuery(cmd)
