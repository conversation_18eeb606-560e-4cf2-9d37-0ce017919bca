#!/usr/bin/env python3
"""
TXD Qualification Test System - Simple Build Script (Python 3.4 compatible)
Validates dependencies, compiles system, and prepares for testing
"""

import os
import sys
import json
import time
from datetime import datetime

class TXDBuilder:
    """Simple build system for TXD Qualification Test System"""
    
    def __init__(self):
        self.build_dir = "build"
        self.reports_dir = "tests/reports"
        self.build_log = []
        self.errors = []
        self.warnings = []
        
    def log(self, message, level="INFO"):
        """Log build messages"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = "[{0}] {1}: {2}".format(timestamp, level, message)
        self.build_log.append(log_entry)
        print(log_entry)
        
        if level == "ERROR":
            self.errors.append(message)
        elif level == "WARNING":
            self.warnings.append(message)
            
    def create_directories(self):
        """Create necessary build directories"""
        self.log("Creating build directories...")
        
        directories = [
            self.build_dir,
            self.reports_dir,
            "tests/unit",
            "tests/integration", 
            "tests/mocks"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                    self.log("Created directory: {0}".format(directory))
                except Exception as e:
                    self.log("Error creating directory {0}: {1}".format(directory, e), "ERROR")
                    return False
            else:
                self.log("Directory exists: {0}".format(directory))
        return True
                
    def check_python_version(self):
        """Check Python version compatibility"""
        self.log("Checking Python version...")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 4):
            self.log("Python {0}.{1} detected. Minimum required: 3.4".format(version.major, version.minor), "ERROR")
            return False
        else:
            self.log("Python {0}.{1}.{2} - OK".format(version.major, version.minor, version.micro))
            return True
            
    def check_basic_modules(self):
        """Check basic Python modules"""
        self.log("Checking basic modules...")
        
        required_modules = ['unittest', 'time', 'json', 'datetime', 'os', 'sys']
        
        for module in required_modules:
            try:
                __import__(module)
                self.log("Module '{0}' - OK".format(module))
            except ImportError:
                self.log("Module '{0}' - MISSING".format(module), "ERROR")
                return False
        
        self.log("All basic modules available")
        return True
            
    def validate_project_structure(self):
        """Validate project directory structure"""
        self.log("Validating project structure...")
        
        required_paths = [
            "Handlers",
            "Procedures", 
            "tests"
        ]
        
        missing_paths = []
        
        for path in required_paths:
            if not os.path.exists(path):
                missing_paths.append(path)
                self.log("Missing directory: {0}".format(path), "ERROR")
            else:
                self.log("Directory found: {0}".format(path))
                
        if missing_paths:
            self.log("Missing required directories: {0}".format(missing_paths), "ERROR")
            return False
        else:
            self.log("Project structure validation passed")
            return True
            
    def check_test_files(self):
        """Check for test files"""
        self.log("Checking test files...")
        
        test_files = [
            "test_unit.py",
            "test_integration.py", 
            "run_system.py",
            "generate_reports.py"
        ]
        
        found_files = 0
        for test_file in test_files:
            if os.path.exists(test_file):
                self.log("Found: {0}".format(test_file))
                found_files += 1
            else:
                self.log("Missing: {0}".format(test_file), "WARNING")
                
        self.log("Found {0}/{1} test files".format(found_files, len(test_files)))
        return True
        
    def create_build_manifest(self):
        """Create simple build manifest"""
        self.log("Creating build manifest...")
        
        manifest = {
            "build_info": {
                "timestamp": datetime.now().isoformat(),
                "python_version": "{0}.{1}.{2}".format(
                    sys.version_info.major, 
                    sys.version_info.minor, 
                    sys.version_info.micro
                ),
                "platform": sys.platform,
                "build_status": "SUCCESS" if not self.errors else "FAILED"
            },
            "errors": len(self.errors),
            "warnings": len(self.warnings)
        }
        
        manifest_path = os.path.join(self.build_dir, "build_manifest.json")
        
        try:
            with open(manifest_path, 'w') as f:
                json.dump(manifest, f, indent=2)
            self.log("Build manifest created: {0}".format(manifest_path))
            return True
        except Exception as e:
            self.log("Error creating build manifest: {0}".format(e), "ERROR")
            return False
            
    def generate_build_report(self):
        """Generate simple build report"""
        self.log("Generating build report...")
        
        report_path = os.path.join(self.reports_dir, "build_report.md")
        
        try:
            with open(report_path, 'w') as f:
                f.write("# TXD Qualification Test System - Build Report\n\n")
                f.write("**Build Date**: {0}\n".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                f.write("**Python Version**: {0}\n".format(sys.version))
                f.write("**Platform**: {0}\n\n".format(sys.platform))
                
                # Build Status
                status = "SUCCESS" if not self.errors else "FAILED"
                f.write("## Build Status: {0}\n\n".format(status))
                
                # Summary
                f.write("## Summary\n\n")
                f.write("- **Errors**: {0}\n".format(len(self.errors)))
                f.write("- **Warnings**: {0}\n".format(len(self.warnings)))
                f.write("- **Log Entries**: {0}\n\n".format(len(self.build_log)))
                
                # Errors
                if self.errors:
                    f.write("## Errors\n\n")
                    for error in self.errors:
                        f.write("- {0}\n".format(error))
                    f.write("\n")
                    
                # Warnings
                if self.warnings:
                    f.write("## Warnings\n\n")
                    for warning in self.warnings:
                        f.write("- {0}\n".format(warning))
                    f.write("\n")
                
            self.log("Build report generated: {0}".format(report_path))
            return True
            
        except Exception as e:
            self.log("Error generating build report: {0}".format(e), "ERROR")
            return False
            
    def run_build(self):
        """Run complete build process"""
        self.log("Starting TXD Qualification Test System build...")
        
        build_steps = [
            ("Creating directories", self.create_directories),
            ("Checking Python version", self.check_python_version),
            ("Checking basic modules", self.check_basic_modules),
            ("Validating project structure", self.validate_project_structure),
            ("Checking test files", self.check_test_files),
            ("Creating build manifest", self.create_build_manifest),
            ("Generating build report", self.generate_build_report)
        ]
        
        start_time = time.time()
        
        for step_name, step_function in build_steps:
            self.log("Running: {0}".format(step_name))
            
            try:
                success = step_function()
                if not success:
                    self.log("Build step failed: {0}".format(step_name), "ERROR")
                    break
            except Exception as e:
                self.log("Build step error in {0}: {1}".format(step_name, e), "ERROR")
                break
                
        build_time = time.time() - start_time
        
        if self.errors:
            self.log("Build FAILED in {0:.2f} seconds with {1} errors".format(build_time, len(self.errors)), "ERROR")
            return False
        else:
            self.log("Build COMPLETED successfully in {0:.2f} seconds".format(build_time))
            return True


def main():
    """Main build function"""
    print("=" * 60)
    print("TXD Qualification Test System - Build System")
    print("=" * 60)
    
    builder = TXDBuilder()
    success = builder.run_build()
    
    print("\n" + "=" * 60)
    if success:
        print("BUILD SUCCESS")
        print("System is ready for testing")
        print("\nNext steps:")
        print("- Run unit tests: python test_unit.py")
        print("- Run integration tests: python test_integration.py")
        print("- Run system tests: python run_system.py")
    else:
        print("BUILD FAILED")
        print("Check build report for details")
        print("Build report: tests/reports/build_report.md")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
