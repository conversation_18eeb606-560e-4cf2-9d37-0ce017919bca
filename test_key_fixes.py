#!/usr/bin/env python3
"""
Test script to validate key fixes before full system test
"""

import subprocess
import sys
import os
from pathlib import Path

# Set up environment like sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

# Test key sequences that should now pass
test_sequences = [
    # Test RFBOB fix
    ("FAR43/FAR43_A_Frequency.py", "RFBOB import"),
    # Test ATC5000NG transponderMode fix
    ("DO181/DO_181E_2_3_2_1_step1.py", "ATC5000NG transponderMode"),
    # Test N5172BSigGen indentation fix
    ("DO189/DO_189_2_2_10.py", "N5172BSigGen import"),
    # Test B4500CPwrMeter fix
    ("DO385/DO385_2_3_3_1.py", "B4500CPwrMeter getpwrmeasuremet"),
]

print("Testing key fixes...")
print("=" * 60)

for sequence, description in test_sequences:
    print(f"\nTesting {description}:")
    print(f"Sequence: {sequence}")
    
    try:
        result = subprocess.run(
            [sys.executable, f"TXDLib/Procedures/{sequence}"],
            capture_output=True,
            text=True,
            timeout=60,  # 1 minute timeout for quick test
            env=env
        )
        
        if result.returncode == 0:
            print("✅ PASSED - Fix working!")
        else:
            print("❌ FAILED")
            # Show first error line
            if result.stderr:
                error_lines = result.stderr.strip().split('\n')
                for line in error_lines[-3:]:  # Last 3 lines
                    if 'Error:' in line or 'Exception:' in line or 'TypeError:' in line:
                        print(f"   Error: {line.strip()}")
                        break
    except subprocess.TimeoutExpired:
        print("⏱️ TIMEOUT - Likely working but slow")
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

print("\n" + "=" * 60)
print("Key fixes validation complete!")
