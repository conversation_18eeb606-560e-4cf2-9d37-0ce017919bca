#!/usr/bin/env python3
"""
TXD Qualification Test System - Live Mode Implementation
Provides direct communication with actual hardware/software interfaces
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, List


class LiveMode:
    """Live mode implementation for real hardware testing"""
    
    def __init__(self):
        """Initialize live mode"""
        self.hardware_interfaces = {}
        self.handlers_path = Path("Handlers")
        self.setup_hardware_interfaces()
    
    def setup_hardware_interfaces(self):
        """Setup all real hardware interfaces"""
        # Add Handlers directory to Python path for imports
        if str(self.handlers_path) not in sys.path:
            sys.path.insert(0, str(self.handlers_path))
        
        try:
            # Import actual hardware handlers
            self.hardware_interfaces = {
                'atc5000ng': self._import_handler('ATC5000NG'),
                'rgs2000ng': self._import_handler('RGS2000NG'),
                'spectrum_analyzer': self._import_handler('N9010BSpecAn'),
                'signal_generator': self._import_handler('N5172BSigGen'),
                'power_meter': self._import_handler('B4500CPwrMeter'),
                'oscilloscope': self._import_handler('D3054Scope'),
                'dc_power_supply': self._import_handler('N6700DCPS'),
                'ni_discretes': self._import_handler('NI6528Discretes'),
                'ni_multiio': self._import_handler('NI6363MultiIO'),
                'pickering': self._import_handler('Pickering'),
                'arinc_client': self._import_handler('ARINC_Client'),
                'digital_bob': self._import_handler('DigitalBOB'),
                'rf_bob': self._import_handler('RFBOB')
            }
        except Exception as e:
            print(f"Warning: Error setting up hardware interfaces: {e}")
            print("Some hardware may not be available")
    
    def _import_handler(self, handler_name: str):
        """
        Import a hardware handler module
        
        Args:
            handler_name: Name of the handler module to import
        
        Returns:
            Imported handler module or None if import fails
        """
        try:
            handler_module = __import__(handler_name)
            return handler_module
        except ImportError as e:
            print(f"Warning: Could not import {handler_name}: {e}")
            return None
        except Exception as e:
            print(f"Error importing {handler_name}: {e}")
            return None
    
    def get_interface(self, interface_name: str):
        """
        Get hardware interface by name
        
        Args:
            interface_name: Name of the interface to retrieve
        
        Returns:
            Hardware interface object or None if not available
        """
        return self.hardware_interfaces.get(interface_name)
    
    def is_hardware_available(self) -> bool:
        """
        Check if hardware is available for testing
        
        Returns:
            True if at least some hardware interfaces are available
        """
        available_interfaces = [
            interface for interface in self.hardware_interfaces.values() 
            if interface is not None
        ]
        return len(available_interfaces) > 0
    
    def get_hardware_status(self) -> Dict[str, Any]:
        """
        Get status of all hardware interfaces
        
        Returns:
            Dictionary containing status of each hardware interface
        """
        status = {}
        
        for interface_name, interface in self.hardware_interfaces.items():
            if interface is None:
                status[interface_name] = {
                    'available': False,
                    'status': 'NOT_IMPORTED',
                    'error': 'Handler module could not be imported'
                }
            else:
                try:
                    # Try to check if hardware is connected/available
                    # This is a basic check - actual implementation would depend on each handler
                    status[interface_name] = {
                        'available': True,
                        'status': 'READY',
                        'handler_loaded': True
                    }
                except Exception as e:
                    status[interface_name] = {
                        'available': False,
                        'status': 'ERROR',
                        'error': str(e)
                    }
        
        return status
    
    def validate_hardware_connectivity(self) -> Dict[str, Any]:
        """
        Validate connectivity to all required hardware
        
        Returns:
            Dictionary containing validation results
        """
        validation_results = {
            'overall_status': 'UNKNOWN',
            'total_interfaces': len(self.hardware_interfaces),
            'available_interfaces': 0,
            'failed_interfaces': 0,
            'interface_details': {}
        }
        
        for interface_name, interface in self.hardware_interfaces.items():
            if interface is None:
                validation_results['interface_details'][interface_name] = {
                    'status': 'FAILED',
                    'reason': 'Handler not imported'
                }
                validation_results['failed_interfaces'] += 1
            else:
                try:
                    # Basic validation - check if handler has required methods
                    # Actual validation would depend on specific handler implementation
                    validation_results['interface_details'][interface_name] = {
                        'status': 'AVAILABLE',
                        'reason': 'Handler loaded successfully'
                    }
                    validation_results['available_interfaces'] += 1
                except Exception as e:
                    validation_results['interface_details'][interface_name] = {
                        'status': 'FAILED',
                        'reason': str(e)
                    }
                    validation_results['failed_interfaces'] += 1
        
        # Determine overall status
        if validation_results['available_interfaces'] == validation_results['total_interfaces']:
            validation_results['overall_status'] = 'ALL_AVAILABLE'
        elif validation_results['available_interfaces'] > 0:
            validation_results['overall_status'] = 'PARTIAL_AVAILABLE'
        else:
            validation_results['overall_status'] = 'NONE_AVAILABLE'
        
        return validation_results
    
    def get_critical_hardware_list(self) -> List[str]:
        """
        Get list of critical hardware interfaces required for testing
        
        Returns:
            List of critical hardware interface names
        """
        return [
            'atc5000ng',      # Primary test controller
            'rgs2000ng',      # Radar generator
            'spectrum_analyzer', # Signal analysis
            'signal_generator',  # Signal generation
            'power_meter'     # Power measurements
        ]
    
    def check_critical_hardware(self) -> Dict[str, Any]:
        """
        Check availability of critical hardware interfaces
        
        Returns:
            Dictionary containing critical hardware status
        """
        critical_hardware = self.get_critical_hardware_list()
        critical_status = {
            'all_critical_available': True,
            'critical_hardware_status': {},
            'missing_critical': []
        }
        
        for hardware_name in critical_hardware:
            interface = self.hardware_interfaces.get(hardware_name)
            if interface is None:
                critical_status['all_critical_available'] = False
                critical_status['missing_critical'].append(hardware_name)
                critical_status['critical_hardware_status'][hardware_name] = 'MISSING'
            else:
                critical_status['critical_hardware_status'][hardware_name] = 'AVAILABLE'
        
        return critical_status
    
    def prepare_hardware_for_testing(self) -> bool:
        """
        Prepare all hardware interfaces for testing
        
        Returns:
            True if hardware preparation successful, False otherwise
        """
        try:
            print("Preparing hardware for live mode testing...")
            
            # Check critical hardware availability
            critical_status = self.check_critical_hardware()
            
            if not critical_status['all_critical_available']:
                print("Warning: Not all critical hardware is available:")
                for missing in critical_status['missing_critical']:
                    print(f"  - Missing: {missing}")
                print("Some tests may fail or be skipped")
            
            # Additional hardware preparation steps would go here
            # For example:
            # - Initialize hardware connections
            # - Perform hardware self-tests
            # - Configure default settings
            
            print("Hardware preparation completed")
            return True
            
        except Exception as e:
            print(f"Error preparing hardware: {e}")
            return False
    
    def cleanup_hardware_after_testing(self) -> bool:
        """
        Cleanup hardware interfaces after testing
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            print("Cleaning up hardware after testing...")
            
            # Hardware cleanup steps would go here
            # For example:
            # - Disable all outputs
            # - Close connections
            # - Reset hardware to safe states
            
            print("Hardware cleanup completed")
            return True
            
        except Exception as e:
            print(f"Error during hardware cleanup: {e}")
            return False
