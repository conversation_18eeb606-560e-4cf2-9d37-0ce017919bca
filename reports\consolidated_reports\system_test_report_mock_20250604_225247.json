{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-04T22:52:47.975836", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 73, "passed": 4, "failed": 69, "errors": 0, "timeouts": 0, "success_rate": 5.47945205479452, "total_execution_time": 67.28684639930725, "start_time": "2025-06-04T22:51:40.688253", "end_time": "2025-06-04T22:52:47.975100"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0696978569030762, "start_time": "2025-06-04T22:51:40.690166", "end_time": "2025-06-04T22:51:41.759864", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9954333305358887, "start_time": "2025-06-04T22:51:41.761027", "end_time": "2025-06-04T22:51:42.756462", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9983422756195068, "start_time": "2025-06-04T22:51:42.757860", "end_time": "2025-06-04T22:51:43.756203", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9826233386993408, "start_time": "2025-06-04T22:51:43.757494", "end_time": "2025-06-04T22:51:44.740118", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9675700664520264, "start_time": "2025-06-04T22:51:44.741678", "end_time": "2025-06-04T22:51:45.709245", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9894375801086426, "start_time": "2025-06-04T22:51:45.710134", "end_time": "2025-06-04T22:51:46.699572", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9802720546722412, "start_time": "2025-06-04T22:51:46.700531", "end_time": "2025-06-04T22:51:47.680805", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9500961303710938, "start_time": "2025-06-04T22:51:47.681637", "end_time": "2025-06-04T22:51:48.631734", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.4064171314239502, "start_time": "2025-06-04T22:51:48.633196", "end_time": "2025-06-04T22:51:49.039613", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9661283493041992, "start_time": "2025-06-04T22:51:49.040348", "end_time": "2025-06-04T22:51:50.006477", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9819185733795166, "start_time": "2025-06-04T22:51:50.007634", "end_time": "2025-06-04T22:51:50.989553", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9784622192382812, "start_time": "2025-06-04T22:51:50.990662", "end_time": "2025-06-04T22:51:51.969124", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.947089433670044, "start_time": "2025-06-04T22:51:51.970576", "end_time": "2025-06-04T22:51:52.917667", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9778778553009033, "start_time": "2025-06-04T22:51:52.918854", "end_time": "2025-06-04T22:51:53.896733", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9857175350189209, "start_time": "2025-06-04T22:51:53.898414", "end_time": "2025-06-04T22:51:54.884133", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9419560432434082, "start_time": "2025-06-04T22:51:54.885421", "end_time": "2025-06-04T22:51:55.827378", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9648387432098389, "start_time": "2025-06-04T22:51:55.828965", "end_time": "2025-06-04T22:51:56.793804", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9464762210845947, "start_time": "2025-06-04T22:51:56.795256", "end_time": "2025-06-04T22:51:57.741733", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9756662845611572, "start_time": "2025-06-04T22:51:57.742975", "end_time": "2025-06-04T22:51:58.718642", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9492418766021729, "start_time": "2025-06-04T22:51:58.719667", "end_time": "2025-06-04T22:51:59.668909", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 50, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9722585678100586, "start_time": "2025-06-04T22:51:59.669764", "end_time": "2025-06-04T22:52:00.642023", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9463725090026855, "start_time": "2025-06-04T22:52:00.643000", "end_time": "2025-06-04T22:52:01.589373", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9803087711334229, "start_time": "2025-06-04T22:52:01.590615", "end_time": "2025-06-04T22:52:02.570924", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 53, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9781222343444824, "start_time": "2025-06-04T22:52:02.572572", "end_time": "2025-06-04T22:52:03.550695", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9606728553771973, "start_time": "2025-06-04T22:52:03.551608", "end_time": "2025-06-04T22:52:04.512282", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 1.006328821182251, "start_time": "2025-06-04T22:52:04.513733", "end_time": "2025-06-04T22:52:05.520063", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0130631923675537, "start_time": "2025-06-04T22:52:05.520954", "end_time": "2025-06-04T22:52:06.534020", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.985375165939331, "start_time": "2025-06-04T22:52:06.535460", "end_time": "2025-06-04T22:52:07.520835", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 1.010310173034668, "start_time": "2025-06-04T22:52:07.522108", "end_time": "2025-06-04T22:52:08.532420", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.977048397064209, "start_time": "2025-06-04T22:52:08.533904", "end_time": "2025-06-04T22:52:09.510953", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0179297924041748, "start_time": "2025-06-04T22:52:09.512329", "end_time": "2025-06-04T22:52:10.530259", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.98097825050354, "start_time": "2025-06-04T22:52:10.531901", "end_time": "2025-06-04T22:52:11.512879", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 39, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0149445533752441, "start_time": "2025-06-04T22:52:11.514071", "end_time": "2025-06-04T22:52:12.529015", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 1.033567190170288, "start_time": "2025-06-04T22:52:12.530432", "end_time": "2025-06-04T22:52:13.564000", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9844133853912354, "start_time": "2025-06-04T22:52:13.565332", "end_time": "2025-06-04T22:52:14.549745", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9762454032897949, "start_time": "2025-06-04T22:52:14.551787", "end_time": "2025-06-04T22:52:15.528033", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 101, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9558920860290527, "start_time": "2025-06-04T22:52:15.528878", "end_time": "2025-06-04T22:52:16.484771", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 52, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9701573848724365, "start_time": "2025-06-04T22:52:16.485961", "end_time": "2025-06-04T22:52:17.456120", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9542937278747559, "start_time": "2025-06-04T22:52:17.457505", "end_time": "2025-06-04T22:52:18.411799", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9815659523010254, "start_time": "2025-06-04T22:52:18.412909", "end_time": "2025-06-04T22:52:19.394476", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.952185869216919, "start_time": "2025-06-04T22:52:19.395567", "end_time": "2025-06-04T22:52:20.347755", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0122604370117188, "start_time": "2025-06-04T22:52:20.349183", "end_time": "2025-06-04T22:52:21.361443", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9585380554199219, "start_time": "2025-06-04T22:52:21.362780", "end_time": "2025-06-04T22:52:22.321309", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9671230316162109, "start_time": "2025-06-04T22:52:22.322401", "end_time": "2025-06-04T22:52:23.289525", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.989661455154419, "start_time": "2025-06-04T22:52:23.290826", "end_time": "2025-06-04T22:52:24.280488", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9629106521606445, "start_time": "2025-06-04T22:52:24.281616", "end_time": "2025-06-04T22:52:25.244528", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9720277786254883, "start_time": "2025-06-04T22:52:25.245643", "end_time": "2025-06-04T22:52:26.217671", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9635822772979736, "start_time": "2025-06-04T22:52:26.218696", "end_time": "2025-06-04T22:52:27.182278", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 40, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9582295417785645, "start_time": "2025-06-04T22:52:27.183304", "end_time": "2025-06-04T22:52:28.141533", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.12087798118591309, "start_time": "2025-06-04T22:52:28.142879", "end_time": "2025-06-04T22:52:28.263758", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.14605283737182617, "start_time": "2025-06-04T22:52:28.264519", "end_time": "2025-06-04T22:52:28.410572", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.11762475967407227, "start_time": "2025-06-04T22:52:28.411099", "end_time": "2025-06-04T22:52:28.528723", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9709527492523193, "start_time": "2025-06-04T22:52:28.529869", "end_time": "2025-06-04T22:52:29.500823", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_3.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9831125736236572, "start_time": "2025-06-04T22:52:29.501670", "end_time": "2025-06-04T22:52:30.484784", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9569206237792969, "start_time": "2025-06-04T22:52:30.485670", "end_time": "2025-06-04T22:52:31.442591", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.977186918258667, "start_time": "2025-06-04T22:52:31.444100", "end_time": "2025-06-04T22:52:32.421283", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 57, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9680271148681641, "start_time": "2025-06-04T22:52:32.422321", "end_time": "2025-06-04T22:52:33.390349", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 70, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9707033634185791, "start_time": "2025-06-04T22:52:33.391471", "end_time": "2025-06-04T22:52:34.362175", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.11248660087585449, "start_time": "2025-06-04T22:52:34.363270", "end_time": "2025-06-04T22:52:34.475757", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9764838218688965, "start_time": "2025-06-04T22:52:34.476416", "end_time": "2025-06-04T22:52:35.452901", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9612958431243896, "start_time": "2025-06-04T22:52:35.454050", "end_time": "2025-06-04T22:52:36.415348", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9700818061828613, "start_time": "2025-06-04T22:52:36.416463", "end_time": "2025-06-04T22:52:37.386546", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9709353446960449, "start_time": "2025-06-04T22:52:37.387456", "end_time": "2025-06-04T22:52:38.358392", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9864494800567627, "start_time": "2025-06-04T22:52:38.359669", "end_time": "2025-06-04T22:52:39.346119", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_A_Frequency.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9554004669189453, "start_time": "2025-06-04T22:52:39.347079", "end_time": "2025-06-04T22:52:40.302480", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_B_Supression.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9497675895690918, "start_time": "2025-06-04T22:52:40.303433", "end_time": "2025-06-04T22:52:41.253201", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.981295108795166, "start_time": "2025-06-04T22:52:41.254548", "end_time": "2025-06-04T22:52:42.235845", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_D_Power.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9400124549865723, "start_time": "2025-06-04T22:52:42.237144", "end_time": "2025-06-04T22:52:43.177157", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_E_Diversity.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9637608528137207, "start_time": "2025-06-04T22:52:43.178638", "end_time": "2025-06-04T22:52:44.142415", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9355876445770264, "start_time": "2025-06-04T22:52:44.143763", "end_time": "2025-06-04T22:52:45.079351", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 62, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9728548526763916, "start_time": "2025-06-04T22:52:45.080465", "end_time": "2025-06-04T22:52:46.053321", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.95279860496521, "start_time": "2025-06-04T22:52:46.054488", "end_time": "2025-06-04T22:52:47.007287", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9661810398101807, "start_time": "2025-06-04T22:52:47.008319", "end_time": "2025-06-04T22:52:47.974501", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_J_Squitter.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\ate_rm.py\", line 3, in <module>\n    clr.AddReference(\"C:\\\\Program Files\\\\Honeywell\\\\Lobster4\\\\ExternalLibraries\\\\Observer\\\\Interface\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSystem.IO.FileNotFoundException: Unable to find assembly 'C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface'.\n\n   at Python.Runtime.CLRModule.AddReference(String name)\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 1, "failed": 10, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 67.28684639930725, "average_sequence_time": 0.9217376219083185, "sequences_per_hour": 3905.666769407485, "optimization_effectiveness": {"optimization_success_rate": 5.47945205479452, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 69, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 10, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 69 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}