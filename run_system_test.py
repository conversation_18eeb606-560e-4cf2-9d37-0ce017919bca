#!/usr/bin/env python3
"""
TXD Qualification Test System - Main System Test Runner
Execute all sequence files with mock/live mode support and consolidated reporting
"""

import sys
import argparse
import os
from pathlib import Path

# Add system_test to Python path
sys.path.insert(0, str(Path(__file__).parent / "system_test"))

from system_test import <PERSON><PERSON><PERSON><PERSON><PERSON>, MockMode, LiveMode, ReportGenerator
from system_test.config import MockConfig, LiveConfig


def main():
    """Main system test execution function"""
    parser = argparse.ArgumentParser(
        description='TXD Qualification Test System - System Test Runner',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run all sequences in mock mode
  python run_system_test.py --mode mock --procedures all
  
  # Run specific procedures in live mode
  python run_system_test.py --mode live --procedures DO282,FAR43
  
  # Run with JSON report only
  python run_system_test.py --mode mock --procedures DO189 --report-format json
  
  # List available procedures
  python run_system_test.py --list-procedures
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['mock', 'live'], 
        default='mock',
        help='Execution mode: mock (no hardware) or live (real hardware)'
    )
    
    parser.add_argument(
        '--procedures',
        help='Comma-separated list of procedures to run (e.g., DO282,FAR43) or "all" for all procedures'
    )
    
    parser.add_argument(
        '--report-format',
        choices=['json', 'markdown', 'both'],
        default='both',
        help='Report format to generate'
    )
    
    parser.add_argument(
        '--output-dir',
        default='reports',
        help='Directory to save reports (default: reports)'
    )
    
    parser.add_argument(
        '--list-procedures',
        action='store_true',
        help='List available procedures and exit'
    )
    
    parser.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration and exit'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Handle special commands
    if args.list_procedures:
        list_procedures()
        return 0
    
    if args.validate_config:
        return validate_configuration(args.mode)
    
    # Validate arguments
    if not args.procedures:
        print("Error: --procedures argument is required")
        print("Use --list-procedures to see available procedures")
        return 1
    
    # Parse procedures
    if args.procedures.lower() == 'all':
        procedures = None  # None means all procedures
    else:
        procedures = [p.strip() for p in args.procedures.split(',')]
    
    # Execute system test
    return execute_system_test(
        mode=args.mode,
        procedures=procedures,
        report_format=args.report_format,
        output_dir=args.output_dir,
        verbose=args.verbose
    )


def list_procedures():
    """List all available procedures"""
    print("TXD Qualification Test System - Available Procedures")
    print("=" * 60)
    
    runner = SequenceRunner(mode='mock')
    sequences = runner.discover_sequences()
    
    if not sequences:
        print("No procedures found in Procedures directory")
        return
    
    total_sequences = 0
    for procedure, sequence_files in sequences.items():
        print("\n{0}:".format(procedure))
        for sequence_file in sequence_files:
            print("  - {0}".format(sequence_file))
        total_sequences += len(sequence_files)

    print("\nTotal: {0} procedures, {1} sequences".format(len(sequences), total_sequences))
    print("\nUsage examples:")
    print("  python run_system_test.py --mode mock --procedures all")
    print("  python run_system_test.py --mode live --procedures {0}".format(','.join(list(sequences.keys())[:2])))


def validate_configuration(mode):
    """Validate configuration for specified mode"""
    print("TXD Qualification Test System - Configuration Validation ({0} mode)".format(mode.upper()))
    print("=" * 70)

    if mode == 'mock':
        config = MockConfig.get_configuration_summary()
        print("Mock Mode Configuration:")
        print("  - Mode: {0}".format(config['mode']))
        print("  - Optimization Status: {0}".format(config['optimization_status']))
        print("  - Mock Devices: {0}".format(config['total_mock_devices']))
        print("  - Error Simulation: {0}".format(config['error_simulation']))
        print("\nTiming Optimizations:")
        for opt, value in config['timing_optimizations'].items():
            print("  - {0}: {1}".format(opt.replace('_', ' ').title(), value))
        print("\n✅ Mock mode configuration is valid")
        return 0
        
    elif mode == 'live':
        config = LiveConfig.get_configuration_summary()
        validation = LiveConfig.validate_configuration()

        print("Live Mode Configuration:")
        print("  - Mode: {0}".format(config['mode']))
        print("  - Optimization Status: {0}".format(config['optimization_status']))
        print("  - Required Hardware: {0}".format(len(config['required_hardware'])))
        print("  - Optional Hardware: {0}".format(len(config['optional_hardware'])))
        print("  - Total Interfaces: {0}".format(config['total_hardware_interfaces']))

        print("\nSafety Features:")
        for feature, enabled in config['safety_features'].items():
            status = "✅ Enabled" if enabled else "❌ Disabled"
            print("  - {0}: {1}".format(feature.replace('_', ' ').title(), status))

        print("\nTiming Optimizations:")
        for opt, value in config['timing_optimizations'].items():
            print("  - {0}: {1}".format(opt.replace('_', ' ').title(), value))

        print("\nValidation Results:")
        if validation['valid']:
            print("✅ Live mode configuration is valid")
        else:
            print("❌ Live mode configuration has errors")

        if validation['warnings']:
            print("\nWarnings:")
            for warning in validation['warnings']:
                print("  ⚠️  {0}".format(warning))

        if validation['errors']:
            print("\nErrors:")
            for error in validation['errors']:
                print("  ❌ {0}".format(error))

        return 0 if validation['valid'] else 1


def execute_system_test(mode, procedures, report_format, output_dir, verbose):
    """Execute the system test"""
    print("TXD QUALIFICATION TEST SYSTEM - SYSTEM TEST EXECUTION")
    print("=" * 70)
    print("Execution Mode: {0}".format(mode.upper()))
    print("Procedures: {0}".format('All' if procedures is None else ', '.join(procedures)))
    print("Report Format: {0}".format(report_format))
    print("Output Directory: {0}".format(output_dir))
    print("=" * 70)
    
    try:
        # Initialize mode-specific setup
        if mode == 'mock':
            mock_mode = MockMode()
            if verbose:
                print("Mock mode initialized - all hardware interfaces mocked")
        else:
            live_mode = LiveMode()
            if not live_mode.prepare_hardware_for_testing():
                print("❌ Hardware preparation failed")
                return 1
            if verbose:
                print("Live mode initialized - real hardware interfaces active")
        
        # Initialize sequence runner
        runner = SequenceRunner(mode=mode)
        
        # Execute all sequences
        results = runner.execute_all_sequences(procedures)
        
        if not results:
            print("❌ No sequences were executed")
            return 1
        
        # Get execution summary
        summary = runner.get_execution_summary()
        
        # Generate reports
        report_generator = ReportGenerator(output_dir)
        
        print("\nGenerating reports...")
        generated_files = report_generator.generate_consolidated_report(
            summary, results, report_format
        )
        
        # Print quick summary
        quick_summary = report_generator.generate_quick_summary(summary)
        print(quick_summary)
        
        # Print report file locations
        print("Generated Reports:")
        for format_type, file_path in generated_files.items():
            print("  - {0}: {1}".format(format_type.upper(), file_path))

        # Cleanup for live mode
        if mode == 'live':
            live_mode.cleanup_hardware_after_testing()

        # Return exit code based on results
        failed_count = summary.get('failed', 0) + summary.get('errors', 0) + summary.get('timeouts', 0)
        return 0 if failed_count == 0 else 1

    except KeyboardInterrupt:
        print("\n❌ System test interrupted by user")
        return 1
    except Exception as e:
        print("❌ System test failed with error: {0}".format(e))
        if verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
