# -*- coding: utf-8 -*-
"""
Created on Tues April 28 21:20:30 2020

@author: E589493
         K. <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Interrogator Frequency Stability, Section 2.2.7
             
             "Second Pulse:
             At the peak of the second transmitter pulse, the interrogator
             output shall be:
             a. At least 250 W for equipment intended for installation in 
             aircraft which operate above 18,000 feet.
             b. At least 50 W for equipment intended for installation
             in aircraft whick operate below 18,00 ft.

             First Pulse:
             At the peak of the first transmitter pulse, the interrogator
             output power shall be within 1 dB of the peak power of the 
             second pulse."


INPUTS:      cable loss, Power Meter, ARINC Server
OUTPUTS:     nPulse (number of Pulse received from power meter), 
             pwr_arr: ypower array List of 4 contains max and avg power of peak 1 and 2 
             PP_W: Peak Power in Watts
             MeasurePowerDifference difference in power between peak 1 and 2

HISTORY:

0
6/10/2020   KF    Initial Release.
6/12/2020   AS    Found pulses using peaks instead of edges, added logic 
                  to determine correct peaks for different channels
                  Added functionality for calculating total PP including cable loss as well as
                  checking if MOPS criteria is met.
6/16/2020   AS    Set cable loss ratio to 40 and manipulated measurePeakePowerWatts to not
                  factor in cable loss
03/10/2021   MRS   Updates for new handlers and Lobster.                                 
"""

#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers import B4500CPwrMeter
       

Std_PowelLevel = -70.0  #Standard DME Signal Power Level
Std_DistanceRange = 34  #Standard DME Reply Signal Level
Std_SquitterRate = 2700 #Standard DME Squitter Rate

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def init_DME_Standard(rm, cable_loss, atc, ARINC):
    """ Sets DME Standard Conditions to DME Channel 56X with VOR Pair 0 111.9MHz at -70dBm adn 34nm, Squidder rate 
    of 2700 """

    rm.logMessage(0,"*Test_189_2_2_7 - Initializing ATC into DME mode ") 
    ARINC.writeChannel(111.90)
    atc.DMEMode()
    #atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))

def atc_power_level(Step_PowerLevel, atc):
    """ This function set teh next power level to be measured eg -50 dBm for DO-189 2.2.1b"""
    cmd = ':ATC:DME:POWER ' + str(Step_PowerLevel)
    atc.write(cmd)
    

def set_VOR_PAIR5(atc, ARINC):
    """ This FUnction tunes the DME and sets the ATC to VOR Pair5. """
    ARINC.writeChannel(117.95)
    time.sleep(5)
    #Turn Off RF
    atc.gwrite(':ATC:DME:STOP')
    atc.gwrite(":ATC:DME:CHANNEL:MODE 5VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 117.9")
    time.sleep(5)
    atc.gwrite(':ATC:DME:START')
    time.sleep(5)

def cableLossRatio(cable_loss):
    """ This function returns the Cable Loss Ratio. Pass in the cable loss as the argument """
    return 10**(cable_loss/10)

def DME_pw_setup(rm,pw):
    """ Basic Power Meter setup """
    #Reset Powermeter
    #Initialize to defaults
    rm.logMessage(0,"*Test_189_2_2_7 -  Setting up PowerMeter ") 
    pw.Reset()
    pw.autoset()        
    time.sleep(5)

    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    pw.setCalculateUnits('dBm')
    pw.setCalculate1_on('ON')
    
    #Trigger
    pw.basicWrite("TRIGger:POSition MIDDLE")
    pw.basicWrite("TRIGger:SOURce CH1")
    pw.basicWrite("TRIGger:SLOPe POS")
    pw.basicWrite("TRIGger:MODE NORMAL")  
    pw.basicWrite("TRIGger:LEV -20.0")

    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    TimeBase = '20e-6'                
    pw.setTimeBase(TimeBase)
    pw.setFrequency1('1.080e9')

    #Get the power for each pulse
    nPulse = int(pw.getnumpulses('18'))
     
    rm.logMessage(0,"*Test_189_2_2_7 - Looking for Peaks ") 
    for i in range(5):
        nPulse = pw.findpeaks('-20')
        if (nPulse == 2 or nPulse == 3):
            rm.logMessage(0,"*Test_189_2_2_7 - Found Peaks. Number of Pulses = " + str(nPulse))
            return nPulse
    return 0

def measurePP(rm, pw, nPulse):
    """ Returns peak power (dBm) of pulse number"""
    #Get the power for each pulse
    rm.logMessage(0,"*Test_189_2_2_7 - Getting Power for Each Pulse ") 

    #comment next line out when running from teststand
    #pw.plotpeaks('18','DME Peaks')


    pwr_arr = []
    if (nPulse == 3):
        # measure pulse 0
        print("\n\nPULSE : ",0)
        pw.setpulsepositions(0)        # set markers for pulse
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
        pwr_str = pwr.split(',')
        pwr_arr.append(float(pwr_str[1])) # average and max
        pwr_arr.append(float(pwr_str[3]))
        
        # ignore pulse 1 
        # measure pulse 2
        print("\n\nPULSE : ",2)
        pw.setpulsepositions(2)        # set markers for pulse
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
        pwr_str = pwr.split(',')
        pwr_arr.append(float(pwr_str[1])) # average and max
        pwr_arr.append(float(pwr_str[3]))

        rm.logMessage(0,("*Test_189_2_2_7 - P1 Avg: " + str(pwr_arr[0]) + ", P1 Max: " + str(pwr_arr[1]) + ", P2 Avg: " + str(pwr_arr[2]) + ", P2 Max: " + str(pwr_arr[3])) )
        return pwr_arr 
    else: 
        # measure all pulses
        for i in range(int(nPulse)):
            print("\n\nPULSE : ",i)
            pw.setpulsepositions(i)        # set markers for pulse i
            time.sleep(1)
            pwr = pw.getpwrmeasuremet()
            pwr_str = pwr.split(',')
            pwr_arr.append(float(pwr_str[1])) # average and max
            pwr_arr.append(float(pwr_str[3]))
            rm.logMessage(0,"*Test_189_2_2_7 - P" + str(i+1)+ " Avg: " + pwr_str[1] + ", P"+str(i+1)+" Max: " + pwr_str[3])  

            "P1 Avg: " + str(pwr_arr[0]) + ", P1 Max: " + str(pwr_arr[1])
            #tme = pw.gettimemeasurement(TimeBase)   #not necessary but gives approx pulse width
        return pwr_arr

def measurePeakePowerWatts(P1_PP, P2_PP, cable_Loss_Ratio):
    """ Claculates the Peak Power in Watt with the cable loss ratio factored in """
    if (P1_PP >= P2_PP):
        PP_W = 10**((P1_PP - 30)/10)
        return PP_W
    else:
        PP_W = 10**((P2_PP - 30)/10)
        return PP_W

def measurePowerDifference(P1_PP, P2_PP):
    """ Retruns the absolute value of the power difference between Pulse 1 and 2 """
    return abs(P1_PP - P2_PP)

# def calcERP(cald_element_list):
#     """ Effective Radiated Power Equation with corrected element power (dBm) passed in as array/list """
#     pwr = 0.00
#     for e in cald_element_list:
#         # Sum dBm to Watts
#         pwr += (0.001 * 10**(e/10))
#     # Watts back to dBm
#     pwr = 10 * math.log10(pwr * 1000)
#     return pwr

# def chanDeviation(cald_element_list):
#     """ Maximum channel deviation calculation with corrected element power (dBm) passed in as an array/list """
#     return (max(cald_element_list) - min(cald_element_list))

def calcTotalPP(P1_PP, P2_PP, cableLoss):
    """ Returns array with total pulse power (dBm) for each pulse by adding cable loss"""
    totalPP = []
    totalPP.append(P1_PP + cableLoss)
    totalPP.append(P1_PP + cableLoss)
    return totalPP

def inThreshold(num):
    """ Returns True if passes the test, False otherwise"""
    if(num < 1):
        return True
    else:
        return False

def printResults(rm,mPP, tPP, PPw, pDiff):
    """ prints results of program """
    rm.logMessage(0,"Peak Averages and Max: " + str(mPP))
    rm.logMessage(0,"Total PP:" + str(tPP))
    rm.logMessage(0,"Peake Power Watts:" + str(PPw))
    rm.logMessage(0,"Power Difference: " + str(pDiff))
	
	
##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_DO189_2_2_7(rm,ARINC,pwr,atc,PathLoss):
    """ DO-189, Pulse Power Characteristics: Sect 2.2.7"""
    
    rm.logMessage(2,"*** DO-189, Pulse Power Characteristics: Sect 2.2.7 ***")
    
    #Initialize Results
    mPP1 = []
    tPP1 = [] 
    PPw1 = [] 
    pDiff1 = []
    mPP2 = []
    tPP2 = [] 
    PPw2 = [] 
    pDiff2 = []
    results = []
	
	#Set Calbeloss
    cableLoss = PathLoss
	
    init_DME_Standard(rm, cableLoss, atc, ARINC)
    cRatio = cableLossRatio(cableLoss)
    nPulse = DME_pw_setup(rm,pwr)

    time.sleep(1)
    # Get Channel X Measurements
    mPP1 = measurePP(rm, pwr, nPulse)
    tPP1 = calcTotalPP(mPP1[1], mPP1[3], cableLoss)
    PPw1 = measurePeakePowerWatts(tPP1[0], tPP1[1], cRatio)
    pDiff1 = measurePowerDifference(mPP1[1], mPP1[3])
    print("\nChannel 111.90\n")
    printResults(rm, mPP1, tPP1, PPw1, pDiff1)

    # Switch to 117.95 
    set_VOR_PAIR5(atc, ARINC)
    time.sleep(5)
    data = ARINC.getChannel_035()

    if(data != 117.95):
        print("Failed to Switch to 117.95")
    else:
        # Get Channel Y Measurements
        nPulse = DME_pw_setup(rm,pwr)
        mPP2 = measurePP(rm, pwr, nPulse)
        tPP2 = calcTotalPP(mPP2[1], mPP2[3], cableLoss)
        PPw2 = measurePeakePowerWatts(tPP2[0], tPP2[1], cRatio)
        pDiff2 = measurePowerDifference(mPP2[1], mPP2[3])
        print("\nChannel 117.95\n")
        printResults(rm, mPP2, tPP2, PPw2, pDiff2)
                

    #results back to TestStand
    r1 = mPP1 + tPP1
    r1.append(PPw1)
    r1.append(pDiff1)
    r2= mPP2 + tPP2 
    r2.append(PPw2) 
    r2.append(pDiff2)
    results = r1 + r2
    rm.logMessage(0,"Results: " + str(results))
    
    rm.logMessage(2,"Done, Closing Session")
   
    return results



if __name__ == "__main__":
    #Initialize Intruuments
    rm = ate_rm()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)
	
    #Initialize Power Meter
    pwr = B4500CPwrMeter(rm)
    pwr.Reset()


    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    
    PathLoss = 48.0

    Test_DO189_2_2_7(rm,ARINC,pwr,atc,PathLoss)
    
    
    #Close the ATC and Power Meter. 
    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    pwr.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
