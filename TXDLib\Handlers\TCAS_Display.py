# -*- coding: utf-8 -*-
"""
Created on Fri Jul  3 11:59:28 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>i
         CNS Qualification Test Group
         
Description:
    This class queries the ARINC_Server program and provides a display of TCAS
    a display plot of the returned intruders.
    
    
Inputs:  see class methods
Outputs: see class methods

History:

07/10/2020   MRS  Initial Release
"""

from PyQt5 import QtWidgets, uic
from PyQt5.QtCore import Qt, QTimer,QThread, pyqtSignal
from pyqtgraph import PlotWidget
import pyqtgraph as pg
import socket
import numpy as np
import sys
sys.path.append("../../common")
import re
import datetime
import time 



TCP_IP = '127.0.0.1'
TCP_PORT = 20633
BUFFER_SIZE = 2048
global xp
global yp
global ap
global itr
global rng
global alt
global brg
xp = []
yp = []
ap = []
itr = []
rng = []
alt = []
brg = []

class MainWindow(QtWidgets.QMainWindow):
    
    #resultsFile

    def __init__(self, *args, **kwargs):
        super(MainWindow, self).__init__(*args, **kwargs)

        #Load the UI Page
        uic.loadUi('TCAS_Display.ui', self)
        self.initUI()
        
    def initUI(self):
        #Status Bar
        #TODO: open file (self.file)
        self.resultsFile  = open("ContMonResults.txt", "w")
        
        self.statusBar()
        self.statusBar().showMessage('Ready: Use Mouse to Pan/Zoom!')

        #Clear existing plots
        self.plotWidget.clear()
        #Add Background colour to white
        #self.plotWidget.setBackground('')
        
        #self.plotWidget.plot = pg.plot()
        self.plotWidget.setAspectLocked()
        
        # Add polar grid lines
        self.plotWidget.addLine(x=0, pen=0.2)
        self.plotWidget.addLine(y=0, pen=0.2)
        for r in range(8, 80, 20):
            circle = pg.QtGui.QGraphicsEllipseItem(-r, -r, r * 2, r * 2)
            circle.setPen(pg.mkPen(0.2))
            self.plotWidget.addItem(circle)
         
        # add labels
        t10 = pg.TextItem(text="10nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t10,ignoreBounds = True)
        t10.setPos(0, 10)
        t20 = pg.TextItem(text="20nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t20,ignoreBounds = True)
        t20.setPos(0, 30)
        t40 = pg.TextItem(text="40nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t40,ignoreBounds = True)
        t40.setPos(0, 50)
        t60 = pg.TextItem(text="60nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t60,ignoreBounds = True)
        t60.setPos(0, 70)
        
        #timer, used for Continuous (2.0 secs) function        
        self.timer1 = QTimer()
        self.timer1.timeout.connect(self.timer1Event)
        self.timer1.start(2000)

        #set up Thread to Read Arinc Data       
        self.thread1 = Read_Arinc_Data()
        self.thread1.done_signal.connect(self.process_graph)
        

    #Timer Event Handler    
    def timer1Event(self):
        global xp
        global yp
        global ap
        global itr
        global rng
        global alt
        global brg


        #print ('Timer!, Start Thread1')      
        self.statusBar().showMessage('Refreshing Display!')
        self.thread1.start()
    
    def process_graph(self):
        self.statusBar().showMessage('Ready: Use Mouse to Pan/Zoom!')
        
        #plot the intruder
        self.plotWidget.clear()

        # Add polar grid lines
        self.plotWidget.addLine(x=0, pen=0.2)
        self.plotWidget.addLine(y=0, pen=0.2)
        for r in range(8, 80, 20):
            circle = pg.QtGui.QGraphicsEllipseItem(-r, -r, r * 2, r * 2)
            circle.setPen(pg.mkPen(0.2))
            self.plotWidget.addItem(circle)
         
        # add labels
        t10 = pg.TextItem(text="10nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t10,ignoreBounds = True)
        t10.setPos(0, 10)
        t20 = pg.TextItem(text="20nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t20,ignoreBounds = True)
        t20.setPos(0, 30)
        t40 = pg.TextItem(text="40nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t40,ignoreBounds = True)
        t40.setPos(0, 50)
        t60 = pg.TextItem(text="60nm", color='r', anchor=(0.5, 0.1), angle=0)
        self.plotWidget.addItem(t60,ignoreBounds = True)
        t60.setPos(0, 70)
       
        #plot the intruders
        self.s1 = pg.ScatterPlotItem(size=20, pen=pg.mkPen(None), brush=pg.mkBrush(255, 255, 255, 120))
        print("Targets Returned",len(xp))
        #print ("position: ",xp,yp)
        self.s1.addPoints(x=xp,y=yp)
        self.plotWidget.addItem(self.s1)
        # add the labels
        for x in range(len(xp)):
            ttxt = str(ap[x])
            talt = pg.TextItem(text=ttxt, color='w', anchor=(0.5, -0.3), angle=0)
            self.plotWidget.addItem(talt,ignoreBounds = True)
            talt.setPos(xp[x], yp[x])


        print("Target Data----------------------------------------------------------- ")
        print(rng)
        print(brg)
        print(alt)
        
        #print(matchIntruders(self.resultsFile))

        
    # timerEvent()

    #Timer Stop and thread stop
    def timer1Stop(self):
        self.timer1.stop()
        self.thread1.stop()
        self.resultsFile.close()
    # timerStop()
    
    
""" ***************************************************************************
*******************************************************************************
*************************************************************************** """

# Read_Arinc_Data Thread  --thread1.
#        self.thread1 = Read_Arinc_data()
#        self.connect(self.thread1, ,self.done_cont_pm)
class Read_Arinc_Data(QThread):

    done_signal = pyqtSignal()

    def __init__(self, parent = None):
        QThread.__init__(self, parent) 
        
    def __del__(self):
        self.exiting = True
        self.wait()

    def stop(self):
        self.terminate()
 
    #Connect to Server and Get TCAS Data    
    def run(self):
        global xp
        global yp
        global res
        global itr
        global rng
        global alt
        global brg
        xp = []
        yp = []
        itr = []
        rng = []
        alt = []
        brg = []   
        
        #create the socket
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        except socket.error:
            print ('Failed to create socket')
            
        #connect and send/receive data from server
        try:    
            s.connect((TCP_IP, TCP_PORT))
            
            itr = []
            rng = []
            alt = []
            brg = []
            
            #send/receive data
            s.send(b"READ,TCAS_OUT,INT")
            data = s.recv(BUFFER_SIZE)
            
            #send/receive data
            s.send(b"READ,TCAS_OUT,ALL")
            data = s.recv(BUFFER_SIZE)
            
            #convert bytes received from server back to string
            data_s = data.decode('utf-8')
            #find all occurances of '-I:'
            #print("retrun string: ",data_s)
            res = [i.start() for i in re.finditer("-I:", data_s)]
            
            
            #get all intruders, ranges, bearings, altitudes
            for x in range(len(res)):
                ss = data_s[res[x]:]
                sss = ss.split(',')
                #take the first 4 substrings as itr,rng,alt,brg
                ssss = sss[0][3:]       #intruder
                itr.append(int(ssss))
                ssss = sss[1][2:]       #range
                rng.append(float(ssss))
                ssss = sss[2][2:]       #altitude
                alt.append(float(ssss))
                ssss = sss[3][2:]       #bearing
                brg.append(float(ssss))
            
                
            #convert polar to rectangular
            for x in range(len(res)):
                xx = rng[x]*np.sin(brg[x]*np.pi/180.)
                yy = rng[x]*np.cos(brg[x]*np.pi/180.)
                aa = alt[x]
                xp.append(xx)
                yp.append(yy)
                ap.append(aa)
            
        except socket.error:
            print('Failed to connect to server')
        
        finally:
            #Close Connection
            s.close()     
            self.done_signal.emit()
               
        
# Read_Arinc_Server()
        
""" ***************************************************************************
****                          HELPER FUNCTIONS                           ******
*************************************************************************** """
        
def matchIntruders(resultsFile):
    """ Parses intruder data, identifies whether MOPS have been met
    Returns true if correct number of planes as well as data match MOPs, 
    false otherwise """
    
    if(itr == []):
        print("ERROR: NOT CONNECTED")
        reportError("No connection. No intruders recieved.", resultsFile)
        return False

    # place holder to track if each intruder is found
    # 0 signifies that no matching intruder has been found.
    # 1 signifies that it has been found.
    intrudersFound = [0,0,0,0,0,0]
    
    # iterate through each intruder
    for x in range(6):
        matched = False
        
        # get data for intruder x
        rnge = rng[x]
        bearing = brg[x]
        altitude = alt[x] + 10000    # factors in plane's altitude
        
        # loops through all expected intruder data to see if any match intruder found
        for i in range(6):
            if(isIntruder(rnge, bearing, altitude, i)):
                if(intrudersFound[i] == 0):
                    # sets index to 1 to indicate this intruder has been found
                    intrudersFound[i] = 1
                    matched = True
                    break
                else:
                    # intruder is repeated; fail
                    reportError("Intruder "+ str(x+1) + " matched expected data for Intruder " + str(x+1) + " but Intruder " + str(x+1) +" already matched with another", resultsFile)
                    return False
        # intruder doesn't match expected data; fail
        if(not matched):
            reportError("Intruder " + str(x+1) + " did not match with any expected data", resultsFile)
            return False
    # all intruders match up with expected data; pass
       
    return True


def isIntruder(rnge, bearing, altitude, num):
    """ Returns True if the intruder passed in matches the requirements for 
    intruder number passed. 
    Parameters: (range, bearing, altitude, intruder number to test) """
    
    if(num == 0):
        return (rnge >= 1.5 and rnge <= 2.5) and (bearing >= -5 and bearing <= 5) and (altitude >= 11900 and altitude <= 12100)
    elif (num == 1):
        return (rnge >= 0.5 and rnge <= 1.5) and (bearing >= 85 and bearing <= 95) and (altitude >= 10400 and altitude <= 10600)
    elif (num == 2):
        return (rnge >= 14.5 and rnge <= 15.5) and (bearing >= 175 and bearing <= 185) and (altitude >= 11900 and altitude <= 12100)
    elif (num == 3):
        return (rnge >= 9.5 and rnge <= 10.5) and (bearing >= -95 and bearing <= -85) and (altitude >= 10900 and altitude <= 11100)
    elif (num == 4):
        return (rnge >= 34.5 and rnge <= 35.5) and (bearing >= 10 and bearing <= 20) and (altitude >= 10400 and altitude <= 10600)
    elif (num == 5):
        return (rnge >= 0.5 and rnge <= 1.5) and (bearing >= -95 and bearing <= -85) and (altitude >= 9400 and altitude <= 9600)

def reportError(errorStr, resultsFile):
    dat1 = datetime.datetime.now().strftime("%m-%d-%Y %H:%M:%S")
    resultsFile.write(("Date: " + dat1))
    resultsFile.write("\n")
    if(itr != []):
        for i in range(6):
            resultsFile.write("Intruder: " + str(itr[i]) + ", Range: " + str(rng[i]) + ", Bearing: " + str(brg[i]) + ", Altitude: " + str(alt[i] + 10000))
            resultsFile.write("\n")
    resultsFile.write("Messsage: " + errorStr)
    resultsFile.write("\n")
    resultsFile.write("\n")

def main():
    app = QtWidgets.QApplication(sys.argv)
    time.sleep(10)
    main = MainWindow()
    main.show()
    sys.exit(app.exec_())

if __name__ == '__main__':         
    main()