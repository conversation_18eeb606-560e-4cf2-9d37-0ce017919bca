# -*- coding: utf-8 -*-
"""
Created on Wed April 28 3:20:30 2020

@author: E589493
         K. <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Audio Identification, Section 2.2.12.
             
             "Identification:
             a. The equipment shall provide a means for selecting the 
             audio (Morse Code) identification of the pilot-selected
             ground station at the sensitivity levels specified in 
             paragraph 2.2.8.
             is the test signal having 70% reply efficiency:

             b. The equipment shall meet the requirements in a. above
             in the presence of a 1,400 randomly distributed pp/s 
             on-channel interfering signal that is of the same 
             amplitude and mode spacing as the RF signal providing
             the identification.
             
             c. The equipment shall continue to display distance 
             information during hte identification period."
             
INPUTS:      Top_Cable_Loss, atc, ARINC_client, sigGen, audio_processing
OUTPUTS:     Decrypted Morse Code Message String, returns a string containing
             the results of the four test cases.

             Note: Assumes that the Pickering switches are set such that the
             DME Audio is passed back to the PXI Scope (NI5110)  The Audio 
             Processing module uses the PXI Scope (5110) to create a WaveForm 
             file and perform the signal processing to decode the Morse Code.

HISTORY:

0
3/03/2020   KF    Initial Release.
06/22/2020  AS    Added tvl statements
                  Added ARINC client                                 
04/10/2021   MRS  Updates for new handlers and Lobster.                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers import N5172BSigGen
from TXDLib.Handlers import audio_processing as ap

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def init_DME_Standard(rm,cable_loss, atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel 56X at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    rm.logMessage(0,"*Test_189_2_2_12 - Setting ATC to DME Mode ") 
    ARINC.writeChannel(111.90)
    atc.DMEMode()
    time.sleep(1)
    #atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))

def setupSigGen(sg):
    """ Initializes sigGen"""
    sg.Reset()

    #Set Up Freq/Power
    sg.setFrequency('1017.0MHz')
    sg.setPower('-60dBm')
    sg.setModulationState("off")
    sg.setRF('on')
    print ("Freq: ",sg.getFrequency())
    print("Pwr: ",sg.getPower())
    print("Mod: ",sg.getModulationState())
    print("RF: ", sg.getRF())

    #Select and Replay Waveform
    sg.replaywaveform('2212_DME_1400',10e6)
    sg.setRF('ON')
    time.sleep(5)

def atc_power_level(Step_PowerLevel, atc):
    """ This function set teh next power level to be measured eg -50 dBm for DO-189 2.2.1b"""
    cmd = ':ATC:DME:POWER ' + str(Step_PowerLevel)
    atc.gwrite(cmd)
    time.sleep(5)
    #print('Power at -50dBm Record ARINC 429 Value make sure error is less than 0.17nm')

def set_VOR_PAIR0(atc):
    """ This FUnction turns off RF and closes ATC """
    #Turn Off RF
    atc.gwrite(":ATC:DME:CHANNEL:MODE 0VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 111.9")
    time.sleep(1)
    atc.gwrite(":ATC:DME:RANGE 34")

def initIdentStart(rm,atc):
    rm.logMessage(0,"*Test_189_2_2_12 - Setting Identification and Equilization Pulses to On ") 

    #Set identification pulse on
    atc.gwrite(":ATC:DME:IDENT:MODE CODE")
    time.sleep(1)
    atc.gwrite(":ATC:DME:IDENT:CODE KSEA")
    time.sleep(1)
    #Turn on Equalization Pulses
    atc.gwrite(":ATC:DME:EQUAL ON")

def toggleEqaulPulses(atc, state):
    """ Pass in a string 'ON' for equalization pulses on or 'OFF' for equalization pulses on """
    cmd = ':ATC:DME:EQUAL ' + str(state)
    atc.gwrite(cmd)

def morse_Code_Parse(rm,word):
    """ This function will return a single morse code word incase there are other words in the sample """
    #Split word and
    word = word.split()
    rm.logMessage(0,"*Test_189_2_2_12 - Parsing Morse Code: "+ str(word[0]))
    return str(max(word, key = len))

##############################################################################
################# MAIN      ##################################################
##############################################################################

def Test_2_2_12(rm,atc,sg,ARINC,PathLoss):

    #Initialzie results.
    s1 = '----'
    s2 = '----'
    s3 = '----'
    s4 = '----'

    #Init DME EQ ON, No IF
    sg.setRF('OFF')    #shut off SigGen (if left on)
    init_DME_Standard(rm,PathLoss, atc, ARINC)
    plvl = -80.0 + PathLoss
    atc_power_level(plvl, atc)
    initIdentStart(rm,atc)
    s1 = ap.myMainDME()
    s1 = morse_Code_Parse(rm,s1)
    
    #toggle off EQ pulses, NO IF
    toggleEqaulPulses(atc, "OFF")
    s2 = ap.myMainDME()
    s2 = morse_Code_Parse(rm,s2)
    
    ##Random Interfernce, EQ OFF
    setupSigGen(sg)            #Turn On SigGen
    time.sleep(5)
    plvl = -45.0 + PathLoss
    atc_power_level(plvl, atc)
    time.sleep(5)
    s3 = ap.myMainDME()
    s3 = morse_Code_Parse(rm,s3)
    
    ##Random Interfernce, EQ ON
    toggleEqaulPulses(atc, "ON")
    time.sleep(5)
    s4 = ap.myMainDME()
    s4 = morse_Code_Parse(rm,s4)

    #Turn Off SigGen and ATC
    atc.gwrite(':ATC:DME:STOP')
    sg.setRF('OFF')

    #return results
    str1 = '1:' + s1 + ' 2:' + s2 + ' 3:' + s3 + ' 4:' + s4
    rm.logMessage(0,"*Test_189_2_2_12 - Final Results: "+ str1)
    return str1
 
############################################################################## 

if __name__ == "__main__":

    #Initialize Intruuments
    rm = ate_rm()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)
	
    #Initialize Signal Generator
    sg = N5172BSigGen(rm)
    sg.Reset()
    #Load TACAN Waveform into SG (if not pre-loaded)
    #res = sg.downloadwaveform('TACAN')
    #rm.logMessage(1,'File Result: ' + str(res))


    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    
    PathLoss = 12.25

    Test_2_2_12(rm,atc,sg,ARINC,PathLoss)
    
    
    #Close the ATC and Signal Generator. 
    atc.gwrite(':ATC:DME:STOP')
    sg.setRF('OFF')
    atc.close()
    sg.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
