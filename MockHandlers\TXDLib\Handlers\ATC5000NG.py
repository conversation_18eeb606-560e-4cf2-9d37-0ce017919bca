"""
Mock implementation of ATC5000NG.py - ATC Test Equipment
Provides mock ATC functionality without network dependencies
"""

import time
import random
from typing import List


class ATC5000NG:
    def __init__(self, ate_rm):
        self.resourceManager = ate_rm
        self.connected = True
        self.status = "20"  # Ready status
        
        # Mock connection success
        self.resourceManager.logMessage(1, "Mock ATC5000 Connection Success")

    def reconnect_socket(self):
        """Mock socket reconnection"""
        self.resourceManager.logMessage(1, "Mock Socket successfully recreated")
        self.resourceManager.logMessage(1, "Mock ATC5000 Connection Success")
        return

    def basicTvlMsg(self, tvlTxt: str):
        self.resourceManager.logMessage(1, tvlTxt)
        
    def write(self, message: str):
        """Mock write to ATC"""
        time.sleep(0.001)  # Optimized timing
        self.resourceManager.logMessage(1, f"[MOCK ATC WRITE] {message}")
    
    def read(self, message: str) -> str:
        """Mock read from ATC"""
        time.sleep(0.001)  # Optimized timing
        mock_response = "OK"
        self.resourceManager.logMessage(1, f"[MOCK ATC READ] {mock_response}")
        return mock_response

    def query(self, message: str) -> str:
        """Mock query to ATC with realistic responses"""
        time.sleep(0.002)  # Optimized timing
        
        # Provide realistic responses based on command
        if "STATUS?" in message.upper():
            response = self.status
        elif "IDN?" in message.upper():
            response = "ATC5000NG,Mock,SN123456,FW2.0"
        elif "FREQ?" in message.upper():
            response = str(1030.0 + random.uniform(-0.5, 0.5))
        elif "PREP?" in message.upper():
            # Mock percent reply data
            response = "95.2,94.8,96.1,95.5"
        elif "DREP?" in message.upper():
            # Mock delay response
            response = str(3.0 + random.uniform(-0.1, 0.1))
        elif "WIDTH?" in message.upper():
            # Mock pulse width
            response = str(0.45 + random.uniform(-0.05, 0.05))
        elif "POS?" in message.upper():
            # Mock pulse position
            response = str(2.0 + random.uniform(-0.1, 0.1))
        elif "RISE?" in message.upper():
            # Mock rise time
            response = str(0.1 + random.uniform(-0.01, 0.01))
        else:
            response = "OK"
            
        self.resourceManager.logMessage(1, f"[MOCK ATC QUERY] {message} -> {response}")
        return response

    def percentquery(self, message: str) -> str:
        """Mock percent query with delay"""
        time.sleep(0.1)  # Reduced from 1s for optimization
        return self.query(message)

    def pulsequery(self, message: str) -> str:
        """Mock pulse query"""
        time.sleep(0.002)  # Optimized timing
        return self.query(message)
    
    def delayquery(self, message: str) -> str:
        """Mock delay query"""
        time.sleep(0.002)  # Optimized timing
        return self.query(message)
			
    def close(self):
        """Mock ATC close"""
        self.write(":ATC:DME:STOP")
        time.sleep(0.1)  # Reduced from 3s
        self.connected = False

    def Ident(self) -> str:
        """Mock identification"""
        return self.query("*IDN?")

    def Reset(self):
        """Mock reset with optimized timing"""
        self.write(":ATC:RESET")
        time.sleep(2)  # Reduced from 8s - optimized reset delay
        self.resourceManager.logMessage(1, "Mock Reset Complete\n")
    
    def atcStatus(self) -> str:
        """Mock status query"""
        return self.query(":ATC:STATUS?")
    
    def gwrite(self, str_cmd: str):
        """Mock generic write"""
        self.resourceManager.logMessage(1, f"Cmd: {str_cmd}")
        self.write(str_cmd)
        
    def gquery(self, str_cmd: str) -> str:
        """Mock generic query"""
        return self.query(str_cmd)

    def init_own_aircraft_pos(self):
        """Mock aircraft position initialization"""
        self.write(":ATC:OWN:LAT 48.834340")
        self.write(":ATC:OWN:LONG -122.214200")
        self.write(":ATC:OWN:HEAD 0")
        self.write(":ATC:OWN:ALT 12000")
        self.write(":ATC:OWN:MSADDR 4")
        self.resourceManager.logMessage(0, "Mock ATC Own Aircraft Set")
        time.sleep(0.1)  # Reduced from 3s
        
    def set_cable_loss(self, top_loss: str, bot_loss: str):
        """Mock cable loss setting"""
        cmd = ':ATC:XPDR:CABLOS ' + top_loss
        self.write(cmd)
        cmd = ':ATC:XPDR:CABLOSBOT ' + bot_loss
        self.write(cmd)
        
    def waitforstatus(self):
        """Mock status waiting - optimized"""
        time.sleep(0.1)  # Reduced wait time
        self.resourceManager.logMessage(1, f"Mock STATUS: {self.status}")
    
    def is_number(self, s: str) -> bool:
        """Helper function to check if string is a number"""
        try:
            float(s)
            return True
        except ValueError:
            return False

    def getPercentReply(self, repeat_count: int) -> List[float]:
        """Mock percent reply with realistic values"""
        # Return mock reply rates for ATCRBS Top/Bot and ModeS Top/Bot
        replyrate = [
            95.2 + random.uniform(-2.0, 2.0),  # ATCRBS Top
            94.8 + random.uniform(-2.0, 2.0),  # ATCRBS Bot
            96.1 + random.uniform(-2.0, 2.0),  # ModeS Top
            95.5 + random.uniform(-2.0, 2.0)   # ModeS Bot
        ]
        self.resourceManager.logMessage(1, f"Mock Reply Rate: {replyrate}")
        return replyrate

    def getPulseFrequency(self, repeat_count: int) -> float:
        """Mock pulse frequency"""
        frequency = 1030.0 + random.uniform(-1.0, 1.0)
        self.resourceManager.logMessage(1, f"Mock Frequency: {frequency}")
        return frequency

    def getReplyDelay(self, repeat_count: int) -> float:
        """Mock reply delay"""
        delay = 3.0 + random.uniform(-0.2, 0.2)
        self.resourceManager.logMessage(1, f"Mock Reply Delay: {delay}")
        return delay

    def getPulseWidth(self, repeat_count: int) -> float:
        """Mock pulse width"""
        width = 0.45 + random.uniform(-0.05, 0.05)
        self.resourceManager.logMessage(1, f"Mock Pulse Width: {width}")
        return width

    def getPulsePosition(self, repeat_count: int) -> float:
        """Mock pulse position"""
        position = 2.0 + random.uniform(-0.1, 0.1)
        self.resourceManager.logMessage(1, f"Mock Pulse Position: {position}")
        return position

    def getRiseTime(self, repeat_count: int) -> float:
        """Mock rise time"""
        risetime = 0.1 + random.uniform(-0.01, 0.01)
        self.resourceManager.logMessage(1, f"Mock Rise Time: {risetime}")
        return risetime

    def getFallTime(self, repeat_count: int) -> float:
        """Mock fall time"""
        falltime = 0.1 + random.uniform(-0.01, 0.01)
        self.resourceManager.logMessage(1, f"Mock Fall Time: {falltime}")
        return falltime

    def transponderMode(self):
        """Mock transponder mode setting"""
        self.write(":ATC:XPDR:MODE ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeS(self):
        """Mock transponder Mode S setting"""
        self.write(":ATC:XPDR:MODES ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode S Set")
        time.sleep(0.1)  # Reduced timing

    def DMEMode(self):
        """Mock DME mode setting"""
        self.write(":ATC:DME:MODE ON")
        self.resourceManager.logMessage(1, "Mock DME Mode Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeA(self):
        """Mock transponder Mode A setting"""
        self.write(":ATC:XPDR:MODEA ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode A Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeC(self):
        """Mock transponder Mode C setting"""
        self.write(":ATC:XPDR:MODEC ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode C Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeCS(self):
        """Mock transponder Mode C/S setting"""
        self.write(":ATC:XPDR:MODECS ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode C/S Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeAS(self):
        """Mock transponder Mode A/S setting"""
        self.write(":ATC:XPDR:MODEAS ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode A/S Set")
        time.sleep(0.1)  # Reduced timing

    def transponderModeA_Only(self):
        """Mock transponder Mode A Only setting"""
        self.write(":ATC:XPDR:MODEA_ONLY ON")
        self.resourceManager.logMessage(1, "Mock Transponder Mode A Only Set")
        time.sleep(0.1)  # Reduced timing

    def data_log_start(self):
        """Mock data logging start"""
        self.write(":ATC:LOG:START")
        self.resourceManager.logMessage(1, "Mock Data Logging Started")
        time.sleep(0.1)  # Reduced timing

    def data_log_stop(self, filename=None):
        """Mock data logging stop"""
        self.write(":ATC:LOG:STOP")
        if filename:
            self.resourceManager.logMessage(1, f"Mock Data Logging Stopped - File: {filename}")
        else:
            self.resourceManager.logMessage(1, "Mock Data Logging Stopped")
        time.sleep(0.1)  # Reduced timing
