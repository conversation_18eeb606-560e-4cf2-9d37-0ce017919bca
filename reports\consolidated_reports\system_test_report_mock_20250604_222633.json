{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-04T22:26:33.342835", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 73, "passed": 4, "failed": 69, "errors": 0, "timeouts": 0, "success_rate": 5.47945205479452, "total_execution_time": 12.234553337097168, "start_time": "2025-06-04T22:26:21.107288", "end_time": "2025-06-04T22:26:33.341841"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1780238151550293, "start_time": "2025-06-04T22:26:21.109295", "end_time": "2025-06-04T22:26:21.287319", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16715502738952637, "start_time": "2025-06-04T22:26:21.288320", "end_time": "2025-06-04T22:26:21.455475", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649777889251709, "start_time": "2025-06-04T22:26:21.455475", "end_time": "2025-06-04T22:26:21.620453", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16697311401367188, "start_time": "2025-06-04T22:26:21.621447", "end_time": "2025-06-04T22:26:21.788420", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17197155952453613, "start_time": "2025-06-04T22:26:21.789420", "end_time": "2025-06-04T22:26:21.961391", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16196036338806152, "start_time": "2025-06-04T22:26:21.962405", "end_time": "2025-06-04T22:26:22.124365", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16298246383666992, "start_time": "2025-06-04T22:26:22.124365", "end_time": "2025-06-04T22:26:22.287347", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16658735275268555, "start_time": "2025-06-04T22:26:22.288338", "end_time": "2025-06-04T22:26:22.454925", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 46, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16512465476989746, "start_time": "2025-06-04T22:26:22.454925", "end_time": "2025-06-04T22:26:22.620050", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 32, in <module>\n    import pyvisa\nModuleNotFoundError: No module named 'pyvisa'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16343402862548828, "start_time": "2025-06-04T22:26:22.621045", "end_time": "2025-06-04T22:26:22.784479", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17297005653381348, "start_time": "2025-06-04T22:26:22.784479", "end_time": "2025-06-04T22:26:22.957450", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497516632080078, "start_time": "2025-06-04T22:26:22.958450", "end_time": "2025-06-04T22:26:23.123426", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1629776954650879, "start_time": "2025-06-04T22:26:23.125422", "end_time": "2025-06-04T22:26:23.287402", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16098427772521973, "start_time": "2025-06-04T22:26:23.288395", "end_time": "2025-06-04T22:26:23.449380", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16704940795898438, "start_time": "2025-06-04T22:26:23.450368", "end_time": "2025-06-04T22:26:23.617418", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16523432731628418, "start_time": "2025-06-04T22:26:23.618344", "end_time": "2025-06-04T22:26:23.783578", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16297149658203125, "start_time": "2025-06-04T22:26:23.784577", "end_time": "2025-06-04T22:26:23.947549", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16570329666137695, "start_time": "2025-06-04T22:26:23.947549", "end_time": "2025-06-04T22:26:24.113252", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16597914695739746, "start_time": "2025-06-04T22:26:24.114245", "end_time": "2025-06-04T22:26:24.280225", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649761199951172, "start_time": "2025-06-04T22:26:24.281224", "end_time": "2025-06-04T22:26:24.446200", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 50, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16496491432189941, "start_time": "2025-06-04T22:26:24.446200", "end_time": "2025-06-04T22:26:24.611165", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16866827011108398, "start_time": "2025-06-04T22:26:24.612163", "end_time": "2025-06-04T22:26:24.780832", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649787425994873, "start_time": "2025-06-04T22:26:24.780832", "end_time": "2025-06-04T22:26:24.945810", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 53, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16997814178466797, "start_time": "2025-06-04T22:26:24.946803", "end_time": "2025-06-04T22:26:25.116781", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16597318649291992, "start_time": "2025-06-04T22:26:25.116781", "end_time": "2025-06-04T22:26:25.282754", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16397571563720703, "start_time": "2025-06-04T22:26:25.282754", "end_time": "2025-06-04T22:26:25.446730", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 48, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16596651077270508, "start_time": "2025-06-04T22:26:25.446730", "end_time": "2025-06-04T22:26:25.612697", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1669774055480957, "start_time": "2025-06-04T22:26:25.613695", "end_time": "2025-06-04T22:26:25.780672", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1659684181213379, "start_time": "2025-06-04T22:26:25.780672", "end_time": "2025-06-04T22:26:25.946640", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497397422790527, "start_time": "2025-06-04T22:26:25.947640", "end_time": "2025-06-04T22:26:26.112614", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1669769287109375, "start_time": "2025-06-04T22:26:26.112614", "end_time": "2025-06-04T22:26:26.279591", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1639702320098877, "start_time": "2025-06-04T22:26:26.279591", "end_time": "2025-06-04T22:26:26.443561", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 39, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649785041809082, "start_time": "2025-06-04T22:26:26.444558", "end_time": "2025-06-04T22:26:26.609536", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16697239875793457, "start_time": "2025-06-04T22:26:26.609536", "end_time": "2025-06-04T22:26:26.776509", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497564315795898, "start_time": "2025-06-04T22:26:26.776509", "end_time": "2025-06-04T22:26:26.941485", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1649792194366455, "start_time": "2025-06-04T22:26:26.942475", "end_time": "2025-06-04T22:26:27.107455", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_10.py\", line 101, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16497278213500977, "start_time": "2025-06-04T22:26:27.107455", "end_time": "2025-06-04T22:26:27.272427", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_12.py\", line 52, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16496968269348145, "start_time": "2025-06-04T22:26:27.272427", "end_time": "2025-06-04T22:26:27.437397", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_1_b.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16698265075683594, "start_time": "2025-06-04T22:26:27.438394", "end_time": "2025-06-04T22:26:27.605377", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_3.py\", line 45, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16897368431091309, "start_time": "2025-06-04T22:26:27.606366", "end_time": "2025-06-04T22:26:27.775340", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_4.py\", line 36, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16697907447814941, "start_time": "2025-06-04T22:26:27.776339", "end_time": "2025-06-04T22:26:27.943318", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_6.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16763997077941895, "start_time": "2025-06-04T22:26:27.944312", "end_time": "2025-06-04T22:26:28.111952", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_7.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16708660125732422, "start_time": "2025-06-04T22:26:28.112940", "end_time": "2025-06-04T22:26:28.280026", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_2_2_8.py\", line 38, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16396832466125488, "start_time": "2025-06-04T22:26:28.281030", "end_time": "2025-06-04T22:26:28.444998", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1799764633178711, "start_time": "2025-06-04T22:26:28.445999", "end_time": "2025-06-04T22:26:28.625975", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248211.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17896246910095215, "start_time": "2025-06-04T22:26:28.626976", "end_time": "2025-06-04T22:26:28.805939", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248212.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18139410018920898, "start_time": "2025-06-04T22:26:28.807210", "end_time": "2025-06-04T22:26:28.988604", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_248213.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17997074127197266, "start_time": "2025-06-04T22:26:28.989605", "end_time": "2025-06-04T22:26:29.169575", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_24822.py\", line 40, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18196606636047363, "start_time": "2025-06-04T22:26:29.170578", "end_time": "2025-06-04T22:26:29.352545", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO282\\DO282_24823.py\", line 35, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16216182708740234, "start_time": "2025-06-04T22:26:29.353544", "end_time": "2025-06-04T22:26:29.515706", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.19498038291931152, "start_time": "2025-06-04T22:26:29.516704", "end_time": "2025-06-04T22:26:29.711684", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16796636581420898, "start_time": "2025-06-04T22:26:29.711684", "end_time": "2025-06-04T22:26:29.879651", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16196608543395996, "start_time": "2025-06-04T22:26:29.879651", "end_time": "2025-06-04T22:26:30.041617", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_3.py\", line 28, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16097378730773926, "start_time": "2025-06-04T22:26:30.041617", "end_time": "2025-06-04T22:26:30.202591", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_5.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1619892120361328, "start_time": "2025-06-04T22:26:30.203590", "end_time": "2025-06-04T22:26:30.365579", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_3_8.py\", line 51, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16495800018310547, "start_time": "2025-06-04T22:26:30.365579", "end_time": "2025-06-04T22:26:30.530537", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 53, in <module>\n    import numpy as np\nModuleNotFoundError: No module named 'numpy'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16748523712158203, "start_time": "2025-06-04T22:26:30.530537", "end_time": "2025-06-04T22:26:30.698023", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 70, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16719698905944824, "start_time": "2025-06-04T22:26:30.699023", "end_time": "2025-06-04T22:26:30.866220", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 56, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.16397309303283691, "start_time": "2025-06-04T22:26:30.866220", "end_time": "2025-06-04T22:26:31.030194", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16298508644104004, "start_time": "2025-06-04T22:26:31.031193", "end_time": "2025-06-04T22:26:31.194178", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 41, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16396141052246094, "start_time": "2025-06-04T22:26:31.195167", "end_time": "2025-06-04T22:26:31.358139", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.162980318069458, "start_time": "2025-06-04T22:26:31.359139", "end_time": "2025-06-04T22:26:31.522120", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16697263717651367, "start_time": "2025-06-04T22:26:31.523113", "end_time": "2025-06-04T22:26:31.690086", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\DO385\\DO385_2_3_3_1.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16498374938964844, "start_time": "2025-06-04T22:26:31.691085", "end_time": "2025-06-04T22:26:31.856069", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_A_Frequency.py\", line 32, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16196203231811523, "start_time": "2025-06-04T22:26:31.856069", "end_time": "2025-06-04T22:26:32.018031", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_B_Supression.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16397333145141602, "start_time": "2025-06-04T22:26:32.018031", "end_time": "2025-06-04T22:26:32.182004", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 47, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16698002815246582, "start_time": "2025-06-04T22:26:32.182004", "end_time": "2025-06-04T22:26:32.348984", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_D_Power.py\", line 55, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16197752952575684, "start_time": "2025-06-04T22:26:32.348984", "end_time": "2025-06-04T22:26:32.510962", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_E_Diversity.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.17197799682617188, "start_time": "2025-06-04T22:26:32.511951", "end_time": "2025-06-04T22:26:32.683929", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 37, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16699838638305664, "start_time": "2025-06-04T22:26:32.684922", "end_time": "2025-06-04T22:26:32.851921", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 62, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1629810333251953, "start_time": "2025-06-04T22:26:32.852914", "end_time": "2025-06-04T22:26:33.015895", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 43, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16198039054870605, "start_time": "2025-06-04T22:26:33.016887", "end_time": "2025-06-04T22:26:33.178868", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 33, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.16198039054870605, "start_time": "2025-06-04T22:26:33.179861", "end_time": "2025-06-04T22:26:33.341841", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"Procedures\\FAR43\\FAR43_J_Squitter.py\", line 42, in <module>\n    from TXDLib.Handlers import ate_rm\nModuleNotFoundError: No module named 'TXDLib'\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 8, "passed": 3, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 1, "failed": 10, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 12.234553337097168, "average_sequence_time": 0.16759662105612558, "sequences_per_hour": 21480.14665996407, "optimization_effectiveness": {"optimization_success_rate": 5.47945205479452, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 69, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 10, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 69 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}