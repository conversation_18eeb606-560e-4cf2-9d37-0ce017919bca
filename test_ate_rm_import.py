#!/usr/bin/env python3
"""
Test script to validate ate_rm import fix
"""

import sys
import os
from pathlib import Path

# Add MockHandlers to path to simulate mock mode
current_dir = Path.cwd()
mock_handlers_path = current_dir / "MockHandlers"
sys.path.insert(0, str(mock_handlers_path))

print("Testing ate_rm import and instantiation...")

try:
    from TXDLib.Handlers import ate_rm
    print("SUCCESS: ate_rm import successful")
    print(f"ate_rm type: {type(ate_rm)}")
    
    # Test instantiation
    rm = ate_rm()
    print("SUCCESS: ate_rm() instantiation successful")
    print(f"rm type: {type(rm)}")
    
    # Test basic functionality
    rm.logMessage(1, "Test message")
    print("SUCCESS: ate_rm.logMessage() working")
    
    # Test cleanup
    rm.cleanup()
    print("SUCCESS: ate_rm.cleanup() working")
    
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()
