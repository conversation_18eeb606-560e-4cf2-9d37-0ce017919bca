{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-05T14:18:43.758701", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 74, "passed": 6, "failed": 68, "errors": 0, "timeouts": 0, "success_rate": 8.108108108108109, "total_execution_time": 15.054062604904175, "start_time": "2025-06-05T14:18:28.703719", "end_time": "2025-06-05T14:18:43.757781"}, "sequence_results": [{"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22356367111206055, "start_time": "2025-06-05T14:18:28.706682", "end_time": "2025-06-05T14:18:28.930243", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 404, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2004702091217041, "start_time": "2025-06-05T14:18:28.932203", "end_time": "2025-06-05T14:18:29.132675", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 380, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1975243091583252, "start_time": "2025-06-05T14:18:29.133729", "end_time": "2025-06-05T14:18:29.331254", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 363, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22134852409362793, "start_time": "2025-06-05T14:18:29.332303", "end_time": "2025-06-05T14:18:29.553653", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 380, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19609332084655762, "start_time": "2025-06-05T14:18:29.555284", "end_time": "2025-06-05T14:18:29.751377", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 251, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1959078311920166, "start_time": "2025-06-05T14:18:29.753407", "end_time": "2025-06-05T14:18:29.949316", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 232, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19930624961853027, "start_time": "2025-06-05T14:18:29.950732", "end_time": "2025-06-05T14:18:30.150039", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 237, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19579744338989258, "start_time": "2025-06-05T14:18:30.151872", "end_time": "2025-06-05T14:18:30.347670", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 247, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.6359512805938721, "start_time": "2025-06-05T14:18:30.348759", "end_time": "2025-06-05T14:18:30.984712", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20072650909423828, "start_time": "2025-06-05T14:18:30.986126", "end_time": "2025-06-05T14:18:31.186854", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 132, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20522189140319824, "start_time": "2025-06-05T14:18:31.188242", "end_time": "2025-06-05T14:18:31.393466", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 128, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1873311996459961, "start_time": "2025-06-05T14:18:31.394577", "end_time": "2025-06-05T14:18:31.581910", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 153, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18990063667297363, "start_time": "2025-06-05T14:18:31.583383", "end_time": "2025-06-05T14:18:31.773286", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 101, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1950550079345703, "start_time": "2025-06-05T14:18:31.774525", "end_time": "2025-06-05T14:18:31.969580", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 113, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18743491172790527, "start_time": "2025-06-05T14:18:31.971358", "end_time": "2025-06-05T14:18:32.158794", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 109, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19216084480285645, "start_time": "2025-06-05T14:18:32.160496", "end_time": "2025-06-05T14:18:32.352658", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 95, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19129323959350586, "start_time": "2025-06-05T14:18:32.353629", "end_time": "2025-06-05T14:18:32.544920", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 128, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18904924392700195, "start_time": "2025-06-05T14:18:32.546020", "end_time": "2025-06-05T14:18:32.735072", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 299, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18672442436218262, "start_time": "2025-06-05T14:18:32.737061", "end_time": "2025-06-05T14:18:32.923786", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 279, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1984567642211914, "start_time": "2025-06-05T14:18:32.925326", "end_time": "2025-06-05T14:18:33.123785", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 53, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19820761680603027, "start_time": "2025-06-05T14:18:33.124825", "end_time": "2025-06-05T14:18:33.323035", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 51, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2068924903869629, "start_time": "2025-06-05T14:18:33.324884", "end_time": "2025-06-05T14:18:33.531776", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 58, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1878974437713623, "start_time": "2025-06-05T14:18:33.533434", "end_time": "2025-06-05T14:18:33.721333", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 56, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20219707489013672, "start_time": "2025-06-05T14:18:33.722647", "end_time": "2025-06-05T14:18:33.924847", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 41, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1955106258392334, "start_time": "2025-06-05T14:18:33.927315", "end_time": "2025-06-05T14:18:34.122828", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 41, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1874096393585205, "start_time": "2025-06-05T14:18:34.124468", "end_time": "2025-06-05T14:18:34.311879", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 187, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19693303108215332, "start_time": "2025-06-05T14:18:34.313305", "end_time": "2025-06-05T14:18:34.510239", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 114, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19421982765197754, "start_time": "2025-06-05T14:18:34.511809", "end_time": "2025-06-05T14:18:34.706028", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 114, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18298816680908203, "start_time": "2025-06-05T14:18:34.708011", "end_time": "2025-06-05T14:18:34.890996", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 191, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1870737075805664, "start_time": "2025-06-05T14:18:34.892100", "end_time": "2025-06-05T14:18:35.079176", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 122, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2272634506225586, "start_time": "2025-06-05T14:18:35.080951", "end_time": "2025-06-05T14:18:35.308215", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 192, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20781517028808594, "start_time": "2025-06-05T14:18:35.310047", "end_time": "2025-06-05T14:18:35.517864", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 209, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2230854034423828, "start_time": "2025-06-05T14:18:35.519014", "end_time": "2025-06-05T14:18:35.742102", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 182, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.21809005737304688, "start_time": "2025-06-05T14:18:35.743593", "end_time": "2025-06-05T14:18:35.961683", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 135, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1891937255859375, "start_time": "2025-06-05T14:18:35.963521", "end_time": "2025-06-05T14:18:36.152717", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 117, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 0.22256064414978027, "start_time": "2025-06-05T14:18:36.154759", "end_time": "2025-06-05T14:18:36.377321", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 104, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18889617919921875, "start_time": "2025-06-05T14:18:36.378755", "end_time": "2025-06-05T14:18:36.567652", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 55, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18566203117370605, "start_time": "2025-06-05T14:18:36.569535", "end_time": "2025-06-05T14:18:36.755197", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 127, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 91, in main\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19009613990783691, "start_time": "2025-06-05T14:18:36.757389", "end_time": "2025-06-05T14:18:36.947481", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 48, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18698644638061523, "start_time": "2025-06-05T14:18:36.949105", "end_time": "2025-06-05T14:18:37.136092", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 39, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1937394142150879, "start_time": "2025-06-05T14:18:37.137577", "end_time": "2025-06-05T14:18:37.331317", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 198, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 159, in main\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19939041137695312, "start_time": "2025-06-05T14:18:37.332631", "end_time": "2025-06-05T14:18:37.532022", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 347, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1961345672607422, "start_time": "2025-06-05T14:18:37.533555", "end_time": "2025-06-05T14:18:37.729691", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 41, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1856372356414795, "start_time": "2025-06-05T14:18:37.732157", "end_time": "2025-06-05T14:18:37.917799", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 29, in <module>\n    from TXDLib.Handlers import N5172BSigGen\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\N5172BSigGen.py\", line 79\n    def set_frequency(self, freq: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO282", "sequence": "DO282_248211.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18322443962097168, "start_time": "2025-06-05T14:18:37.919768", "end_time": "2025-06-05T14:18:38.102994", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248211.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248212.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19937562942504883, "start_time": "2025-06-05T14:18:38.104379", "end_time": "2025-06-05T14:18:38.303750", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248212.py\", line 43, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_248213.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19971561431884766, "start_time": "2025-06-05T14:18:38.305676", "end_time": "2025-06-05T14:18:38.505392", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_248213.py\", line 44, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24822.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18843317031860352, "start_time": "2025-06-05T14:18:38.506500", "end_time": "2025-06-05T14:18:38.694936", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24822.py\", line 41, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "DO282_24823.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1965806484222412, "start_time": "2025-06-05T14:18:38.696759", "end_time": "2025-06-05T14:18:38.893340", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO282\\DO282_24823.py\", line 36, in <module>\n    from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc\nModuleNotFoundError: No module named 'TXDLib.Procedures'\n"}, {"procedure": "DO282", "sequence": "FEC.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1373450756072998, "start_time": "2025-06-05T14:18:38.894836", "end_time": "2025-06-05T14:18:39.032181", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_CONNECTION.py", "status": "PASSED", "return_code": 0, "execution_time": 0.17640328407287598, "start_time": "2025-06-05T14:18:39.032909", "end_time": "2025-06-05T14:18:39.209314", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "UAT_LOGGING.py", "status": "PASSED", "return_code": 0, "execution_time": 0.17865347862243652, "start_time": "2025-06-05T14:18:39.210062", "end_time": "2025-06-05T14:18:39.388717", "stdout": "", "stderr": ""}, {"procedure": "DO282", "sequence": "reedsolo.py", "status": "PASSED", "return_code": 0, "execution_time": 0.13936400413513184, "start_time": "2025-06-05T14:18:39.389457", "end_time": "2025-06-05T14:18:39.528822", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_3.py", "status": "PASSED", "return_code": 0, "execution_time": 0.1670246124267578, "start_time": "2025-06-05T14:18:39.530538", "end_time": "2025-06-05T14:18:39.697564", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_3_5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18964362144470215, "start_time": "2025-06-05T14:18:39.698436", "end_time": "2025-06-05T14:18:39.888080", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_5.py\", line 222, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_3_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19156980514526367, "start_time": "2025-06-05T14:18:39.889348", "end_time": "2025-06-05T14:18:40.080920", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_8.py\", line 53, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\MockHandlers\\TXDLib\\Handlers\\D3054Scope.py\", line 79\n    def set_timebase(self, timebase: float):\nIndentationError: unexpected indent\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_1_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.3592081069946289, "start_time": "2025-06-05T14:18:40.082731", "end_time": "2025-06-05T14:18:40.441939", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_1_1.py\", line 227, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19821643829345703, "start_time": "2025-06-05T14:18:40.443057", "end_time": "2025-06-05T14:18:40.641273", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_1.py\", line 292, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_4_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19776296615600586, "start_time": "2025-06-05T14:18:40.643343", "end_time": "2025-06-05T14:18:40.841108", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_4_2_2.py\", line 232, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_5_4_1.py", "status": "PASSED", "return_code": 0, "execution_time": 0.13257193565368652, "start_time": "2025-06-05T14:18:40.842174", "end_time": "2025-06-05T14:18:40.974747", "stdout": "", "stderr": ""}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_1_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1935415267944336, "start_time": "2025-06-05T14:18:40.975844", "end_time": "2025-06-05T14:18:41.169386", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_1_2.py\", line 186, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19027304649353027, "start_time": "2025-06-05T14:18:41.170625", "end_time": "2025-06-05T14:18:41.360898", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_2_2_2.py\", line 229, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_2_4_6_4_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.2133622169494629, "start_time": "2025-06-05T14:18:41.361886", "end_time": "2025-06-05T14:18:41.575247", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_4_6_4_2.py\", line 223, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO385", "sequence": "DO385_2_3_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1972026824951172, "start_time": "2025-06-05T14:18:41.576763", "end_time": "2025-06-05T14:18:41.773967", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_3_3_1.py\", line 173, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_A_Frequency.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20118236541748047, "start_time": "2025-06-05T14:18:41.774935", "end_time": "2025-06-05T14:18:41.976114", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_A_Frequency.py\", line 153, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_B_Supression.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19862818717956543, "start_time": "2025-06-05T14:18:41.977558", "end_time": "2025-06-05T14:18:42.176189", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_B_Supression.py\", line 154, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_C_Sensitivity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19185137748718262, "start_time": "2025-06-05T14:18:42.177769", "end_time": "2025-06-05T14:18:42.369618", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_C_Sensitivity.py\", line 217, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_D_Power.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20195293426513672, "start_time": "2025-06-05T14:18:42.371817", "end_time": "2025-06-05T14:18:42.573769", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_D_Power.py\", line 322, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_E_Diversity.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19551420211791992, "start_time": "2025-06-05T14:18:42.574754", "end_time": "2025-06-05T14:18:42.770268", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_E_Diversity.py\", line 301, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_F_ModeSAddress.py", "status": "FAILED", "return_code": 1, "execution_time": 0.18970632553100586, "start_time": "2025-06-05T14:18:42.771632", "end_time": "2025-06-05T14:18:42.961339", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_F_ModeSAddress.py\", line 165, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_G_ModeSFormat.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1924724578857422, "start_time": "2025-06-05T14:18:42.962257", "end_time": "2025-06-05T14:18:43.154731", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_G_ModeSFormat.py\", line 533, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_H_ModeSAllCall.py", "status": "FAILED", "return_code": 1, "execution_time": 0.1941673755645752, "start_time": "2025-06-05T14:18:43.156404", "end_time": "2025-06-05T14:18:43.350572", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_H_ModeSAllCall.py\", line 254, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_I_ATCRBSOnly.py", "status": "FAILED", "return_code": 1, "execution_time": 0.20868229866027832, "start_time": "2025-06-05T14:18:43.351521", "end_time": "2025-06-05T14:18:43.560204", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_I_ATCRBSOnly.py\", line 130, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "FAR43", "sequence": "FAR43_J_Squitter.py", "status": "FAILED", "return_code": 1, "execution_time": 0.19562125205993652, "start_time": "2025-06-05T14:18:43.561237", "end_time": "2025-06-05T14:18:43.756861", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\FAR43\\FAR43_J_Squitter.py\", line 237, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}, "DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO282": {"total": 9, "passed": 4, "failed": 5, "errors": 0, "timeouts": 0}, "DO385": {"total": 11, "passed": 2, "failed": 9, "errors": 0, "timeouts": 0}, "FAR43": {"total": 10, "passed": 0, "failed": 10, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 15.054062604904175, "average_sequence_time": 0.203433278444651, "sequences_per_hour": 17696.219750887354, "optimization_effectiveness": {"optimization_success_rate": 8.108108108108109, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "ACTIVE"}}, "failure_analysis": {"total_failures": 68, "failure_by_procedure": {"DO181": 35, "DO189": 9, "DO282": 5, "DO385": 9, "FAR43": 10}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 68 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}