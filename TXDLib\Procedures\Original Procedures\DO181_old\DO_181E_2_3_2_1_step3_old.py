# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 3 (Dynamic Range).
             
             "Interrogate the transponder with a standard Mode A interrogation
             at RF Levels from MTL + 3 db to -21 dBm in approximately 5 equal
             steps.  Determine the reply ratio.  Repeate for a standard ModeC 
             ATCRBS/Mode S All-Call"
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'ReplyRatios' Array of Reply_Ratios at the specified Power Level
              and Interrogation mode.

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step3(rm,atc,PathLoss):
    """ DO-181E, Receiver Characteristics, Sect *******, Step 3 """

    
    rm.logMessage(2,"*** DO-181E, Receiver Characteristics, Sect *******, Step 3 ***\r\n")
    
    
    #Initialize Power Levels and ReplyRatios
    Pwr_Levels = ['-73.0','-63.0','-53.0', '-43.0', '-33.0', '-21']     #interrogation power levels
    ReplyRatios = [0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]       #Values read by TestStand

    #Adjust Power Levels by RF Path Loss
    Pwr_Levels[0] = str((float(Pwr_Levels[0]) + PathLoss))
    Pwr_Levels[1] = str((float(Pwr_Levels[1]) + PathLoss))
    Pwr_Levels[2] = str((float(Pwr_Levels[2]) + PathLoss))
    Pwr_Levels[3] = str((float(Pwr_Levels[3]) + PathLoss))
    Pwr_Levels[4] = str((float(Pwr_Levels[4]) + PathLoss))
    Pwr_Levels[5] = str((float(Pwr_Levels[5]) + PathLoss))
    print(Pwr_Levels)
   
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:PRF 100")       #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")     #Set Power Deviation for Bot Antenna
            
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    atc.waitforstatus()            
    rm.logMessage(0,"Test_2_3_2_1_Step3 ModeA - Begin Power Loop")    
    
    #loop thru the five power levels,Check the Reply Rate for Mode A
    k=0
    for P in Pwr_Levels:
        cmd = ':ATC:XPDR:POW ' + P
        atc.gwrite(cmd)
        time.sleep(20)
        
        replyrate = atc.getPercentReply(2)
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1
        
        #ModeA Bot
        val = replyrate[1]
        
        #print result at this frequency
        rm.logMessage(0,("RESULT: Power %s, ReplyRate %f") % (P,val)) 
        ReplyRatios[k] = val
        k=k+1
    
    
    #Turn off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #Set for Mode C/S All-Call
    atc.transponderModeCS()
    atc.gwrite(":ATC:XPDR:PRF 50")       #Pulse Repatition

    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    atc.waitforstatus()
    rm.logMessage(0,"Test_2_3_2_1_Step3 ModeS - Begin Power Loop")    
     
    #loop thru the five power levels
    for P in Pwr_Levels:
        cmd = ':ATC:XPDR:POW ' + P
        atc.gwrite(cmd)
        time.sleep(20)
        
        replyrate = atc.getPercentReply(2)      
        # fix for erroneous reply rate
        count = 0
        while replyrate[3] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1
        
        #ModeS Bot
        val = replyrate[3]
        
        #print result at this frequency
        rm.logMessage(0,("RESULT: Power %s, ReplyRate %f") % (P,val)) 
        ReplyRatios[k] = val
        k=k+1
     
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
       
    rm.logMessage(0,"Test_2_3_2_1_Step3 End: " + str(ReplyRatios))
    rm.logMessage(2,"Done, closing session")    
    
    return ReplyRatios


##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step3(rm,atc_obj,-12.0)
    
    
    atc_obj.close()

    


