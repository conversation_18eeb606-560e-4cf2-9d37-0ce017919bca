# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Diversity Operation, Section ********
             
			 Step1 Single Channel Test: Interrogate channel A only while monitoring channels A and B.  At 
             signal level MTL + 6 dB use a Mode A, Mode C/S All-Call and Mode S(Mode S format UF=4, and if so equiped
             20).  For signal levels of -50 dBm and -21 dBm use a Mode A and Mode S interrogatin and veriy:
             -- correct channel replies,
             -- reply delay for each interrogation signal type and signal level.
             Repeat reversing the channels.  Compare records of reply delays for conformance ********
             
            
             
INPUTS:      RM,ATC,PwrMeter, Top_PathLoss, Bot_PathLoss
OUTPUTS:     Power_TopAntenna1       # Power Level in dBm, Step1,Transmit on Top Antenna
             Power_BotAntenna1       # Power Level in dBm, Step1,Transmit on Top Antenna
             Power_TopAntenna2       # Power Level in dBm, Step2,Transmit on Bot Antenna
             Power_BotAntenna2       # Power Level in dBm, Step2,Transmit on Bot Antenna
             Reply_DelayTop1         # Reply Delay uSec, Step1,Transmit on Top Antenna
             Reply_DelayBot1         # Reply Delay uSec, Step1,Transmit on Top Antenna
             Reply_DelayTop2         # Reply Delay uSec, Step2,Transmit on Bot Antenna
             Reply_DelayBot2         # Reply Delay uSec, Step2,Transmit on Bot Antenna




HISTORY:

04/27/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup.
03/08/2021   MRS    Updates for New Handlers and Lobster.
11/11/2022   CS     Edited for TXD Pre-Qual
10/2023      CS     Cleaned up reply delay errors from ATC. Edited Power Meter setup.                               
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import B4500CPwrMeter


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB_Bottom(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)  #Primary Power
    #rfbob.disconnect()

def init_RFBOB_Top(rfbob):
    """ initializes the RF BOB to Prim & Sec to TOP port. """
    
    #rfbob.connect()
    rfbob.setSwitch(0,1)   #Primary Top Port
    rfbob.setSwitch(1,1)   #Secondary Top Port
    rfbob.setSwitch(10,1)  #Primary Power
    #rfbob.disconnect()

def pw_init(pw):
    """ Initializes Power Meter to pulse mode, sets timebase, sets trigger""" 
    
    ### PUT METER IN PULSE MODE
    pw.autoset()         #Initialize to defaults
    #pw.displyClear()
    time.sleep(5)
    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    pw.setCalculateUnits('dBm')
    pw.setCalculate1_on('ON')
    
    #Trigger
    pw.basicWrite("TRIGger:POSition LEFT")
    pw.basicWrite("TRIGger:SOURce CH1")   ######originally Ext Video Out from ATC, changed to ch1
    pw.basicWrite("TRIGger:SLOPe POS")
    pw.basicWrite("SENSe1:AVERage 1")
    pw.basicWrite("TRIGger:MODE NORMAL")  #Importantae!
    pw.basicWrite("TRIGger:LEV 50.0")    #Importantae!
    pw.basicWrite("TRIGger:HOLDoff 30e-6")
    pw.basicWrite("DISPlay:TRACe1:VSCALE 10.00")
    pw.basicWrite("SENSe1:CORRection:OFFSet 52")
    pw.basicWrite("DISPlay:TRACe1:VCENTer 21.71")
    pw.basicWrite("SENSe1:AVERage 1")
    
    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    TimeBase = '10e-6'                 #Importantae!
    pw.setTimeBase(TimeBase)
    print("TimeBase-Pulse: ",pw.getTimeBase())
    print("TSPAN: ",pw.basicQuery("DISPlay:TSPAN?"))
    pw.setFrequency1('1.090e9')
      

def find_pulses(pw,title="PowerMeter (dBm vs time)"):
    """ using Power Meter, finds peaks, gets stats for each pulse, and plots. Returns string with
    pulse parameters and integer with number of pulses. """
    pw.basicWrite("ABORt")
    time.sleep(0.3)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(1)
    pw.basicWrite("INITiate:IMMediate")
    time.sleep(2)
    pw.basicWrite("INITiate:CONTinuous OFF")
    time.sleep(0.3)
    nPulse = pw.findpeaks('50')      #actual level for max in dBm
    pwr = ""
    for i in range(nPulse):
        print("\n\nPULSE : ",i)
        pw.setpulsepositions(i)        # set markers for pulse i
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
    #plots the results
    #pw.plotpeaks('-30',title)            #comment this statment when running from TestStand
    return pwr, nPulse
    


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_10_Step1a(rm,atc,pwrmeter,rfbob,PathLossBot,PathLossTop):

    rm.logMessage(2,"*** DO-181E, Diversity Operation: Sect ********_Step1a - MODE A ***")
	
    #initialize rfbob
    init_RFBOB_Top(rfbob)


    #Set the ATC Scope 1 to SYNCH
    atc.gwrite(":ATC:SET:SCO:CH1 24")     #used as trigger for power meter

    #Initialize Power Levels
    Power_TopAntenna1 = 0.0      # Power Level in dBm, Step1,Transmit on Top Antenna
    Power_BotAntenna1 = 0.0      # Power Level in dBm, Step1,Transmit on Top Antenna
    Power_TopAntenna2 = 0.0      # Power Level in dBm, Step2,Transmit on Bot Antenna
    Power_BotAntenna2 = 0.0      # Power Level in dBm, Step2,Transmit on Bot Antenna
    Reply_DelayTop1 = 0.0        # Reply Delay uSec, Step1,Transmit on Top Antenna
    Reply_DelayBot1 = 0.0        # Reply Delay uSec, Step1,Transmit on Top Antenna
    Reply_DelayTop2 = 0.0        # Reply Delay uSec, Step2,Transmit on Bot Antenna
    Reply_DelayBot2 = 0.0        # Reply Delay uSec, Step2,Transmit on Bot Antenna
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A, Top Antenna
    atc.transponderModeA()
    #atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
    
    #Transmit on Top Antenna
    atc.gwrite(":ATC:SET:GENA:SIG ON") #Turn On Top Generators
    atc.gwrite(":ATC:SET:GENB:SIG ON") #Turn On Top Generators
    atc.gwrite(":ATC:SET:GENC:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
    rm.logMessage(2,"Step1 SetUp for Top Ant")
 
    #Make Power Measurements on Top Antenna
    init_RFBOB_Top(rfbob) 
    rm.logMessage(2,"Step1a Top Ant")   
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #initialize powermeter
    pw_init(pwrmeter)
    time.sleep(1)
    #Step1:Measure Pulse Power -- Mode S, Top Antenna
    val1 = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter,Diversity,Top Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val1 = float(p_str[3])
    else:
        rm.logMessage(3,"Step1a Top Ant Error, No Pulses")
 		
    #Step 1 Power Level,Top Antenna
    line = "Step1a - Top Ant: " + str(val1)
    rm.logMessage(2,line)  
    Power_TopAntenna1 = (val1)    #dBm --see Notes

    #Get Reply Delay 
    time.sleep(1)
    Reply_DelayTop1 = atc.getReplyDelay(2)
    rm.logMessage(0,("Top Reply Delay Initial: " + str(Reply_DelayTop1)))
    time.sleep(1)
    # fix for erroneous reply rate,try 10 times
    count = 0
    while (Reply_DelayTop1 == -1.0 or Reply_DelayTop1 == 20) and count < 10:
        Reply_DelayTop1 = atc.getReplyDelay(2)
        time.sleep(1)
        count = count + 1       
    rm.logMessage(0,("Top Reply Delay Final: " + str(Reply_DelayTop1)))
   
    #Make Power Measurements on Bottom Antenna
    init_RFBOB_Bottom(rfbob) 
    rm.logMessage(2,"Step1b Bot Ant")

    #initialize powermeter
    pw_init(pwrmeter)
    
    #Step1:Measure Pulse Power -- Mode S, Bottom Antenna 
    val2 = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter,Diversity,Bot Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val2 = float(p_str[3])
    else:
        rm.logMessage(3,"Step1b Bot Ant Error, No Pulses")   
    
    #Step 1 Power Level,Bot Antenna    
    line = "Step1b - Bot Ant: " + str(val2)
    rm.logMessage(2,line)  
    Power_BotAntenna1 = (val2)    #dBm --see Notes

    #Get Reply Delay 
    time.sleep(1)
    Reply_DelayBot1 = atc.getReplyDelay(2)
    rm.logMessage(0,("Bottom Reply Delay Initial: " + str(Reply_DelayBot1)))
    time.sleep(1)
    # fix for erroneous reply rate,try 10 times
    count = 0
    while (Reply_DelayBot1 == -1.0 or Reply_DelayBot1 == 20) and count < 10:
        Reply_DelayBot1 = atc.getReplyDelay(2)
        time.sleep(1)
        count = count + 1       
    rm.logMessage(0,("Bottom Reply Delay Final: " + str(Reply_DelayBot1)))
    
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    time.sleep(2)

    #Set Up Transponder -- MODE A, Bottom Antenna
    atc.transponderModeA()
    #atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
    
    #Transmit on Bottom Antenna
    atc.gwrite(":ATC:SET:GENA:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GENB:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GENC:SIG ON") #Turn ON Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:SIG ON") #Turn ON Two BOTTOM Generators
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT BOTTOM") #Bottom Antenna
    rm.logMessage(2,"Step2 SetUp for Bot Ant")
 
    #Make Power Measurements on Top Antenna
    init_RFBOB_Top(rfbob)
    rm.logMessage(2,"Step2a Top Ant")    
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #initialize powermeter
    pw_init(pwrmeter)
    
    #Step2:Measure Pulse Power -- Mode S, Top Antenna
    val3 = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter,Diversity,Top Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val3 = float(p_str[3])
    else:
        rm.logMessage(3,"Step2a Top Ant Error, No Pulses")   
    
    #Step 2 Power Level,Top Antenna   
    line = "Step2a - Top Ant: " + str(val3)
    rm.logMessage(2,line)  
    Power_TopAntenna2 = (val3)    #dBm --see Notes

    time.sleep(1)
    #Get Reply Delay 
    Reply_DelayTop2 = atc.getReplyDelay(2)
    time.sleep(1)
    rm.logMessage(0,("Top Reply 2 Delay Initial: " + str(Reply_DelayTop2)))
    time.sleep(1)
    # fix for erroneous reply rate,try 10 times
    count = 0
    while (Reply_DelayTop2 == -1.0 or Reply_DelayTop2 == 20) and count < 10:
        Reply_DelayTop2 = atc.getReplyDelay(2)
        time.sleep(0.3)
        count = count + 1       
    rm.logMessage(0,("Top Reply 2 Delay Final: " + str(Reply_DelayTop2)))
 
    #Make Power Measurements on Bottom Antenna
    init_RFBOB_Bottom(rfbob) 
    
    #initialize powermeter
    pw_init(pwrmeter)
    
    #Step2:Measure Pulse Power -- Mode S, Bottom Antenna 
    val4 = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter,Diversity,Bot Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val4 = float(p_str[3])
    else:
        rm.logMessage(3,"Step2b Bot Ant Error, No Pulses")   
    
    #Step 2 Power Level,Bot Antenna
    line = "Step2b - Bot Ant: " + str(val4)
    rm.logMessage(2,line)     
    Power_BotAntenna2 = (val4)    #dBm --see Notes

    #Get Reply Delay 
    time.sleep(1)
    Reply_DelayBot2 = atc.getReplyDelay(2)
    rm.logMessage(0,("Bottom Reply Delay 2 Initial: " + str(Reply_DelayBot2)))
    time.sleep(1)
    # fix for erroneous reply rate,try 10 times
    count = 0
    while (Reply_DelayBot2 == -1.0 or Reply_DelayBot2 == 20) and count < 10:
        Reply_DelayBot2 = atc.getReplyDelay(2)
        time.sleep(1)
        count = count + 1       
    
    rm.logMessage(0,("Bottom Reply Delay Final: " + str(Reply_DelayBot2)))
    time.sleep(1)
           
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    time.sleep(2)

    
    #Restore Transmitters
    atc.gwrite(":ATC:SET:GENA:SIG ON") # TOP Generators
    atc.gwrite(":ATC:SET:GENB:SIG ON") # TOP Generators
    atc.gwrite(":ATC:SET:GENC:SIG ON") # BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:SIG ON") # BOTTOM Generators
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna

    #Adjust Power Levels for PathLoss
    Power_TopAntenna1 = Power_TopAntenna1 # + PathLossTop
    Power_BotAntenna1 = Power_BotAntenna1 # + PathLossTop
    Power_TopAntenna2 = Power_TopAntenna2 # + PathLossBot
    Power_BotAntenna2 = Power_BotAntenna2 # + PathLossBot

    rm.logMessage(0,"Power's: %f,%f,%f,%f" % (Power_TopAntenna1,Power_BotAntenna1,Power_TopAntenna2,Power_BotAntenna2))   
    rm.logMessage(0,"ReplyDelay's: %f,%f,%f,%f" % (Reply_DelayTop1,Reply_DelayBot1,Reply_DelayTop2,Reply_DelayBot2))   
    rm.logMessage(2,"Done, closing session")
    

    #Return results back to teststand.
    return [Power_TopAntenna1,Power_BotAntenna1,Reply_DelayTop1,Reply_DelayBot1,
            Power_TopAntenna2,Power_BotAntenna2,Reply_DelayTop2,Reply_DelayBot2]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    pwr_obj = B4500CPwrMeter(rm)
    pwr_obj.Reset()
    
    res = Test_2_3_2_10_Step1a(rm,atc_obj,pwr_obj,rf_obj,52.75,52.69)
    
    pwr_obj.close()
    atc_obj.close()
    rf_obj.disconnect()

