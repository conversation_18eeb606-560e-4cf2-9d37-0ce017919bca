#!/usr/bin/env python3
"""
Unit Tests for FAR43 Procedures
Tests transponder performance procedures and HIGH PRIORITY RF stabilization optimizations
"""

import unittest
import time
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from tests.mocks.mock_atc5000ng import MockATC5000NG
from tests.mocks.mock_spectrum_analyzer import MockSpectrumAnalyzer
from tests.mocks.mock_power_meter import MockPowerMeter
from tests.mocks.mock_signal_generator import MockSignalGenerator
from tests.mocks.mock_resource_manager import MockResourceManager


class TestFAR43Procedures(unittest.TestCase):
    """Test cases for FAR43 transponder performance procedures"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_rm = MockResourceManager()
        self.mock_atc = MockATC5000NG(self.mock_rm, "TCPIP0::*************::2001::SOCKET")
        self.mock_spec_an = MockSpectrumAnalyzer(self.mock_rm, "TCPIP0::************::INSTR")
        self.mock_power_meter = MockPowerMeter(self.mock_rm, "TCPIP0::************::INSTR")
        self.mock_siggen = MockSignalGenerator(self.mock_rm, "TCPIP0::************::INSTR")
        
    def tearDown(self):
        """Clean up after tests"""
        # Clean up any resources
        pass
        
    def test_far43_a_frequency_optimization(self):
        """Test FAR43_A_Frequency procedure with HIGH PRIORITY RF stabilization optimization"""
        atc = self.mock_atc
        
        # Test optimized RF stabilization timing
        start_time = time.time()
        
        # Simulate the optimized procedure execution
        # Original delays: 25s + 25s = 50s total
        # Optimized delays: 15s + 15s = 30s total
        
        # First RF stabilization (optimized)
        atc.set_frequency(1030e6)
        atc.wait_for_rf_stabilization()  # Should use 15s instead of 25s
        time.sleep(0.15)  # Scaled 15s delay for testing
        
        # Second RF stabilization (optimized)
        atc.set_frequency(1090e6)
        atc.wait_for_rf_stabilization()  # Should use 15s instead of 25s
        time.sleep(0.15)  # Scaled 15s delay for testing
        
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 50s scaled = 0.5s, Optimized: 30s scaled = 0.3s
        actual_time = end_time - start_time
        self.assertLess(actual_time, 0.4)  # Should be closer to optimized timing
        
    def test_far43_frequency_accuracy(self):
        """Test FAR43 frequency accuracy requirements"""
        spec_an = self.mock_spec_an
        atc = self.mock_atc
        
        # Configure for frequency measurement
        atc.set_test_mode('TRANSPONDER')
        atc.set_frequency(1030e6)  # Mode S frequency
        
        # Configure spectrum analyzer
        spec_an.set_center_frequency(1030e6)
        spec_an.set_span(1e6)  # 1 MHz span
        spec_an.set_resolution_bandwidth(1000)  # 1 kHz RBW
        
        # Simulate transponder transmission
        spec_an.simulate_transponder_signal(frequency=1030e6, power=-10.0)
        
        # Measure frequency
        measured_freq = spec_an.measure_peak_frequency()
        
        # Validate frequency accuracy per FAR 43 (±0.01%)
        expected_freq = 1030e6
        freq_error = abs(measured_freq - expected_freq) / expected_freq
        self.assertLess(freq_error, 0.0001)  # 0.01% tolerance
        
    def test_far43_b_suppression(self):
        """Test FAR43_B_Suppression procedure"""
        atc = self.mock_atc
        power_meter = self.mock_power_meter
        
        # Configure for suppression test
        atc.set_test_mode('TRANSPONDER')
        atc.set_suppression_mode(True)
        
        # Configure power meter
        power_meter.set_frequency(1030e6)
        power_meter.set_measurement_mode('PEAK')
        
        # Test suppression levels
        suppression_levels = [-20, -30, -40, -50]  # dBm
        
        for level in suppression_levels:
            atc.set_suppression_level(level)
            time.sleep(0.01)  # Brief settling time
            
            # Measure suppressed power
            measured_power = power_meter.measure_power()
            
            # Validate suppression effectiveness
            self.assertLess(measured_power, level + 3.0)  # Allow 3dB margin
            
    def test_far43_c_sensitivity(self):
        """Test FAR43_C_Sensitivity procedure"""
        atc = self.mock_atc
        siggen = self.mock_siggen
        
        # Configure for sensitivity test
        atc.set_test_mode('TRANSPONDER')
        siggen.set_frequency(1030e6)
        siggen.set_rf_output(True)
        
        # Test sensitivity threshold
        power_levels = [-90, -85, -80, -75, -70]  # dBm
        responses = []
        
        for power in power_levels:
            siggen.set_power(power)
            time.sleep(0.01)  # Brief settling
            
            # Check for transponder response
            response = atc.check_transponder_response()
            responses.append((power, response))
            
        # Find sensitivity threshold
        threshold_power = None
        for power, response in responses:
            if response:
                threshold_power = power
                break
                
        # Validate sensitivity meets requirements
        self.assertIsNotNone(threshold_power)
        self.assertLess(threshold_power, -74.0)  # Better than -74 dBm per FAR 43
        
    def test_far43_d_power(self):
        """Test FAR43_D_Power procedure"""
        atc = self.mock_atc
        power_meter = self.mock_power_meter
        
        # Configure for power measurement
        atc.set_test_mode('TRANSPONDER')
        power_meter.set_frequency(1030e6)
        power_meter.set_measurement_mode('AVERAGE')
        
        # Trigger transponder transmission
        atc.trigger_transmission()
        time.sleep(0.01)  # Brief measurement time
        
        # Measure transmit power
        measured_power = power_meter.measure_power()
        
        # Validate power levels per FAR 43
        # Mode S: 18.5 to 27 dBm
        self.assertGreaterEqual(measured_power, 18.5)
        self.assertLessEqual(measured_power, 27.0)
        
    def test_far43_e_diversity(self):
        """Test FAR43_E_Diversity procedure"""
        atc = self.mock_atc
        power_meter = self.mock_power_meter
        
        # Configure for diversity test
        atc.set_test_mode('TRANSPONDER')
        atc.enable_diversity(True)
        
        # Test both antenna ports
        antenna_powers = {}
        
        for antenna in ['TOP', 'BOTTOM']:
            atc.select_antenna(antenna)
            atc.trigger_transmission()
            time.sleep(0.01)  # Brief measurement
            
            power = power_meter.measure_power()
            antenna_powers[antenna] = power
            
        # Validate diversity operation
        self.assertIn('TOP', antenna_powers)
        self.assertIn('BOTTOM', antenna_powers)
        
        # Both antennas should have reasonable power levels
        for antenna, power in antenna_powers.items():
            self.assertGreater(power, 15.0)  # Minimum power
            self.assertLess(power, 30.0)     # Maximum power
            
    def test_far43_f_modes_address(self):
        """Test FAR43_F_ModeSAddress procedure"""
        atc = self.mock_atc
        
        # Configure for Mode S address test
        atc.set_test_mode('MODE_S')
        
        # Test various Mode S addresses
        test_addresses = [0x123456, 0xABCDEF, 0x000001, 0xFFFFFE]
        
        for address in test_addresses:
            atc.set_mode_s_address(address)
            
            # Interrogate with the address
            response = atc.interrogate_mode_s(address)
            
            # Should respond to correct address
            self.assertTrue(response['valid'])
            self.assertEqual(response['address'], address)
            
        # Test invalid address (should not respond)
        invalid_response = atc.interrogate_mode_s(0x999999)
        self.assertFalse(invalid_response['valid'])
        
    def test_far43_g_modes_format(self):
        """Test FAR43_G_ModeSFormat procedure"""
        atc = self.mock_atc
        
        # Configure for Mode S format test
        atc.set_test_mode('MODE_S')
        atc.set_mode_s_address(0x123456)
        
        # Test different Mode S formats
        formats = ['DF4', 'DF5', 'DF20', 'DF21']
        
        for format_type in formats:
            response = atc.interrogate_mode_s_format(format_type)
            
            # Validate response format
            self.assertTrue(response['valid'])
            self.assertEqual(response['format'], format_type)
            self.assertIn('data', response)
            
    def test_far43_h_modes_allcall(self):
        """Test FAR43_H_ModeSAllCall procedure"""
        atc = self.mock_atc
        
        # Configure for Mode S all-call test
        atc.set_test_mode('MODE_S')
        atc.set_mode_s_address(0x123456)
        
        # Send all-call interrogation
        response = atc.send_mode_s_allcall()
        
        # Should respond to all-call
        self.assertTrue(response['valid'])
        self.assertEqual(response['capability'], 'MODE_S')
        self.assertIn('address', response)
        
    def test_far43_i_atcrbs_only(self):
        """Test FAR43_I_ATCRBSOnly procedure"""
        atc = self.mock_atc
        
        # Configure for ATCRBS-only test
        atc.set_test_mode('ATCRBS')
        atc.disable_mode_s()
        
        # Test ATCRBS modes
        modes = [1, 2, 3, 'A', 'C']
        
        for mode in modes:
            response = atc.interrogate_atcrbs(mode)
            
            # Should respond to ATCRBS interrogations
            self.assertTrue(response['valid'])
            self.assertEqual(response['mode'], mode)
            
        # Should not respond to Mode S
        mode_s_response = atc.interrogate_mode_s(0x123456)
        self.assertFalse(mode_s_response['valid'])
        
    def test_far43_j_squitter(self):
        """Test FAR43_J_Squitter procedure"""
        atc = self.mock_atc
        spec_an = self.mock_spec_an
        
        # Configure for squitter test
        atc.set_test_mode('MODE_S')
        atc.enable_squitter(True)
        
        # Configure spectrum analyzer for squitter detection
        spec_an.set_center_frequency(1090e6)
        spec_an.set_span(1e6)
        spec_an.set_measurement_mode('SPECTROGRAM')
        
        # Monitor for squitter transmissions
        time.sleep(0.1)  # Monitor period
        squitter_data = spec_an.detect_squitter_transmissions()
        
        # Validate squitter operation
        self.assertGreater(len(squitter_data), 0)  # Should detect squitters
        
        for squitter in squitter_data:
            self.assertAlmostEqual(squitter['frequency'], 1090e6, delta=1000)
            self.assertGreater(squitter['power'], -50.0)  # Reasonable power level
            
    def test_rf_stabilization_optimization_validation(self):
        """Test that RF stabilization optimizations work correctly"""
        atc = self.mock_atc
        
        # Test optimized RF stabilization timing
        start_time = time.time()
        
        # Perform multiple RF stabilization operations
        frequencies = [1030e6, 1090e6, 1025e6, 978e6]
        
        for freq in frequencies:
            atc.set_frequency(freq)
            atc.wait_for_rf_stabilization()  # Should use 15s instead of 25s
            time.sleep(0.015)  # Scaled timing for test
            
        end_time = time.time()
        
        # Should complete faster than original timing
        # Original: 4 × 25s = 100s scaled = 1.0s
        # Optimized: 4 × 15s = 60s scaled = 0.6s
        actual_time = end_time - start_time
        self.assertLess(actual_time, 0.8)  # Should be closer to optimized timing
        
    def test_measurement_accuracy_with_optimization(self):
        """Test that optimizations don't affect measurement accuracy"""
        atc = self.mock_atc
        spec_an = self.mock_spec_an
        
        # Test frequency accuracy with optimized stabilization
        atc.set_frequency(1030e6)
        atc.wait_for_rf_stabilization()  # Optimized timing
        
        # Measure frequency
        spec_an.set_center_frequency(1030e6)
        measured_freq = spec_an.measure_peak_frequency()
        
        # Should still meet accuracy requirements
        freq_error = abs(measured_freq - 1030e6) / 1030e6
        self.assertLess(freq_error, 0.0001)  # 0.01% accuracy maintained
        
    def test_error_handling_with_optimizations(self):
        """Test error handling works correctly with optimizations"""
        atc = self.mock_atc
        
        # Test error handling with optimized timing
        atc.simulate_rf_error()
        
        # Should handle errors gracefully even with faster timing
        with self.assertRaises(Exception):
            atc.set_frequency(1030e6)
            
        # Recovery should work
        atc.clear_rf_error()
        result = atc.set_frequency(1030e6)
        self.assertTrue(result)


if __name__ == '__main__':
    unittest.main()
