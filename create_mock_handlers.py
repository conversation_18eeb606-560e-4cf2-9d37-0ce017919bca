#!/usr/bin/env python3
"""
Script to create comprehensive mock handlers for all TXDLib handlers
"""

import os
import glob
from pathlib import Path

# List of Python handler files to mock
HANDLER_FILES = [
    "AC_PowerSupply.py",
    "ARINC_Client.py", 
    "ATC_RGS_Log_Decode.py",
    "B4500CPwrMeter.py",
    "Compression.py",
    "D3054Scope.py",
    "Decode_Common.py",
    "DigitalBOB.py",
    "HBIT.py",
    "MSO56.py",
    "N5172BSigGen.py",
    "N6700DCPS.py",
    "NI5110Scope.py",
    "NI6363MultiIO.py",
    "NI6528Discretes.py",
    "NIDMM.py",
    "OmniBusBox_Client.py",
    "Pickering.py",
    "RFBOB.py",
    "TCAS_Display.py",
    "TCAS_Display_Stop.py",
    "Test_ARINC_Client.py",
    "UUTReg.py",
    "audio_processing.py",
    "main.py",
    "pilpxi.py",
    "powersupply.py"
]

def create_mock_handler(filename):
    """Create a mock handler file"""
    base_name = filename.replace('.py', '')
    
    mock_content = f'''"""
Mock implementation of {filename}
Provides mock functionality without external dependencies
"""

import time
import random
from typing import Any, List, Dict, Optional, Union


class Mock{base_name}:
    """Mock implementation of {base_name}"""
    
    def __init__(self, *args, **kwargs):
        """Mock initialization"""
        self.connected = True
        self.initialized = True
        
        # Handle common initialization patterns
        if args and hasattr(args[0], 'logMessage'):
            self.resourceManager = args[0]
            self.resourceManager.logMessage(1, f"Mock {base_name} initialized successfully")
        else:
            print(f"Mock {base_name} initialized successfully")
    
    def __getattr__(self, name):
        """Mock any missing methods dynamically"""
        def mock_method(*args, **kwargs):
            """Dynamic mock method"""
            time.sleep(0.001)  # Minimal realistic delay
            
            # Handle common method patterns
            if name.lower().startswith(('get', 'read', 'query', 'measure')):
                # Return realistic mock data for getters/queries
                if 'freq' in name.lower():
                    return 1030.0 + random.uniform(-1.0, 1.0)
                elif 'power' in name.lower() or 'pwr' in name.lower():
                    return -20.0 + random.uniform(-10.0, 10.0)
                elif 'voltage' in name.lower() or 'volt' in name.lower():
                    return 5.0 + random.uniform(-0.5, 0.5)
                elif 'current' in name.lower():
                    return 0.1 + random.uniform(-0.05, 0.05)
                elif 'temp' in name.lower():
                    return 25.0 + random.uniform(-5.0, 5.0)
                elif 'status' in name.lower():
                    return "OK" if random.random() > 0.1 else "BUSY"
                elif 'id' in name.lower():
                    return f"Mock {base_name},Model123,SN456789,FW1.0"
                elif 'time' in name.lower():
                    return 0.1 + random.uniform(-0.01, 0.01)
                elif 'width' in name.lower():
                    return 0.45 + random.uniform(-0.05, 0.05)
                elif 'delay' in name.lower():
                    return 3.0 + random.uniform(-0.2, 0.2)
                else:
                    return "OK"
            elif name.lower().startswith(('set', 'write', 'send', 'config', 'init')):
                # Setters/writers return success
                return True
            elif name.lower() in ['close', 'cleanup', 'disconnect', 'stop']:
                # Cleanup methods
                self.connected = False
                return True
            elif name.lower() in ['reset', 'restart']:
                # Reset methods with optimized timing
                time.sleep(0.5)  # Reduced reset time
                return True
            else:
                # Default return for unknown methods
                return True
                
        return mock_method

# Create class alias for backward compatibility
{base_name} = Mock{base_name}

# Additional common patterns for specific handlers
'''

    # Add specific patterns for certain handlers
    if 'PowerSupply' in filename or 'DCPS' in filename:
        mock_content += '''
    def set_voltage(self, voltage: float):
        """Mock voltage setting"""
        time.sleep(0.001)
        return True
        
    def set_current(self, current: float):
        """Mock current setting"""
        time.sleep(0.001)
        return True
        
    def get_voltage(self) -> float:
        """Mock voltage reading"""
        return 5.0 + random.uniform(-0.1, 0.1)
        
    def get_current(self) -> float:
        """Mock current reading"""
        return 0.1 + random.uniform(-0.01, 0.01)
'''

    elif 'SigGen' in filename or 'Signal' in filename:
        mock_content += '''
    def set_frequency(self, freq: float):
        """Mock frequency setting"""
        time.sleep(0.001)
        return True
        
    def set_power(self, power: float):
        """Mock power setting"""
        time.sleep(0.001)
        return True
        
    def get_frequency(self) -> float:
        """Mock frequency reading"""
        return 1000.0 + random.uniform(-10.0, 10.0)
'''

    elif 'Scope' in filename:
        mock_content += '''
    def set_timebase(self, timebase: float):
        """Mock timebase setting"""
        time.sleep(0.001)
        return True
        
    def get_waveform(self) -> List[float]:
        """Mock waveform data"""
        return [random.uniform(-1.0, 1.0) for _ in range(1000)]
'''

    elif 'DMM' in filename:
        mock_content += '''
    def measure_voltage(self) -> float:
        """Mock voltage measurement"""
        time.sleep(0.01)
        return 5.0 + random.uniform(-0.1, 0.1)
        
    def measure_current(self) -> float:
        """Mock current measurement"""
        time.sleep(0.01)
        return 0.1 + random.uniform(-0.01, 0.01)
'''

    return mock_content

def main():
    """Create all mock handler files"""
    mock_dir = Path("MockHandlers")
    mock_dir.mkdir(exist_ok=True)
    
    print("Creating mock handlers...")
    
    for handler_file in HANDLER_FILES:
        mock_file_path = mock_dir / handler_file
        
        if not mock_file_path.exists():
            print(f"Creating mock for {handler_file}")
            mock_content = create_mock_handler(handler_file)
            
            with open(mock_file_path, 'w') as f:
                f.write(mock_content)
        else:
            print(f"Mock for {handler_file} already exists, skipping")
    
    print(f"Created mock handlers in {mock_dir}")
    print("Mock handler creation complete!")

if __name__ == "__main__":
    main()
