# -*- coding: utf-8 -*-
"""
Created on Tues March 3 3:20:30 2020

@author: E589493
         K<PERSON> <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Interrogator Frequency Stability, Section 2.2.6
             
             "The center frequency of the pulse spectrum shall be
             within +/- 100 kHz of the assigned channel frequency."
             
INPUTS:      Top_Cable_Loss, ATC, ARINC Server, Spectrum Analyzer
OUTPUTS:     Center Frequency: feq1 Float

HISTORY:

0
3/03/2020   KF    Initial Release.
06/22/2020   AS    Added tvl statements, Added ARINC
03/10/2021   MRS   Updates for new handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers import N9010BSpecAn
       

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def init_DME_Standard(cable_loss, atc):
    """ Sets DME Standard COnditions to DME Channel 56X at -70dBm adn 34nm, Squidder rate 
    of 2700 """
    time.sleep(5)
    atc.DMEMode()
    atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))

def atc_power_level(Step_PowerLevel, atc):
    """ This function set teh next power level to be measured eg -50 dBm """
    cmd = ':ATC:DME:POWER ' + str(Step_PowerLevel)
    atc.write(cmd)

def set_VOR_PAIR0(atc, ARINC):
    """ This FUnction tunes the DME and Sets up the ATC for 0VOR Mode"""
    #Turn Off RF
    atc.gwrite(':ATC:DME:STOP')
    #Tune DME
    ARINC.writeChannel(111.9)
    #Setup ATC
    time.sleep(5)
    atc.gwrite(":ATC:DME:CHANNEL:MODE 0VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 111.9")
    time.sleep(3)
    atc.gwrite(':ATC:DME:START')
    time.sleep(3)

def set_VOR_PAIR5(atc, ARINC):
    """ This FUnction tunes the DME and Sets up the ATC for 5VOR Mode """
    #Turn Off RF
    atc.gwrite(':ATC:DME:STOP')
    #Tune DME
    ARINC.writeChannel(117.95)
    #Setup ATC
    time.sleep(5)
    atc.gwrite(":ATC:DME:CHANNEL:MODE 5VOR")
    time.sleep(1)
    atc.gwrite(":ATC:DME:CHANNEL 117.9")
    time.sleep(3)
    atc.gwrite(':ATC:DME:START')
    time.sleep(3)

def int226SpecAn(rm,specAN):
    """ This Function initializes the Spectrum analyzer for DO189 2.2.6 """
    rm.logMessage(0,"*Test_189_2_2_6 - Initializing  up Spectrum Analyzer ") 

    specAN.Reset()
    time.sleep(5)
    specAN.CenterFreqSet(1080, 'MHz')
    time.sleep(1)
    specAN.ResBandwidthSet(10, 'kHz')
    time.sleep(1)
    specAN.VidBandwidthSet(100, 'kHz')
    time.sleep(1)
    specAN.SweepTimeSet(10, 's')
    time.sleep(1)
    specAN.SpanSet(200, 'kHz')
    time.sleep(1)
    specAN.SweepContSet('ON')
    time.sleep(1)
    specAN.setRefLevel(10, 'dBm')
    time.sleep(3)

def measure_Pair1(rm,specAN):
    """ This Function measures the VOR pair 1 interrogator frequency """
    rm.logMessage(0,"*Test_189_2_2_6 - Measuring VOR Pair 1 Interrogator Frequency ") 

    specAN.CenterFreqSet(1080, 'MHz')
    time.sleep(1)
    specAN.ResBandwidthSet(10, 'kHz')
    time.sleep(1)
    specAN.VidBandwidthSet(100, 'kHz')
    time.sleep(1)
    specAN.SweepTimeSet(10, 's')
    time.sleep(1)
    specAN.SpanSet(500, 'kHz')
    specAN.TraceTypeSet('MAXHold')
    time.sleep(30)
    freq1 = specAN.GetMaxPeakFreq()
    power = specAN.GetMaxPeakPower()
    if float(power) > -30:
        rm.logMessage(0,"*Test_189_2_2_6 - VOR Pair 1 Interrogator Frequency: " + str(freq1)) 
        return float(freq1)
    else:
        rm.logMessage(3,"*Test_189_2_2_6 - VOR Pair 1 Interrogator Frequency: 0") 
        return 0

def measure_Pair2(rm,specAN):
    """ This Function measures the VOR pair 2 interrogator frequency """
    rm.logMessage(0,"*Test_189_2_2_6 -  Measuring VOR Pair 2 Interrogator Frequency ") 

    specAN.CenterFreqSet(1150, 'MHz')
    time.sleep(1)
    specAN.ResBandwidthSet(10, 'kHz')
    time.sleep(1)
    specAN.VidBandwidthSet(100, 'kHz')
    time.sleep(1)
    specAN.SweepTimeSet(10, 's')
    time.sleep(1)
    specAN.SpanSet(500, 'kHz')
    time.sleep(1)
    specAN.TraceTypeSet('MAXHold')
    time.sleep(30)
    freq1 = specAN.GetMaxPeakFreq()
    power = specAN.GetMaxPeakPower()
    if float(power) > -30:
        rm.logMessage(0,"*Test_189_2_2_6 - VOR Pair 2 Interrogator Frequency: " + str(freq1))
        return float(freq1)
    else:
        rm.logMessage(0,"*Test_189_2_2_6 - VOR Pair 2 Interrogator Frequency: 0") 
        return 0

##############################################################################
################# MAIN     ##################################################
##############################################################################
def main():

    #Initialize Intruuments
    rm = ate_rm()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
    time.sleep(5)

    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    
    atc.DMEMode()

    #Initialize Spectrum Analyzer
    specAN = N9010BSpecAn(rm)
    specAN.Reset()
    int226SpecAn(rm,specAN)
    time.sleep(3)

    #Measure Channel X
    set_VOR_PAIR0(atc, ARINC)
    print(measure_Pair1(rm,specAN))
    time.sleep(.1)

    #Measure Channel Y 
    set_VOR_PAIR5(atc, ARINC)
    print(measure_Pair2(rm,specAN))
    time.sleep(.1)


    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    specAN.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()


if __name__ == "__main__":
    main()